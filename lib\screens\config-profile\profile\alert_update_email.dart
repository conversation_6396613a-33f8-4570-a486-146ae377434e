import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/config-profile/config_profile_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:cooperado_minha_unimed/shared/vo/profile/profile-payload.vo.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:cooperado_minha_unimed/shared/widgets/formatter_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class AlertUpdateEmail extends StatefulWidget {
  final Enderecos? address;
  final Contatos? contato;
  final Function? onPressed;
  const AlertUpdateEmail(
      {super.key, this.contato, this.onPressed, this.address});
  @override
  AlertUState createState() => AlertUState();
}

class AlertUState extends State<AlertUpdateEmail> {
  TextEditingController controller = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool? disableFields;
  bool isEmailValid = false;
  FocusNode? focusTextFieldSearch;

  @override
  void initState() {
    disableFields = false;
    controller.text = widget.contato!.numeroContato!.replaceAll(" ", "");
    // foneFormatter.maskText(controller.text);
    context.read<ConfigProfileCubit>().setInitial();
    isEmailValid = StringUtils.validateEmail(widget.contato!.numeroContato!);
    focusTextFieldSearch = FocusNode();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
      title: const Text('Atualizar Email'),
      content: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: TextFormField(
                autofocus: true,
                focusNode: focusTextFieldSearch,
                controller: controller,
                keyboardType: TextInputType.emailAddress,
                style: const TextStyle(color: CooperadoColors.tealGreen),
                decoration: InputDecoration(
                    enabledBorder: _borderInput(),
                    focusedBorder: _borderInput(),
                    errorBorder: _borderInput(),
                    focusedErrorBorder: _borderInput(),
                    labelText: 'Email',
                    labelStyle:
                        const TextStyle(color: CooperadoColors.tealGreen)),
                inputFormatters: [...FormatterFild.inputEmailFormatter],
                onChanged: (value) {
                  setState(
                      () => isEmailValid = StringUtils.validateEmail(value));
                },
              ),
            ),
            _indicatorStateUpadeContact(),
          ],
        ),
      ),
      actions: [
        _buttonCancelar(),
        _buttonConfirmar(),
      ],
    );
  }

  Widget _buttonCancelar() {
    return BlocBuilder<ConfigProfileCubit, ConfigProfileState>(
      builder: (context, state) {
        if (state is UpdatingProfileState) {
          return ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: CooperadoColors.grayLight,
              ),
              onPressed: null,
              child: const Text('Cancelar'));
        } else {
          return ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: CooperadoColors.grayLight2,
            ),
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          );
        }
      },
    );
  }

  Widget _buttonConfirmar() {
    return BlocBuilder<ConfigProfileCubit, ConfigProfileState>(
      builder: (context, state) {
        if (state is UpdatingProfileState || !isEmailValid) {
          return ElevatedButton(
              style: ElevatedButton.styleFrom(
                  backgroundColor: CooperadoColors.tealGreenSecondary),
              onPressed: null,
              child: const Text('Confirmar'));
        } else {
          return ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: CooperadoColors.tealGreenSecondary,
            ),
            onPressed: _updateEmail,
            child: const Text('Confirmar'),
          );
        }
      },
    );
  }

  _updateEmail() {
    if (controller.text.isEmpty) return;
    final newContato = Contatos.fromJson(widget.contato!.toJson());
    final crm = context.read<AuthCubit>().credentials.crm;
    newContato.numeroContato = controller.text;
    context.read<ConfigProfileCubit>().updateContato(
        crm: crm, endereco: widget.address, newContato: newContato);
  }

  Widget _indicatorStateUpadeContact() {
    return BlocConsumer<ConfigProfileCubit, ConfigProfileState>(
      listener: (context, state) {
        if (state is UpdatedProfileState) {
          Alert.open(context,
              title: 'Sucesso',
              text: 'Email atualizado com sucesso', callbackClose: () {
            Navigator.pop(context);
            context.read<ConfigProfileCubit>().setInitial();
          });
        }
      },
      builder: (context, state) {
        if (state is UpdatingProfileState) {
          return const SpinKitThreeBounce(
              color: CooperadoColors.tealGreen, size: 30);
        } else if (state is ErrorUpdateProfileState) {
          return Text(state.message,
              style: const TextStyle(color: Colors.red, fontSize: 12));
        } else {
          return Container();
        }
      },
    );
  }

  InputBorder _borderInput() {
    return const OutlineInputBorder(
      borderSide: BorderSide(color: CooperadoColors.grayLight),
    );
  }
}

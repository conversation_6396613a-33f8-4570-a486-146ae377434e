class UnimedException {
  final String? message;

  UnimedException(this.message);

  @override
  String toString() {
    return message ?? '';
  }
}

class NotFoundDataException extends UnimedException {
  NotFoundDataException() : super('Dados não encontrados no momento.');
}

class NotFoundException extends UnimedException {
  NotFoundException()
      : super('Serviço não encontrado no momento. Tente novamente mais tarde');
}

class AuthException extends UnimedException {
  AuthException(super.message);
}

class AuthInvalidException extends UnimedException {
  AuthInvalidException(super.message);
}

class TransparenciaException extends UnimedException {
  TransparenciaException(super.message);
}

class DiretoriaException extends UnimedException {
  DiretoriaException(super.message);
}

class IndicadoresException extends UnimedException {
  IndicadoresException(super.message);
}

class ServicesException extends UnimedException {
  ServicesException(super.message);
}

class FiscalCouncilException extends UnimedException {
  FiscalCouncilException(super.message);
}

class NoticeException extends UnimedException {
  NoticeException(String super.message);
}

class PdfException extends UnimedException {
  PdfException(super.message);
}

class ScoreComparisonException extends UnimedException {
  ScoreComparisonException(super.message);
}

class ScheduleException extends UnimedException {
  ScheduleException(super.message);
}

class EconomicIndicatorsException extends UnimedException {
  EconomicIndicatorsException(super.message);
}

class ChannelEthicsException extends UnimedException {
  ChannelEthicsException(super.message);
}

class GraphQlException extends UnimedException {
  GraphQlException(super.message);
}

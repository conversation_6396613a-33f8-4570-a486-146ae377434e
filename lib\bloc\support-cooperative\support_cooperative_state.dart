part of 'support_cooperative_cubit.dart';

abstract class SupportCooperativeState extends Equatable {
  const SupportCooperativeState();

  @override
  List<Object> get props => [];
}

class InitialSupportCooperativeState extends SupportCooperativeState {}

class LoadingSupportCooperativeState extends SupportCooperativeState {
  @override
  List<Object> get props => [];
}

class ErrorSupportCooperativeState extends SupportCooperativeState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorSupportCooperativeState(this.message);
}

class LoadedSupportCooperativeState extends SupportCooperativeState {
  final String content;

  @override
  List<Object> get props => [content];

  const LoadedSupportCooperativeState({required this.content});
}

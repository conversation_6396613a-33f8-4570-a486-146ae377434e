import 'package:intl/intl.dart';

class ResImageExamResultModel {
  late String descriptionExam;
  late String codOrder;
  late String cpf;
  late String codPatient;
  late String namePatient;
  late String crm;
  late String nameProvider;
  late String solicitationDate;
  late String resultDate;
  List<ImageLinks>? imageLinks;

  String get solicitationDateFormatted {
    return DateFormat('dd/MM/yyyy HH:mm')
        .format(DateTime.parse(solicitationDate));
  }

  ResImageExamResultModel(
      {required this.descriptionExam,
      required this.codOrder,
      required this.cpf,
      required this.codPatient,
      required this.namePatient,
      required this.crm,
      required this.nameProvider,
      required this.solicitationDate,
      required this.resultDate,
      required this.imageLinks});

  ResImageExamResultModel.fromJson(Map<String, dynamic> json) {
    descriptionExam = json['descriptionExam'];
    codOrder = json['codOrder'];
    cpf = json['cpf'];
    codPatient = json['codPatient'];
    namePatient = json['namePatient'];
    crm = json['crm'];
    nameProvider = json['nameProvider'];
    solicitationDate = json['solicitationDate'];
    resultDate = json['resultDate'];
    if (json['imageLinks'] != null) {
      imageLinks = <ImageLinks>[];
      json['imageLinks'].forEach((v) {
        imageLinks!.add(ImageLinks.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['descriptionExam'] = descriptionExam;
    data['codOrder'] = codOrder;
    data['cpf'] = cpf;
    data['codPatient'] = codPatient;
    data['namePatient'] = namePatient;
    data['crm'] = crm;
    data['nameProvider'] = nameProvider;
    data['solicitationDate'] = solicitationDate;
    data['resultDate'] = resultDate;
    data['imageLinks'] = imageLinks!.map((v) => v.toJson()).toList();
    return data;
  }
}

class ImageLinks {
  late String codOrder;
  late String codOrderItem;
  late String examName;
  late String link;

  ImageLinks(
      {required this.codOrder,
      required this.codOrderItem,
      required this.examName,
      required this.link});

  ImageLinks.fromJson(Map<String, dynamic> json) {
    codOrder = json['codOrder'];
    codOrderItem = json['codOrderItem'];
    examName = json['examName'];
    link = json['link'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codOrder'] = codOrder;
    data['codOrderItem'] = codOrderItem;
    data['examName'] = examName;
    data['link'] = link;
    return data;
  }
}

import 'package:flutter/material.dart';

class LetterAnimation extends StatelessWidget {
  final Widget banner;
  final Animation<double> controller;
  final Size screenSize;

  final Animation<double> opacity;
  final Animation<double> width;
  final Animation<double> height;
  final Animation<EdgeInsets> padding;

  LetterAnimation(
      {super.key,
      required this.banner,
      required this.controller,
      required this.screenSize})
      : opacity = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(
          CurvedAnimation(
            parent: controller,
            curve: const Interval(
              0.0,
              0.3,
              curve: Curves.ease,
            ),
          ),
        ),
        width = Tween<double>(
          begin: 0.0,
          end: screenSize.width,
        ).animate(
          CurvedAnimation(
            parent: controller,
            curve: const Interval(
              0.0,
              0.5,
              curve: Curves.ease,
            ),
          ),
        ),
        height = Tween<double>(begin: 0.0, end: screenSize.height).animate(
          CurvedAnimation(
            parent: controller,
            curve: const Interval(
              0.5,
              1.0,
              curve: Curves.ease,
            ),
          ),
        ),
        padding = EdgeInsetsTween(
          begin: const EdgeInsets.only(bottom: 16.0),
          end: const EdgeInsets.only(bottom: 0.0),
        ).animate(
          CurvedAnimation(
            parent: controller,
            curve: const Interval(
              0.5,
              1.0,
              curve: Curves.ease,
            ),
          ),
        );

  Widget _buildAnimation(BuildContext context, Widget? child) {
    return Container(
      padding: padding.value,
      alignment: Alignment.bottomCenter,
      child: Opacity(
        opacity: opacity.value,
        child: SizedBox(
          width: width.value,
          height: height.value,
          child: banner,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      builder: _buildAnimation,
      animation: controller,
    );
  }
}

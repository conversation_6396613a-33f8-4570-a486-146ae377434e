import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';

// criamos a classe apenas com os atributos utilizados para fins de gráficos no app
// json completo possui chaves identicas e não utilizadas pelo app

class ComparativeProductionModel {
  String? periodo;
  int? qtdPrestadores;
  double? valorEspecialidade;
  double? valorPrestador;
  double? mediaPrestadores;
  double? diferenca;

  String get valorEspecialidadeFormatted =>
      StringUtils.formatMoney(mediaPrestadores!);
  String get valorPrestadorFormatted =>
      StringUtils.formatMoney(valorPrestador!);

  ComparativeProductionModel(
      {this.periodo,
      this.qtdPrestadores,
      this.valorEspecialidade,
      this.valorPrestador,
      this.mediaPrestadores,
      this.diferenca});

  ComparativeProductionModel.fromJson(Map<String, dynamic> json) {
    periodo = json['periodo'];
    qtdPrestadores = json['qtdPrestadores'];
    valorEspecialidade = double.parse(json['valorEspecialidade'].toString());
    valorPrestador = double.parse(json['valorPrestador'].toString());
    mediaPrestadores = double.parse(json['mediaPrestadores'].toString());
    diferenca = double.parse(json['diferenca'].toString());
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['periodo'] = periodo;
    data['qtdPrestadores'] = qtdPrestadores;
    data['valorEspecialidade'] = valorEspecialidade;
    data['valorPrestador'] = valorPrestador;
    data['mediaPrestadores'] = mediaPrestadores;
    data['diferenca'] = diferenca;
    return data;
  }
}

class Prestador {
  int? codigo;
  int? codigoUnimed;
  String? nome;
  bool? suspensaoCovid;
  GrupoPrestador? grupoPrestador;
  String? crm;
  int? cpf;
  Especialidades? especialidadePrincipal;

  Prestador(
      {this.codigo,
      this.codigoUnimed,
      this.nome,
      this.suspensaoCovid,
      this.grupoPrestador,
      this.crm,
      this.cpf,
      this.especialidadePrincipal});

  Prestador.fromJson(Map<String, dynamic> json) {
    codigo = json['codigo'];
    codigoUnimed = json['codigoUnimed'];
    nome = json['nome'];
    suspensaoCovid = json['suspensaoCovid'];

    grupoPrestador = json['grupoPrestador'] != null
        ? GrupoPrestador.fromJson(json['grupoPrestador'])
        : null;
    crm = json['crm'];
    cpf = json['cpf'];
    especialidadePrincipal = json['especialidadePrincipal'] != null
        ? Especialidades.fromJson(json['especialidadePrincipal'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codigo'] = codigo;
    data['codigoUnimed'] = codigoUnimed;
    data['nome'] = nome;
    data['suspensaoCovid'] = suspensaoCovid;

    if (grupoPrestador != null) {
      data['grupoPrestador'] = grupoPrestador!.toJson();
    }
    data['crm'] = crm;
    data['cpf'] = cpf;
    if (especialidadePrincipal != null) {
      data['especialidadePrincipal'] = especialidadePrincipal!.toJson();
    }
    return data;
  }
}

class Especialidades {
  int? codigo;
  String? descricao;
  String? qualificacoes;
  String? qualificacao;
  String? especialidadePrincipal;

  Especialidades(
      {this.codigo,
      this.descricao,
      this.qualificacoes,
      this.qualificacao,
      this.especialidadePrincipal});

  Especialidades.fromJson(Map<String, dynamic> json) {
    codigo = json['codigo'];
    descricao = json['descricao'];
    qualificacoes = json['qualificacoes'];
    qualificacao = json['qualificacao'];
    especialidadePrincipal = json['especialidadePrincipal'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codigo'] = codigo;
    data['descricao'] = descricao;
    data['qualificacoes'] = qualificacoes;
    data['qualificacao'] = qualificacao;
    data['especialidadePrincipal'] = especialidadePrincipal;
    return data;
  }
}

class GrupoPrestador {
  String? codigo;
  String? descricao;

  GrupoPrestador({this.codigo, this.descricao});

  GrupoPrestador.fromJson(Map<String, dynamic> json) {
    codigo = json['codigo'];
    descricao = json['descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codigo'] = codigo;
    data['descricao'] = descricao;
    return data;
  }
}

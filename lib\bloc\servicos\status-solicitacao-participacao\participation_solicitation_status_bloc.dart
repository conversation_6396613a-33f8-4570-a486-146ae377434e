import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/shared/api/services.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/servicos/honorary_solicitation.vo.dart';
import 'package:flutter/material.dart';

import 'participation_solicitation_status_state.dart';

class SolicitationParticipationStatusCubit
    extends Cubit<SolicitationParticipationStatusState> {
  SolicitationParticipationStatusCubit()
      : super(SolicitationParticipationStatusInitial());

  List<HonorarySolicitation>? _solicitacoes;
  List<HonorarySolicitation>? _solicitacoesFull;

  List<HonorarySolicitation>? get solicitacoes => _solicitacoes;

  getSolicitationParticipationStatusEvent() async {
    emit(LoadingGetSolicitationParticipationStatusState());
    try {
      _solicitacoesFull =
          await Locator.instance!<ServicesApi>().getLastParticipationRequests();

      emit(DoneGetSolicitationParticipationStatusState(_solicitacoesFull));
    } catch (ex) {
      debugPrint(ex.toString());
      emit(ErrorGetSolicitationParticipationStatusState('$ex'));
    }
  }

  solicitationFilter(filter) async {
    emit(LoadingGetSolicitationParticipationStatusState());
    if (filter.isEmpty) {
      _solicitacoes = _solicitacoesFull;
    } else {
      final formattedFilterText = filter.trim().toUpperCase();

      // logger.d('Text to Search: => $formattedFilterText');

      _solicitacoes = _solicitacoesFull!.where((s) {
        return s.participacao!.descricao!.contains(formattedFilterText) ||
            s.numeroSolicitacao.toString().contains(formattedFilterText) ||
            s.numeroNota.toString().contains(formattedFilterText) ||
            s.situacao.toString().contains(formattedFilterText);
      }).toList();
    }

    emit(DoneGetSolicitationParticipationStatusState(_solicitacoes));
  }
}

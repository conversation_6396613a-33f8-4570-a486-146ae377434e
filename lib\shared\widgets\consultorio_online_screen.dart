import 'dart:async';
import 'dart:io';

import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:cooperado_minha_unimed/shared/widgets/page_orientation.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';

class ConsultorioWebViewScreen extends OrientationPage {
  final String title;
  final String url;
  final bool enableOrientation;
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const ConsultorioWebViewScreen({
    super.key,
    required this.title,
    required this.url,
    this.enableOrientation = false,
    required this.analytics,
    required this.observer,
  }) : super(activateOrientation: enableOrientation);

  @override
  ConsultorioWebViewScreenState createState() => ConsultorioWebViewScreenState();
}

class ConsultorioWebViewScreenState extends State<ConsultorioWebViewScreen> {
  late final WebViewController _controller;
  bool loadingPage = true;
  bool initialLoading = true;

  @override
  void initState() {
    super.initState();

    // Analytics
    widget.analytics.logScreenView(
      screenName: widget.title,
      screenClass: 'ConsultorioWebViewScreen',
    );

    // Orientação
    if (widget.activateOrientation) {
      SystemChrome.setPreferredOrientations(
        [
          DeviceOrientation.portraitDown,
          DeviceOrientation.portraitUp,
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.landscapeRight,
        ],
      );
    } else {
      SystemChrome.setPreferredOrientations(
        [DeviceOrientation.portraitUp],
      );
    }

    // Para Android, usar AndroidWebView
    if (Platform.isAndroid) {
      WebViewPlatform.instance = AndroidWebViewPlatform();
    }

    // Instancia o controlador
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..addJavaScriptChannel(
        'Toaster',
        onMessageReceived: (JavaScriptMessage msg) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(msg.message)),
          );
        },
      )
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (_) => setState(() => loadingPage = true),
          onPageFinished: (_) => setState(() => loadingPage = false),
          onNavigationRequest: (_) => NavigationDecision.navigate,
          onWebResourceError: (err) {
            setState(() => loadingPage = false);
            Alert.open(
              context,
              callbackClose: () => Navigator.pop(context),
              title: 'Alerta',
              text: 'Não foi possível carregar essa página.',
            );
          },
        ),
      );

    // Comportamento original de delay
    Future.delayed(const Duration(seconds: 3), () {
      _controller.loadRequest(Uri.parse(widget.url));
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() => initialLoading = false);
        }
      });
    });
  }

  @override
  void dispose() {
    // Restaura orientação padrão
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
      builder: (context, orientation) => Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          title: Text(widget.title),
          actions: <Widget>[
            NavigationControls(
              webViewController: _controller,
              orientation: orientation,
              hasOrientation: widget.activateOrientation,
              loading: initialLoading || loadingPage,
            ),
          ],
        ),
        body: SafeArea(
          child: Stack(
            children: [
              WebViewWidget(controller: _controller),
              if (loadingPage || initialLoading)
                Container(
                  color: const Color.fromRGBO(255, 255, 255, 0.95),
                  child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SpinKitCircle(color: CooperadoColors.tealGreen),
                      Text('Carregando Consultório Online...'),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class NavigationControls extends StatelessWidget {
  final WebViewController webViewController;
  final Orientation orientation;
  final bool hasOrientation;
  final bool loading;

  const NavigationControls({
    super.key,
    required this.webViewController,
    required this.orientation,
    required this.hasOrientation,
    required this.loading,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: loading
              ? null
              : () async {
                  if (await webViewController.canGoBack()) {
                    await webViewController.goBack();
                  } else if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        duration: Duration(milliseconds: 500),
                        content: Text('Nenhuma página anterior'),
                      ),
                    );
                  }
                },
        ),
        IconButton(
          icon: const Icon(Icons.arrow_forward_ios),
          onPressed: loading
              ? null
              : () async {
                  if (await webViewController.canGoForward()) {
                    await webViewController.goForward();
                  } else if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        duration: Duration(milliseconds: 500),
                        content: Text('Nenhuma página posterior'),
                      ),
                    );
                  }
                },
        ),
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: loading ? null : () => webViewController.reload(),
        ),
        if (hasOrientation)
          IconButton(
            icon: const Icon(Icons.crop_rotate),
            onPressed: () async {
              if (orientation == Orientation.portrait) {
                await SystemChrome.setPreferredOrientations(
                  [
                    DeviceOrientation.landscapeLeft,
                  ],
                );
              } else {
                await SystemChrome.setPreferredOrientations(
                  [
                    DeviceOrientation.portraitUp,
                  ],
                );
              }
              Future.delayed(const Duration(seconds: 10), () {
                SystemChrome.setPreferredOrientations(
                  [
                    DeviceOrientation.portraitDown,
                    DeviceOrientation.portraitUp,
                    DeviceOrientation.landscapeLeft,
                    DeviceOrientation.landscapeRight,
                  ],
                );
              });
            },
          ),
      ],
    );
  }
}

import 'dart:io';
import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/add_file/add_file_state.dart';
import 'package:cooperado_minha_unimed/models/glosa_resource/file-attach.model.dart';
import 'package:cooperado_minha_unimed/shared/api/services.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/utils/file.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';

import 'package:path/path.dart';

const String loadingSendMessage = 'Enviando...';
const String removingMessage = 'Removendo...';
const String loadingMessage = 'Carregando...';

class GlosaResourceAddFileCubit extends Cubit<GlosaResourceAddFileState> {
  GlosaResourceAddFileCubit() : super(InitialState());

  final logger = UnimedLogger(className: 'GlosaResourceAddFileBloc');

  List<FileAttach?> _attachments = List<FileAttach?>.empty(growable: true);

  List<FileAttach?> get attachments => _attachments;

  int sizeFileByte = 5000000;
  String sizeFileString = "5Mb"; //5Mb

  int _maxFilesAttachments = 5;
  int get maxFilesAttachments => _maxFilesAttachments;

  final List<String> _formatFilesAllowed = ['png', 'jpg', 'jpeg', 'pdf'];
  get formatFilesAllowed => _formatFilesAllowed;

  setFilesConfig({
    final int? maxFiles,
    final int? maxFileSizeInMb,
  }) {
    _maxFilesAttachments = maxFiles ?? _maxFilesAttachments;
    sizeFileByte = maxFileSizeInMb != null
        ? maxFileSizeInMb * 1000000
        : sizeFileByte; // Convert Mb to bytes
    sizeFileString = maxFileSizeInMb != null
        ? '$maxFileSizeInMb Mb'
        : sizeFileString; // Update size string
  }

  loadFiles({required final String sequencial}) async {
    emit(LoadingState());

    try {
      final api = Locator.instance!.get<ServicesApi>();

      _attachments = List<FileAttach?>.empty(growable: true);

      final files = await api.getGlosaFiles(sequencial: sequencial);

      if (files.isNotEmpty) {
        _attachments.addAll(files);
      }
      _attachments.add(null);

      emit(DoneState(attachments: _attachments));
    } catch (e) {
      logger.e("GlosaResourceAddFileCubit Error on Load File Attachment$e");
      emit(ErrorState('Falha ao carregar os arquivos'));
    }
  }

  attachFile({
    required final File file,
    required final int index,
  }) async {
    String name = basename(file.path);

    final fileSize = await file.length();

    if (fileSize > sizeFileByte) {
      emit(GlosaResourceAttachmentErrorState(
        message: 'O tamanho de arquivo máximo permitido é $sizeFileString',
      ));

      return;
    }

    if (_attachments.length > _maxFilesAttachments) {
      emit(GlosaResourceAttachmentErrorState(
        message:
            'Você pode anexar no máximo $_maxFilesAttachments imagens por solicitação.',
      ));

      return;
    } else if (_fileIsNotAllow(file.path)) {
      emit(GlosaResourceAttachmentErrorState(
        message: 'O formato do arquivo $name não é permitido',
      ));

      return;
    }

    emit(LoadingState());

    final compressedFile = await FileUtils.getCompressedFile(file);
    _attachments[index] = FileAttach(
      file: file,
      name: name,
      thumbnail: compressedFile,
    );
    if (attachments.length < maxFilesAttachments) {
      _attachments.add(null);
    }

    emit(DoneState(attachments: _attachments));
  }

  removeFile({
    required int index,
  }) async {
    emit(LoadingDocumentState(
        index: index,
        message: "$removingMessage ${_attachments[index]?.name}"));

    try {
      _attachments.removeAt(index);
      if (_attachments.last != null) {
        _attachments.add(null);
      }

      //Faltar chamar serviço para remover arquivo, o serviço não foi feito ainda.

      emit(DoneState(attachments: _attachments));
    } catch (e) {
      logger.e("GlosaResourceAddFileCubit Error on Remove File Attachment$e");
      emit(ErrorState('Falha ao remover o arquivo'));
    }
  }

  sendAttachedFiles({
    required final String guide,
    required final String sequencial,
  }) async {
    final api = Locator.instance!.get<ServicesApi>();
    int index = 0;
    int count = 1;
    int filesSended = 0;

    final filesToSend = _attachments
        .where((element) => element != null && element.sended != true)
        .toList()
        .length;

    for (FileAttach? file in _attachments) {
      try {
        emit(LoadingDocumentState(
            index: index,
            message: "$loadingSendMessage ($count/$filesToSend)"));
        if (file != null && file.sended != true) {
          await api.addGlosaFile(
            sequencial: sequencial,
            guide: guide,
            file: file,
          );
          count++;
          file.sended = true;
        }
        filesSended++;

        emit(FileSendedState(index: index));
        index++;
      } catch (e) {
        file?.sended = false;
        logger.e("GlosaResourceAddFileCubit Error on Send File Attachment$e");
        emit(ErrorSendedState(index: count));
      }
    }
    if (filesSended == _attachments.length) {
      emit(FinishSendAllFileState(attachments: _attachments));
    } else {
      emit(DoneState(attachments: _attachments));
    }
  }

  stopAttachFile() {
    emit(DoneState(attachments: attachments));
  }

  bool _fileIsNotAllow(filename) {
    try {
      final List<String> fileparts = filename.split('.');
      final String fileExtension = fileparts.last.toLowerCase();

      return !_formatFilesAllowed.contains(fileExtension);
    } catch (e) {
      logger.e(
        "GlosaResourceAddFileCubit Error on File Attachment validation $e",
      );

      return true;
    }
  }
}

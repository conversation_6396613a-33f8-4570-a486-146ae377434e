class ResExamResultImageDetailModel {
  late String name;
  String? codeExam;
  late String urlDocument;
  late List<Applicants> applicants;

  ResExamResultImageDetailModel(
      {required this.name,
      this.codeExam,
      required this.urlDocument,
      required this.applicants});

  ResExamResultImageDetailModel.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    codeExam = json['codeExam'];
    urlDocument = json['urlDocument'];
    if (json['applicants'] != null) {
      applicants = <Applicants>[];
      json['applicants'].forEach((v) {
        applicants.add(Applicants.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['codeExam'] = codeExam;
    data['urlDocument'] = urlDocument;
    data['applicants'] = applicants.map((v) => v.toJson()).toList();
    return data;
  }
}

class Applicants {
  late String name;

  Applicants({required this.name});

  Applicants.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    return data;
  }
}

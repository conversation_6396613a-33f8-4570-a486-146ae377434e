// ignore_for_file: unnecessary_null_comparison

import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/configs/res_configs_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/indicators/res_indicator_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/indicators/res_indicator_data_state.dart';
import 'package:cooperado_minha_unimed/bloc/res/indicators/res_indicators_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/res/indicators/widgets/indicators_graphics.dart';
import 'package:cooperado_minha_unimed/screens/res/indicators/widgets/show_modal_indicators.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/app_bar_res.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/empty_list.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/erro_service.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/filters_widget.dart';
import 'package:cooperado_minha_unimed/shared/utils/router_observer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:intl/intl.dart';

// ignore: must_be_immutable
class ResIndicatorsScreen extends StatefulWidget {
  final String nameBeneficiary;
  String cpfBeneficiary = '';
  ResIndicatorsScreen(
      {super.key, required this.nameBeneficiary, required this.cpfBeneficiary});

  @override
  State<ResIndicatorsScreen> createState() => _ResIndicatorsScreenState();
}

class _ResIndicatorsScreenState extends State<ResIndicatorsScreen>
    with RouteAware {
  FocusNode? focusTextFieldSearch;
  final bool _isLoading = false;
  int _filterMonthSelected = 3;
  late DateTimeRange _dateRangeToFilter;
  final List<int> _lastMonthsToFilter = [3, 6];

  @override
  void initState() {
    context.read<ResConfigCubit>().getResConfigs(
        crm: context.read<AuthCubit>().credentials.crm,
        cpf: widget.cpfBeneficiary);
    _dateRangeToFilter = DateTimeRange(
      start: _selectedDataStart(3),
      end: DateTime.now(),
    );
    _loadIndicators();

    super.initState();

    focusTextFieldSearch = FocusNode();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void dispose() {
    focusTextFieldSearch!.dispose();

    super.dispose();
  }

  void _loadIndicators() {
    context.read<ResIndsicatorsDataCubit>().clearSelectedIndicators();

    context
        .read<ResIndicatorsCubit>()
        .getResBrazilBeneficiaryIndicators(cpf: widget.cpfBeneficiary);
  }

  void _onDateRangeSelected(DateTimeRange dateRange) {
    setState(() {
      _filterMonthSelected = 0;
      _dateRangeToFilter = dateRange;
    });
    _loadIndicators();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarRes(
        title: 'Indicadores',
        nameBeneficiary: widget.nameBeneficiary,
      ),
      body: Padding(
        padding: const EdgeInsets.only(left: 20, right: 20, top: 24),
        child: SingleChildScrollView(
          child: BlocBuilder<ResIndsicatorsDataCubit, ResIndicatorsDataState>(
              builder: (context, stateResIndicatorsDataState) {
            if (stateResIndicatorsDataState is LoadingResIndicatorsDataState) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    height: MediaQuery.of(context).size.height / 4,
                  ),
                  const Center(
                      child: SpinKitCircle(color: CooperadoColors.tealGreen)),
                ],
              );
            }
            return Column(
              children: [
                FiltersWidget(
                  isLoading: _isLoading,
                  lastMonthsToFilter: _lastMonthsToFilter,
                  filterMonthSelected: _filterMonthSelected,
                  dateRangeToFilter: _dateRangeToFilter,
                  onMonthFilterChanged: (filterSelected) {
                    setState(() {
                      _filterMonthSelected =
                          filterSelected ?? _filterMonthSelected;
                      debugPrint('filterSelected: $filterSelected');
                      _dateRangeToFilter = DateTimeRange(
                        start: _selectedDataStart(_filterMonthSelected),
                        end: DateTime.now(),
                      );
                    });
                    _loadIndicators();
                  },
                  // onClearDateRange: () {
                  //   setState(() {
                  //     _dateRangeToFilter = DateTimeRange(
                  //       start: _selectedDataStart(3),
                  //       end: DateTime.now(),
                  //     );
                  //     _filterMonthSelected = 3;
                  //   });
                  //   _loadIndicators();
                  // },
                  selectDateToFilter: _selectDateToFilter,
                  onDateRangeSelected: _onDateRangeSelected,
                ),
                BlocBuilder<ResIndicatorsCubit, ResIndicatorsState>(
                  builder: (context, state) {
                    if (state is LoadingResAllIndicatorsState) {
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            height: MediaQuery.of(context).size.height / 4,
                          ),
                          const Center(
                              child: SpinKitCircle(
                                  color: CooperadoColors.tealGreen)),
                        ],
                      );
                    }

                    if (state is LoadedResIndicatorState) {
                      return Column(children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 8.0),
                          child: Align(
                            alignment: Alignment.topLeft,
                            child: Text(
                              'Indicadores',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: CooperadoColors.blackText,
                              ),
                            ),
                          ),
                        ),
                        IndicatorsSelection(
                          cpfBeneficiary: widget.cpfBeneficiary,
                          dateRangeToFilter: _dateRangeToFilter,
                        ),
                        IndicatorGraphics(
                          cpfBeneficiary: widget.cpfBeneficiary,
                          dateRangeToFilter: _dateRangeToFilter,
                          selectedIndicators: context
                              .read<ResIndicatorsCubit>()
                              .listIndicators
                              .map((e) => e.id.toString())
                              .toList(),
                        )
                      ]);
                    } else if (state is NoDataResIndicatorState) {
                      return Expanded(
                        child: EmptyList(
                          pathIcon: 'assets/svg/icon_file.svg',
                          message: _dateRangeToFilter != null
                              ? 'Sem dados de indicadores para o período de ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter.start)} até  ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter.end)}'
                              : 'Sem dados de indicadores.',
                        ),
                      );
                    }

                    if (state is ErrorResIndicatorState) {
                      return SizedBox(
                        height: MediaQuery.of(context).size.height * 0.5,
                        child: ErroService(
                            message: state.message,
                            onPressed: () {
                              _loadIndicators();
                            }),
                      );
                    }

                    return const SizedBox();
                  },
                ),
              ],
            );
          }),
        ),
      ),
    );
  }

  Future<DateTime?> _selectDateToFilter(BuildContext context, String helpText,
      {DateTime? firstDate}) async {
    return await showDatePicker(
      context: context,
      helpText: helpText,
      initialDate: DateTime.now(),
      firstDate:
          firstDate ?? DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            textTheme: const TextTheme(
              labelSmall: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.bold,
              ),
              headlineMedium: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          child: child!,
        );
      },
    );
  }

  DateTime _selectedDataStart(int meses) {
    DateTime dataAtual = DateTime.now();
    DateTime novaData = DateTime(
      dataAtual.year,
      dataAtual.month - meses,
      dataAtual.day,
      dataAtual.hour,
      dataAtual.minute,
      dataAtual.second,
      dataAtual.millisecond,
      dataAtual.microsecond,
    );

    return novaData;
  }
}

import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/utils/graph_utils.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../historical-production/line_chart_historic.dart';

class LineGraphHistory extends StatefulWidget {
  const LineGraphHistory({super.key, required this.data});

  final List<Valores> data;

  @override
  State<LineGraphHistory> createState() => _LineGraphHistoryState();
}

class _LineGraphHistoryState extends State<LineGraphHistory> {
  List<Color> gradientColors = [
    CooperadoColors.tealGreen,
    CooperadoColors.tealGreen,
  ];
  int leftCount = 0;
  List<double> valuesY = [];
  @override
  void initState() {
    // leftCount = 0;
    valuesY.clear();
    for (Valores element in widget.data) {
      valuesY.add(element.valor ?? 0);
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
      builder: (context, state) {
        return Stack(
          children: <Widget>[
            AspectRatio(
              aspectRatio: 1.70,
              child: Padding(
                padding: const EdgeInsets.only(
                  right: 18,
                  left: 12,
                  top: 24,
                  bottom: 12,
                ),
                child: LineChart(
                  mainData(!state.isSensitiveDataVisible),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  double completeNumbers(double input) {
    double result = 0.0;

    if (input % widget.data.length == 0) {
      result = input;
    } else {
      double completeValue = widget.data.length - (input % widget.data.length);
      result = input + completeValue;
    }
    return result;
  }

  Widget bottomTitleWidgets(double value, TitleMeta meta) {
    List<String> dates = [];
    for (int i = 0; i < widget.data.length; i++) {
      dates.add(widget.data[i].mes);
    }

    return SideTitleWidget(
      axisSide: meta.axisSide,
      child: Text(dates[value.toInt()], style: const TextStyle(fontSize: 13)),
    );
  }

 LineChartData mainData(bool isSensitiveDataVisible) {
  return LineChartData(
    gridData: FlGridData(
      show: true,
      drawHorizontalLine: true,
      drawVerticalLine: false,
      horizontalInterval: _getInterval(),
      verticalInterval: widget.data.length.toDouble(),
      getDrawingHorizontalLine: (value) {
        return const FlLine(
          color: Colors.grey,
          strokeWidth: 0.8,
        );
      },
    ),
    titlesData: FlTitlesData(
      show: true,
      rightTitles: const AxisTitles(
        sideTitles: SideTitles(showTitles: false),
      ),
      topTitles: const AxisTitles(
        sideTitles: SideTitles(showTitles: false),
      ),
      bottomTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          reservedSize: 30,
          interval: 1,
          getTitlesWidget: bottomTitleWidgets,
        ),
      ),
      leftTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: !isSensitiveDataVisible,
          interval: _getInterval(),
          reservedSize: 60,
        ),
      ),
    ),
    borderData: FlBorderData(
      show: true,
      border: const Border(bottom: BorderSide(), top: BorderSide()),
    ),
    minX: 0,
    maxX: widget.data.length.toDouble() - 1,
    minY: 0,
    maxY: completeNumbers(GraphUtils.findMax(valuesY)),
    lineTouchData: LineTouchData(
      getTouchLineEnd: (data, index) => double.infinity,
      getTouchedSpotIndicator:
          (LineChartBarData barData, List<int> spotIndexes) {
        return spotIndexes.map((spotIndex) {
          return TouchedSpotIndicatorData(
            const FlLine(color: Colors.grey, strokeWidth: 3),
            FlDotData(
              getDotPainter: (spot, percent, barData, index) =>
                  FlDotCirclePainter(
                radius: 8,
                color: CooperadoColors.tealGreen,
              ),
            ),
          );
        }).toList();
      },
      touchTooltipData: LineTouchTooltipData(
        maxContentWidth: 100,
        tooltipBgColor: Colors.grey[300]!,
        getTooltipItems: (touchedSpots) {
          return touchedSpots.map((LineBarSpot touchedSpot) {
            final textStyle = TextStyle(
              color: touchedSpot.bar.color,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            );
            final text = !isSensitiveDataVisible
                ? 'R\$ ${GraphUtils.priceToCurrency(touchedSpot.y)}'
                : 'R\$ ****';
            return LineTooltipItem(text, textStyle);
          }).toList();
        },
      ),
    ),
    lineBarsData: [
      LineChartBarData(
        spots: List.generate(valuesY.length,
            (index) => FlSpot(index.toDouble(), valuesY[index])),
        isCurved: false,
        color: CooperadoColors.tealGreen,
        barWidth: 3,
        isStrokeCapRound: true,
        dotData: const FlDotData(
          show: true,
        ),
        belowBarData: BarAreaData(
          show: true,
          gradient: LinearGradient(
            colors: gradientColors
                .map((color) => color.withOpacity(0.1))
                .toList(),
          ),
        ),
      ),
    ],
  );
}

  double _getInterval() {
    if ((completeNumbers(GraphUtils.findMax(valuesY)) / widget.data.length) <
        1) {
      return 1;
    }
    return completeNumbers(GraphUtils.findMax(valuesY)) / widget.data.length;
  }
}

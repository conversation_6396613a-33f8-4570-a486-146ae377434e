// ignore_for_file: prefer_collection_literals

import 'package:intl/intl.dart';

class IndicatorDataModel {
  late int id;
  late String unity;
  late List<IndicatorsData> indicatorsData;

  IndicatorDataModel(
      {required this.id, required this.unity, required this.indicatorsData});
        List<IndicatorsData> get indicatorsDataSorted {
    indicatorsData.sort((a, b) => a.dateTime.compareTo(b.dateTime));
    return indicatorsData;
  }

  IndicatorDataModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    unity = json['unity'];
    if (json['indicatorsData'] != null) {
      indicatorsData = List<IndicatorsData>.empty(growable: true);
      json['indicatorsData'].forEach((v) {
        indicatorsData.add(IndicatorsData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['unity'] = unity;
    data['indicatorsData'] =
        indicatorsData.map((v) => v.toJson()).toList();

    return data;
  }
}


class IndicatorsData {
  late String date;
  late String x;
  late String y;

  IndicatorsData({required this.date, required this.x, required this.y});

  String get dateFormatted {
    return DateFormat('dd/MM/yyyy HH:mm').format(DateTime.parse(date));
  }

  DateTime get dateTime {
    return DateTime.parse(date);
  }

  IndicatorsData.fromJson(Map<String, dynamic> json) {
    date = json['date'];
    x = json['x'];
    y = json['y'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['date'] = date;
    data['x'] = x;
    data['y'] = y;
    return data;
  }
}

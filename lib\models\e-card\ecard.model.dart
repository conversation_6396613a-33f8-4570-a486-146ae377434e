class ECardModel {
  final String providerCard;
  final DateTime activationStartDate;
  final DateTime? validityDate;
  final String status;
  final ViewAddress viewAddress;

  ECardModel({
    required this.providerCard,
    required this.activationStartDate,
    required this.validityDate,
    required this.status,
    required this.viewAddress,
  });

  factory ECardModel.fromJson(Map<String, dynamic> json) {
    return ECardModel(
      providerCard: json['providerCard'],
      activationStartDate: DateTime.parse(json['activationStartDate']),
      validityDate: json['validityDate'] != null
          ? DateTime.parse(json['validityDate'])
          : null,
      status: json['status'],
      viewAddress: ViewAddress.fromJson(json['viewAddress']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'providerCard': providerCard,
      'activationStartDate': activationStartDate.toIso8601String(),
      'validityDate': validityDate?.toIso8601String(),
      'status': status,
      'viewAddress': viewAddress.toJson(),
    };
  }

  bool isStatusPending() {
    return status.toLowerCase() == 'Pendente'.toLowerCase();
  }

  bool isStatusActive() {
    return status.toLowerCase() == 'Ativo'.toLowerCase();
  }

  bool getRemainingTime() {
    if (validityDate == null) {
      return false;
    }
    final now = DateTime.now();
    if (now.isBefore(validityDate!)) {
      return true;
    } else {
      return false;
    }
  }

  ECardModel copyWith({
    String? providerCard,
    DateTime? activationStartDate,
    DateTime? validityDate,
    String? status,
    ViewAddress? viewAddress,
  }) {
    return ECardModel(
      providerCard: providerCard ?? this.providerCard,
      activationStartDate: activationStartDate ?? this.activationStartDate,
      validityDate: validityDate ?? this.validityDate,
      status: status ?? this.status,
      viewAddress: viewAddress ?? this.viewAddress,
    );
  }
}

class ViewAddress {
  final String streetTypeCode;
  final String streetName;
  final int addressNumber;

  ViewAddress({
    required this.streetTypeCode,
    required this.streetName,
    required this.addressNumber,
  });

  factory ViewAddress.fromJson(Map<String, dynamic> json) {
    return ViewAddress(
      streetTypeCode: json['streetTypeCode'],
      streetName: json['streetName'],
      addressNumber: json['addressNumber'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'streetTypeCode': streetTypeCode,
      'streetName': streetName,
      'addressNumber': addressNumber,
    };
  }
}

import 'package:cooperado_minha_unimed/bloc/notificao/notificacao_state.dart';
import 'package:cooperado_minha_unimed/shared/api/notificacao-graphql.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class NotificacaoCubit extends Cubit<NotificacaoState> {
  NotificacaoCubit() : super(InitialNotificaoState());

  void listNotificacoesResult({
    required String codPrestador,
    int? page,
    int? perPage,
    DateTime? startDateTime,
    DateTime? endDateTime,
  }) async {
    emit(NotificacaoStateLoading());
    try {
      final response = await Locator.instance!<GraphQlApiNotificacao>().getListNotificacoes(
        codPrestador: codPrestador,
        page: page,
        perPage: perPage,
        startDateTime: startDateTime,
        endDateTime: endDateTime,
      );
      emit(
        NotificacaoStateLoaded(
          totalPages: response.totalPages,
          notificacoes: response.notifications ?? [],
        ),
      );
    } catch (e) {
      emit(NotificacaoStateError(message: e.toString()));
    }
  }

  void markAsSeen({
    required String codPrestador,
    required String notificationId,
  }) async {
    try {
      await Locator.instance!<GraphQlApiNotificacao>().markAsSeen(
        codPrestador: codPrestador,
        notificationId: notificationId,
      );
    } catch (e) {
      emit(NotificacaoStateError(message: e.toString()));
    }
  }

  void togglePinNotificationStatus({
    required String codPrestador,
    required String notificationId,
    required bool pinNotification,
  }) async {
    try {
      await Locator.instance!<GraphQlApiNotificacao>().togglePinNotificationStatus(
        codPrestador: codPrestador,
        notificationId: notificationId,
        pinNotification: pinNotification,
      );
    } catch (e) {
      emit(NotificacaoStateError(message: e.toString()));
    }
  }

  Future<void> deleteNotification({
    required String codPrestador,
    required String notificationId,
  }) async {
    try {
      await Locator.instance!<GraphQlApiNotificacao>().deleteNotification(
        codPrestador: codPrestador,
        notificationId: notificationId,
      );
      emit(DoneDeleteNotificacaoState());
    } catch (e) {
      emit(NotificacaoStateError(message: e.toString()));
    }
  }
}

import 'package:auto_size_text/auto_size_text.dart';
import 'package:cooperado_minha_unimed/bloc/notificao/notificacao_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/notificao/notifications-count/notifications_count_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/profile/profile_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/notificacao/notification.model.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ExpandableNotificationItem extends StatefulWidget {
  final NotificationModel notification;
  final Function() updateNotificationCount;
  final Function(NotificationModel) togglePinNotificationStatus;

  const ExpandableNotificationItem({
    super.key,
    required this.notification,
    required this.updateNotificationCount,
    required this.togglePinNotificationStatus,
  });

  @override
  State<ExpandableNotificationItem> createState() => _ExpandableNotificationItemState();
}

class _ExpandableNotificationItemState extends State<ExpandableNotificationItem> {
  bool _isExpanded = false;

  void _markAsSeen() {
    context.read<NotificacaoCubit>().markAsSeen(
          codPrestador: context.read<ProfileCubit>().user.codPrestador.toString(),
          notificationId: widget.notification.notificationId!,
        );

    context.read<NotificationsCountCubit>().updateNotificationCount();

    setState(() {
      widget.notification.readAt = DateTime.now().toIso8601String();
    });
    widget.updateNotificationCount();
  }

  @override
  Widget build(BuildContext context) {
    bool isSeen = widget.notification.readAt != null;

    return InkWell(
      onTap: () {
        setState(() => _isExpanded = !_isExpanded);
        if (_isExpanded && widget.notification.readAt == null) {
          _markAsSeen();
        }
      },
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 3),
        padding: const EdgeInsets.only(left: 10, right: 10, top: 16, bottom: 10),
        decoration: BoxDecoration(
          color: isSeen ? CooperadoColors.grayLight3 : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSeen ? CooperadoColors.grayLight3 : CooperadoColors.backgroundColor,
          ),
          boxShadow: const [
            BoxShadow(
              color: Colors.grey,
              offset: Offset(0.0, 0.8),
              blurRadius: 2.0,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    context.read<NotificacaoCubit>().togglePinNotificationStatus(
                          codPrestador: context.read<ProfileCubit>().user.codPrestador.toString(),
                          notificationId: widget.notification.notificationId!,
                          pinNotification: !(widget.notification.pinned ?? false),
                        );
                    widget.togglePinNotificationStatus(widget.notification);
                  },
                  child: Icon(
                    (widget.notification.pinned ?? false) ? Icons.push_pin : Icons.push_pin_outlined,
                    color: CooperadoColors.grayDark,
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          if (!isSeen)
                            Container(
                              height: 5,
                              width: 5,
                              margin: const EdgeInsets.only(right: 5),
                              decoration: const BoxDecoration(
                                color: CooperadoColors.green,
                                shape: BoxShape.circle,
                              ),
                            ),
                          Expanded(
                            child: AutoSizeText(
                              widget.notification.title ?? "Não informado",
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: isSeen ? Colors.grey[700] : Colors.black,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      if (!_isExpanded)
                        Padding(
                          padding: const EdgeInsets.only(top: 5),
                          child: AutoSizeText(
                            widget.notification.description ?? "Não informado",
                            style: const TextStyle(
                              fontSize: 12,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                    ],
                  ),
                ),
                const SizedBox(width: 10),
                Icon(
                  _isExpanded ? Icons.expand_less : Icons.expand_more,
                  color: CooperadoColors.grayDark,
                ),
              ],
            ),
            if (_isExpanded)
              Padding(
                padding: const EdgeInsets.only(top: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Divider(color: Colors.grey[300]),
                    AutoSizeText(
                      widget.notification.description ?? "Não informado",
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            const SizedBox(height: 5),
            if (_isExpanded) const Divider(color: Colors.grey),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (_isExpanded)
                  InkWell(
                    onTap: _deleteNotification,
                    child: Ink(
                      child: const AutoSizeText(
                        'excluir',
                        style: TextStyle(
                          color: CooperadoColors.darkRed,
                        ),
                      ),
                    ),
                  ),
                AutoSizeText(
                  widget.notification.formattedCreatedAtDate,
                  style: const TextStyle(
                    fontSize: 10,
                    color: CooperadoColors.grayDark,
                  ),
                  minFontSize: 8,
                  maxFontSize: 12,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _deleteNotification() {
    Alert.open(
      context,
      title: 'Tem certeza?',
      text: 'Quer mesmo excluir a notificação?',
      actions: [
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5.0)),
            backgroundColor: CooperadoColors.tealGreen,
            textStyle: const TextStyle(
              color: Colors.white,
            ),
          ),
          onPressed: () {
            context.read<NotificacaoCubit>().deleteNotification(
                  codPrestador: context.read<ProfileCubit>().user.codPrestador.toString(),
                  notificationId: widget.notification.notificationId!,
                );
            Navigator.of(context).pop();
          },
          child: const Text(
            'Sim, excluir',
          ),
        )
      ],
      textButtonClose: 'Cancelar',
    );
  }
}

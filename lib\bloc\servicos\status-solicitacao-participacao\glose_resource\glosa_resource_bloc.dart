import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/models/glosa_resource/glosa_resource.model.dart';
import 'package:cooperado_minha_unimed/shared/api/services.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:flutter/material.dart';

import 'glosa_resource_state.dart';

class GlosaResourceCubit extends Cubit<GlosaResourceState> {
  GlosaResourceCubit() : super(GlosaResourceInitial());

  late List<GlosaResourceData> _solicitacoes;

  List<GlosaResourceData> get solicitacoes => _solicitacoes;

  getGlosaResourceEvent({required String crm, required String guide}) async {
    emit(LoadingGetGlosaResourceState());
    try {
      _solicitacoes = await Locator.instance!<ServicesApi>()
          .getGlosaResource(crm: crm, guide: guide);

      emit(DoneGetGlosaResourceState(_solicitacoes));
    } catch (ex) {
      debugPrint(ex.toString());
      emit(ErrorGlosaResourceState('$ex'));
    }
  }
}

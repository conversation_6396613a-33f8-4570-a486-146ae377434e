class RedefinePasswordModel {
  String? crm;
  String token;
  String senha;
  String confirmarSenha;

  RedefinePasswordModel({
    this.crm,
    required this.token,
    required this.senha,
    required this.confirmarSenha,
  });

  RedefinePasswordModel.fromJson(Map<String, dynamic> json)
      : crm = json['crm'],
        token = json['token'],
        senha = json['senha'],
        confirmarSenha = json['confirmarSenha'];

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['crm'] = crm;
    data['token'] = token;
    data['senha'] = senha;
    data['confirmarSenha'] = confirmarSenha;
    return data;
  }
}

import 'package:cooperado_minha_unimed/bloc/indicators/daily_attendance/daily_attendance_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/daily-attendance/daily_graphic.dart';
import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class Daily extends StatelessWidget {
  final bool animate;
  final int services;
  const Daily({super.key, required this.services, this.animate = true});
  @override
  Widget build(BuildContext context) {
    final dateBegin =
        BlocProvider.of<DailyAttendanceCubit>(context, listen: true).dateBegin;
    final dateEnd =
        BlocProvider.of<DailyAttendanceCubit>(context, listen: true).dateEnd!;

    return Material(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Flexible(
                  child: Text(
                    StringUtils.formatPeriod(dateBegin, dateEnd),
                    style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: CooperadoColors.grayDark),
                    textAlign: TextAlign.center,
                  ),
                ),
                Flexible(
                  child: Container(
                    decoration: const BoxDecoration(
                      border: Border(
                        left: BorderSide(
                            width: 1.0, color: CooperadoColors.tealGreen),
                      ),
                    ),
                    child: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
  builder: (context, state) {
    final isSensitiveDataVisible = state.isSensitiveDataVisible;
    return Padding(
      padding: const EdgeInsets.only(left: 10, bottom: 5),
      child: Text(
        isSensitiveDataVisible ? '$services Serviços realizados' : '** Serviços realizados',
        style: const TextStyle(color: CooperadoColors.tealGreen),
      ),
    );
  },
),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 150, child: DailyGraphic(animate: animate)),
        ],
      ),
    );
  }
}

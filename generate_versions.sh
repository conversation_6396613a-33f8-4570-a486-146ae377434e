#!/bin/sh

if flutter pub run cooperado_minha_unimed:firebase_upload_android; then
    flutter pub run cooperado_minha_unimed:fortaleza_appstore_firebase_upload_ios
fi

if flutter clean; then 
    flutter pub get
fi    

if flutter pub run cooperado_minha_unimed:sobral_firebase_upload_android; then
    flutter pub run cooperado_minha_unimed:sobral_firebase_upload_ios
fi

if flutter clean; then 
    flutter pub get
fi  

if flutter pub run cooperado_minha_unimed:cariri_firebase_upload_android; then
    flutter pub run cooperado_minha_unimed:cariri_firebase_upload_ios
fi

if flutter pub run cooperado_minha_unimed:ceara_firebase_upload_android; then
    flutter pub run cooperado_minha_unimed:ceara_firebase_upload_ios
fi 
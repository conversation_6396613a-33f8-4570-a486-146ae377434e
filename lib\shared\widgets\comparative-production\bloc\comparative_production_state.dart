part of 'comparative_production_cubit.dart';

abstract class ComparativeProductionState extends Equatable {
  const ComparativeProductionState();
}

class InitialComparativeProductionState extends ComparativeProductionState {
  @override
  List<Object> get props => [];
}

class LoadingComparativeProductionState extends ComparativeProductionState {
  @override
  List<Object> get props => [];
}

class ErrorComparativeProductionState extends ComparativeProductionState {
  final String? message;
  @override
  List<Object?> get props => [message];
  const ErrorComparativeProductionState(this.message);
}

class LoadedComparativeProductionState extends ComparativeProductionState {
  final ComparativeProductionModel comparativeProductionModel;
  @override
  List<Object> get props => [comparativeProductionModel];

  const LoadedComparativeProductionState(this.comparativeProductionModel);
}

class SelectDateLoading extends ComparativeProductionState {
  @override
  List<Object> get props => [];
}

class SelectedDate extends ComparativeProductionState {
  final DateTime? dateTime;
  @override
  List<Object?> get props => [dateTime];
  const SelectedDate(this.dateTime);
}

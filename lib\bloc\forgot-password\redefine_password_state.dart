part of 'redefine_password_cubit.dart';

abstract class RedefinePasswordState extends Equatable {
  const RedefinePasswordState();

  @override
  List<Object> get props => [];
}

class RedefinePasswordInitial extends RedefinePasswordState {}

class LoadingRedefinePassworddState extends RedefinePasswordState {
  @override
  List<Object> get props => [];
}

class ErrorRedefinePassworddState extends RedefinePasswordState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorRedefinePassworddState(this.message);
}

class SucessRedefinePasswordState extends RedefinePasswordState {
  final String message;

  @override
  List<Object> get props => [message];

  const SucessRedefinePasswordState(this.message);
}

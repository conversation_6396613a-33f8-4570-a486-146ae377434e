import 'package:cooperado_minha_unimed/models/res-internal/lab_exam.model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:intl/intl.dart';

void main() {
  group('ResLabExamModel', () {
    test('from<PERSON><PERSON> should correctly parse JSON data', () {
      final jsonData = {
        'doctorEmail': '<EMAIL>',
        'doctorId': '12345',
        'doctorName': 'Dr. <PERSON>',
        'orderDate': '2024-07-26T10:00:00',
        'orderId': 'ORD123456',
        'orderLabel': 'Hemograma Completo',
        'orderStatus': 'Finalizado',
        'patientName': '<PERSON>',
        'resultPdfUrl': 'https://exemplo.com/resultado.pdf',
        'resultScheduledDate': '2024-07-28T14:00:00',
        'unit': 'Laboratório Central',
        'unitId': 'LAB001',
        'unitName': 'Unimed Laboratório Central'
      };

      final model = ResLabExamModel.fromJson(jsonData);

      expect(model.doctorEmail, '<EMAIL>');
      expect(model.doctorId, '12345');
      expect(model.doctorName, 'Dr. <PERSON> <PERSON>');
      expect(model.orderDate, '2024-07-26T10:00:00');
      expect(model.orderId, 'ORD123456');
      expect(model.orderLabel, 'Hemograma Completo');
      expect(model.orderStatus, 'Finalizado');
      expect(model.patientName, 'Maria Oliveira');
      expect(model.resultPdfUrl, 'https://exemplo.com/resultado.pdf');
      expect(model.resultScheduledDate, '2024-07-28T14:00:00');
      expect(model.unit, 'Laboratório Central');
      expect(model.unitId, 'LAB001');
      expect(model.unitName, 'Unimed Laboratório Central');

      // Test formatted date
      final expectedFormattedDate = DateFormat('dd/MM/yyyy HH:mm')
          .format(DateTime.parse('2024-07-26T10:00:00'));
      expect(model.dateOrderFormatted, expectedFormattedDate);
    });

    test('fromJson should handle null optional fields', () {
      final jsonData = {
        'doctorId': '12345',
        'doctorName': 'Dr. João Silva',
        'orderDate': '2024-07-26T10:00:00',
        'orderId': 'ORD123456',
        'orderLabel': 'Hemograma Completo',
        'orderStatus': 'Finalizado',
        'patientName': 'Maria Oliveira',
        'resultScheduledDate': '2024-07-28T14:00:00',
        'unit': 'Laboratório Central',
        'unitId': 'LAB001',
        'unitName': 'Unimed Laboratório Central'
      };

      final model = ResLabExamModel.fromJson(jsonData);

      expect(model.doctorEmail, null);
      expect(model.resultPdfUrl, null);
    });

    test('toJson should correctly convert model to JSON', () {
      final model = ResLabExamModel(
        doctorEmail: '<EMAIL>',
        doctorId: '12345',
        doctorName: 'Dr. João Silva',
        orderDate: '2024-07-26T10:00:00',
        orderId: 'ORD123456',
        orderLabel: 'Hemograma Completo',
        orderStatus: 'Finalizado',
        patientName: 'Maria Oliveira',
        resultPdfUrl: 'https://exemplo.com/resultado.pdf',
        resultScheduledDate: '2024-07-28T14:00:00',
        unit: 'Laboratório Central',
        unitId: 'LAB001',
        unitName: 'Unimed Laboratório Central',
      );

      final json = model.toJson();

      expect(json['doctorEmail'], '<EMAIL>');
      expect(json['doctorId'], '12345');
      expect(json['doctorName'], 'Dr. João Silva');
      expect(json['orderDate'], '2024-07-26T10:00:00');
      expect(json['orderId'], 'ORD123456');
      expect(json['orderLabel'], 'Hemograma Completo');
      expect(json['orderStatus'], 'Finalizado');
      expect(json['patientName'], 'Maria Oliveira');
      expect(json['resultPdfUrl'], 'https://exemplo.com/resultado.pdf');
      expect(json['resultScheduledDate'], '2024-07-28T14:00:00');
      expect(json['unit'], 'Laboratório Central');
      expect(json['unitId'], 'LAB001');
      expect(json['unitName'], 'Unimed Laboratório Central');
    });

    test('dateOrderFormatted should format date correctly', () {
      final model = ResLabExamModel(
        doctorId: '12345',
        doctorName: 'Dr. João Silva',
        orderDate: '2024-07-26T10:00:00',
        orderId: 'ORD123456',
        orderLabel: 'Hemograma Completo',
        orderStatus: 'Finalizado',
        patientName: 'Maria Oliveira',
        resultScheduledDate: '2024-07-28T14:00:00',
        unit: 'Laboratório Central',
        unitId: 'LAB001',
        unitName: 'Unimed Laboratório Central',
      );

      final expectedFormattedDate = DateFormat('dd/MM/yyyy HH:mm')
          .format(DateTime.parse('2024-07-26T10:00:00'));
      expect(model.dateOrderFormatted, expectedFormattedDate);
    });

    test('dateOrderFormatted should handle invalid date format', () {
      final model = ResLabExamModel(
        doctorId: '12345',
        doctorName: 'Dr. João Silva',
        orderDate: 'data-invalida',
        orderId: 'ORD123456',
        orderLabel: 'Hemograma Completo',
        orderStatus: 'Finalizado',
        patientName: 'Maria Oliveira',
        resultScheduledDate: '2024-07-28T14:00:00',
        unit: 'Laboratório Central',
        unitId: 'LAB001',
        unitName: 'Unimed Laboratório Central',
      );

      expect(() => model.dateOrderFormatted, throwsFormatException);
    });
  });
}
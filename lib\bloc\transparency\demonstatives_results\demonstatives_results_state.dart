part of 'demonstatives_results_cubit.dart';

abstract class DemonstrativesResultsState extends Equatable {
  const DemonstrativesResultsState();
}

class InitialDemonstrativesResultsState extends DemonstrativesResultsState {
  @override
  List<Object> get props => [];
}

class LoadingDemonstrativesResultsState extends DemonstrativesResultsState {
  @override
  List<Object> get props => [];
}

class ErrorDemonstrativesResultsState extends DemonstrativesResultsState {
  final String? message;
  @override
  List<Object?> get props => [message];
  const ErrorDemonstrativesResultsState(this.message);
}

class LoadedDemonstrativesResultsState extends DemonstrativesResultsState {
  final DemonstrativoResultadosVO demonstrativoResultados;
  @override
  List<Object> get props => [demonstrativoResultados];

  const LoadedDemonstrativesResultsState(this.demonstrativoResultados);
}

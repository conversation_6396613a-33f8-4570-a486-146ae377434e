class ResDetailExameFisicoModel {
  late String nome;
  late String valor;
  late String unidadeMedida;

  ResDetailExameFisicoModel({
    required this.nome,
    required this.valor,
    required this.unidadeMedida,
  });

  ResDetailExameFisicoModel.fromJson(Map<String, dynamic> json) {
    nome = json['nome'];
    valor = json['valor'];
    unidadeMedida = json['unidadeMedida'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['nome'] = nome;
    data['valor'] = valor;
    data['unidadeMedida'] = unidadeMedida;
    return data;
  }
}

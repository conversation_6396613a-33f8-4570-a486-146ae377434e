import 'package:cooperado_minha_unimed/bloc/indicators/last_production/last_production_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/attendace-comparative/main.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/comparativo-custo/main.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/daily-attendance/main.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/historical-production/main.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/producao-medica/main.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/widgets/comparative-production/view/card_comparative_production.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class IndicadoresScreen extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const IndicadoresScreen({
    super.key,
    required this.analytics,
    required this.observer,
  });

  @override
  IndicadoresScreenState createState() => IndicadoresScreenState();
}

class IndicadoresScreenState extends State<IndicadoresScreen> {
  @override
  void initState() {
    super.initState();
    widget.analytics.logScreenView(
      screenName: 'Meus indicadores',
      screenClass: 'IndicadoresScreen',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text("Meus Indicadores"),
        backgroundColor: CooperadoColors.tealGreenDark,
        actions: [
             Padding(
             padding: const EdgeInsets.only(right: 15.0),
             child: IconButton(
                       icon: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
              builder: (context, state) {
                return Icon(
                  state.isSensitiveDataVisible ? Icons.visibility : Icons.visibility_off,
                );
              },
                       ),
                       onPressed: () {
              context.read<SensitiveDataCubit>().toggleSensitiveDataVisibility();
                       },
                     ),
           ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          Navigator.pushReplacement(
            context,
            FadeRoute(
              page: IndicadoresScreen(
                analytics: widget.analytics,
                observer: widget.observer,
              ),
            ),
          );
        },
        color: CooperadoColors.tealGreen,
        child: SafeArea(
          child: BlocBuilder<LastProductionCubit, LastProductionState>(
              builder: (context, state) {
            if (state is LoadedLastProductionState) {
              return SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    CardComparativoProducao(
                      lastDate: state.lastDate,
                    ),
                    ComparativoCustoScreen(lastDate: state.lastDate),
                    ProducaoMedicaScreen(lastDate: state.lastDate),
                    const DailyAttendanceScreen(),
                    ProductionHistoryScreen(lastDate: state.lastDate),
                    AttendanceComparativeScreen(lastDate: state.lastDate)
                  ],
                ),
              );
            } else if (state is LoadingLastProductionState) {
              return const Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SpinKitCircle(
                    color: CooperadoColors.tealGreen,
                  ),
                ],
              );
            } else if (state is ErrorLastProductionState) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Center(
                    child: ErrorBanner(
                      message: state.message,
                    ),
                  ),
                ],
              );
            }
            return Container();
          }),
        ),
      ),
    );
  }
}

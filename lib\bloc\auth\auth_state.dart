part of 'auth_cubit.dart';

abstract class AuthState extends Equatable {
  const AuthState();
}

class InitialAuthState extends AuthState {
  @override
  List<Object> get props => [];
}

class LoadingAuthState extends AuthState {
  @override
  List<Object> get props => [];
}

class ErrorAuthState extends AuthState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorAuthState(this.message);
}

class LoadedAuthState extends AuthState {
  final User user;

  final GeneralConfigModel generalConfigModel;

  @override
  List<Object> get props => [user];

  const LoadedAuthState({required this.user, required this.generalConfigModel});
}

class LoadingLogoutUserState extends AuthState {
  @override
  List<Object> get props => [];
}

class DoneLogoutUserState extends AuthState {
  @override
  List<Object> get props => [];
}

class ErrorLogoutUserState extends AuthState {
  final String message;
  @override
  List<Object> get props => [message];

  const ErrorLogoutUserState(this.message);
}

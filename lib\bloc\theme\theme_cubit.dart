import 'package:cooperado_minha_unimed/colors.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'theme_state.dart';

class ThemeCubit extends Cubit<ThemeState> {
  ThemeCubit() : super(InitialThemeState());

  ThemeData _themeData = ThemeCooperado.purple();
  ThemeData get themeData => _themeData;

  void setTheme(ThemeData themeData) async {
    _themeData = themeData;
    emit(SpecialThemeState(_themeData));
  }
}

class ChannelEthicsResponsive {
  final Retorno? retorno;
  final String? mensagem;
  final int? ok;

  ChannelEthicsResponsive({
    this.retorno,
    this.mensagem,
    this.ok,
  });

  ChannelEthicsResponsive.fromJson(Map<String, dynamic> json)
      : retorno = (json['retorno'] as Map<String, dynamic>?) != null
            ? Retorno.fromJson(json['retorno'] as Map<String, dynamic>)
            : null,
        mensagem = json['mensagem'] as String?,
        ok = json['ok'] as int?;

  Map<String, dynamic> toJson() =>
      {'retorno': retorno?.toJson(), 'mensagem': mensagem, 'ok': ok};
}

class Retorno {
  final dynamic quantidadeNotificacoes;
  final Page? page;
  final List<dynamic>? posts;
  final dynamic paginas;
  final dynamic galeria;
  final dynamic banners;

  Retorno({
    this.quantidadeNotificacoes,
    this.page,
    this.posts,
    this.paginas,
    this.galeria,
    this.banners,
  });

  Retorno.fromJson(Map<String, dynamic> json)
      : quantidadeNotificacoes = json['quantidadeNotificacoes'],
        page = (json['page'] as Map<String, dynamic>?) != null
            ? Page.fromJson(json['page'] as Map<String, dynamic>)
            : null,
        posts = json['posts'] as List?,
        paginas = json['paginas'],
        galeria = json['galeria'],
        banners = json['banners'];

  Map<String, dynamic> toJson() => {
        'quantidadeNotificacoes': quantidadeNotificacoes,
        'page': page?.toJson(),
        'posts': posts,
        'paginas': paginas,
        'galeria': galeria,
        'banners': banners
      };
}

class Page {
  final int? id;
  final String? titulo;
  final String? status;
  final dynamic destinatario;
  final dynamic assunto;
  final dynamic arquivo;
  final String? url;
  final Seo? seo;
  final dynamic imagem;
  final dynamic pagina;
  final bool? paginado;
  final bool? lido;
  final String? content;
  final List<dynamic>? tipoNoticia;
  final List<dynamic>? tipoNoticiaBlog;
  final List<dynamic>? tags;
  final dynamic postsRelacionados;
  final Data? data;
  final String? excerpt;
  final String? areaRestrita;
  final List<String>? localPublicacao;
  final String? type;
  final CustomFields? customFields;

  Page({
    this.id,
    this.titulo,
    this.status,
    this.destinatario,
    this.assunto,
    this.arquivo,
    this.url,
    this.seo,
    this.imagem,
    this.pagina,
    this.paginado,
    this.lido,
    this.content,
    this.tipoNoticia,
    this.tipoNoticiaBlog,
    this.tags,
    this.postsRelacionados,
    this.data,
    this.excerpt,
    this.areaRestrita,
    this.localPublicacao,
    this.type,
    this.customFields,
  });

  Page.fromJson(Map<String, dynamic> json)
      : id = json['id'] as int?,
        titulo = json['titulo'] as String?,
        status = json['status'] as String?,
        destinatario = json['destinatario'],
        assunto = json['assunto'],
        arquivo = json['arquivo'],
        url = json['url'] as String?,
        seo = (json['seo'] as Map<String, dynamic>?) != null
            ? Seo.fromJson(json['seo'] as Map<String, dynamic>)
            : null,
        imagem = json['imagem'],
        pagina = json['pagina'],
        paginado = json['paginado'] as bool?,
        lido = json['lido'] as bool?,
        content = json['content'] as String?,
        tipoNoticia = json['tipo_noticia'] as List?,
        tipoNoticiaBlog = json['tipo_noticia_blog'] as List?,
        tags = json['tags'] as List?,
        postsRelacionados = json['posts_relacionados'],
        data = (json['data'] as Map<String, dynamic>?) != null
            ? Data.fromJson(json['data'] as Map<String, dynamic>)
            : null,
        excerpt = json['excerpt'] as String?,
        areaRestrita = json['areaRestrita'] as String?,
        localPublicacao = (json['local_publicacao'] as List?)
            ?.map((dynamic e) => e as String)
            .toList(),
        type = json['type'] as String?,
        customFields = (json['custom_fields'] as Map<String, dynamic>?) != null
            ? CustomFields.fromJson(
                json['custom_fields'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() => {
        'id': id,
        'titulo': titulo,
        'status': status,
        'destinatario': destinatario,
        'assunto': assunto,
        'arquivo': arquivo,
        'url': url,
        'seo': seo?.toJson(),
        'imagem': imagem,
        'pagina': pagina,
        'paginado': paginado,
        'lido': lido,
        'content': content,
        'tipo_noticia': tipoNoticia,
        'tipo_noticia_blog': tipoNoticiaBlog,
        'tags': tags,
        'posts_relacionados': postsRelacionados,
        'data': data?.toJson(),
        'excerpt': excerpt,
        'areaRestrita': areaRestrita,
        'local_publicacao': localPublicacao,
        'type': type,
        'custom_fields': customFields?.toJson()
      };
}

class Seo {
  final String? metaDescription;
  final String? metaTitle;
  final String? robots;
  final String? type;
  final String? locale;
  final String? image;

  Seo({
    this.metaDescription,
    this.metaTitle,
    this.robots,
    this.type,
    this.locale,
    this.image,
  });

  Seo.fromJson(Map<String, dynamic> json)
      : metaDescription = json['meta_description'] as String?,
        metaTitle = json['meta_title'] as String?,
        robots = json['robots'] as String?,
        type = json['type'] as String?,
        locale = json['locale'] as String?,
        image = json['image'] as String?;

  Map<String, dynamic> toJson() => {
        'meta_description': metaDescription,
        'meta_title': metaTitle,
        'robots': robots,
        'type': type,
        'locale': locale,
        'image': image
      };
}

class Data {
  final Chronology? chronology;
  final int? era;
  final int? year;
  final int? dayOfMonth;
  final int? dayOfWeek;
  final int? dayOfYear;
  final int? monthOfYear;
  final int? centuryOfEra;
  final int? yearOfEra;
  final int? yearOfCentury;
  final int? weekyear;
  final int? weekOfWeekyear;
  final int? hourOfDay;
  final int? minuteOfHour;
  final int? secondOfMinute;
  final int? millisOfSecond;
  final int? millisOfDay;
  final List<int>? values;
  final List<FieldTypes>? fieldTypes;
  final List<Fields>? fields;

  Data({
    this.chronology,
    this.era,
    this.year,
    this.dayOfMonth,
    this.dayOfWeek,
    this.dayOfYear,
    this.monthOfYear,
    this.centuryOfEra,
    this.yearOfEra,
    this.yearOfCentury,
    this.weekyear,
    this.weekOfWeekyear,
    this.hourOfDay,
    this.minuteOfHour,
    this.secondOfMinute,
    this.millisOfSecond,
    this.millisOfDay,
    this.values,
    this.fieldTypes,
    this.fields,
  });

  Data.fromJson(Map<String, dynamic> json)
      : chronology = (json['chronology'] as Map<String, dynamic>?) != null
            ? Chronology.fromJson(json['chronology'] as Map<String, dynamic>)
            : null,
        era = json['era'] as int?,
        year = json['year'] as int?,
        dayOfMonth = json['dayOfMonth'] as int?,
        dayOfWeek = json['dayOfWeek'] as int?,
        dayOfYear = json['dayOfYear'] as int?,
        monthOfYear = json['monthOfYear'] as int?,
        centuryOfEra = json['centuryOfEra'] as int?,
        yearOfEra = json['yearOfEra'] as int?,
        yearOfCentury = json['yearOfCentury'] as int?,
        weekyear = json['weekyear'] as int?,
        weekOfWeekyear = json['weekOfWeekyear'] as int?,
        hourOfDay = json['hourOfDay'] as int?,
        minuteOfHour = json['minuteOfHour'] as int?,
        secondOfMinute = json['secondOfMinute'] as int?,
        millisOfSecond = json['millisOfSecond'] as int?,
        millisOfDay = json['millisOfDay'] as int?,
        values =
            (json['values'] as List?)?.map((dynamic e) => e as int).toList(),
        fieldTypes = (json['fieldTypes'] as List?)
            ?.map((dynamic e) => FieldTypes.fromJson(e as Map<String, dynamic>))
            .toList(),
        fields = (json['fields'] as List?)
            ?.map((dynamic e) => Fields.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        'chronology': chronology?.toJson(),
        'era': era,
        'year': year,
        'dayOfMonth': dayOfMonth,
        'dayOfWeek': dayOfWeek,
        'dayOfYear': dayOfYear,
        'monthOfYear': monthOfYear,
        'centuryOfEra': centuryOfEra,
        'yearOfEra': yearOfEra,
        'yearOfCentury': yearOfCentury,
        'weekyear': weekyear,
        'weekOfWeekyear': weekOfWeekyear,
        'hourOfDay': hourOfDay,
        'minuteOfHour': minuteOfHour,
        'secondOfMinute': secondOfMinute,
        'millisOfSecond': millisOfSecond,
        'millisOfDay': millisOfDay,
        'values': values,
        'fieldTypes': fieldTypes?.map((e) => e.toJson()).toList(),
        'fields': fields?.map((e) => e.toJson()).toList()
      };
}

class Chronology {
  final Zone? zone;

  Chronology({
    this.zone,
  });

  Chronology.fromJson(Map<String, dynamic> json)
      : zone = (json['zone'] as Map<String, dynamic>?) != null
            ? Zone.fromJson(json['zone'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() => {'zone': zone?.toJson()};
}

class Zone {
  final bool? fixed;
  final String? id;

  Zone({
    this.fixed,
    this.id,
  });

  Zone.fromJson(Map<String, dynamic> json)
      : fixed = json['fixed'] as bool?,
        id = json['id'] as String?;

  Map<String, dynamic> toJson() => {'fixed': fixed, 'id': id};
}

class FieldTypes {
  final DurationType? durationType;
  final dynamic rangeDurationType;
  final String? name;

  FieldTypes({
    this.durationType,
    this.rangeDurationType,
    this.name,
  });

  FieldTypes.fromJson(Map<String, dynamic> json)
      : durationType = (json['durationType'] as Map<String, dynamic>?) != null
            ? DurationType.fromJson(
                json['durationType'] as Map<String, dynamic>)
            : null,
        rangeDurationType = json['rangeDurationType'],
        name = json['name'] as String?;

  Map<String, dynamic> toJson() => {
        'durationType': durationType?.toJson(),
        'rangeDurationType': rangeDurationType,
        'name': name
      };
}

class DurationType {
  final String? name;

  DurationType({
    this.name,
  });

  DurationType.fromJson(Map<String, dynamic> json)
      : name = json['name'] as String?;

  Map<String, dynamic> toJson() => {'name': name};
}

class Fields {
  final bool? lenient;
  final int? minimumValue;
  final int? maximumValue;
  final dynamic rangeDurationField;
  final LeapDurationField? leapDurationField;
  final DurationField? durationField;
  final String? name;
  final TypeModel? type;
  final bool? supported;

  Fields({
    this.lenient,
    this.minimumValue,
    this.maximumValue,
    this.rangeDurationField,
    this.leapDurationField,
    this.durationField,
    this.name,
    this.type,
    this.supported,
  });

  Fields.fromJson(Map<String, dynamic> json)
      : lenient = json['lenient'] as bool?,
        minimumValue = json['minimumValue'] as int?,
        maximumValue = json['maximumValue'] as int?,
        rangeDurationField = json['rangeDurationField'],
        leapDurationField =
            (json['leapDurationField'] as Map<String, dynamic>?) != null
                ? LeapDurationField.fromJson(
                    json['leapDurationField'] as Map<String, dynamic>)
                : null,
        durationField = (json['durationField'] as Map<String, dynamic>?) != null
            ? DurationField.fromJson(
                json['durationField'] as Map<String, dynamic>)
            : null,
        name = json['name'] as String?,
        type = (json['type'] as Map<String, dynamic>?) != null
            ? TypeModel.fromJson(json['type'] as Map<String, dynamic>)
            : null,
        supported = json['supported'] as bool?;

  Map<String, dynamic> toJson() => {
        'lenient': lenient,
        'minimumValue': minimumValue,
        'maximumValue': maximumValue,
        'rangeDurationField': rangeDurationField,
        'leapDurationField': leapDurationField?.toJson(),
        'durationField': durationField?.toJson(),
        'name': name,
        'type': type?.toJson(),
        'supported': supported
      };
}

class LeapDurationField {
  final int? unitMillis;
  final bool? precise;
  final String? name;
  final TypeModel? type;
  final bool? supported;

  LeapDurationField({
    this.unitMillis,
    this.precise,
    this.name,
    this.type,
    this.supported,
  });

  LeapDurationField.fromJson(Map<String, dynamic> json)
      : unitMillis = json['unitMillis'] as int?,
        precise = json['precise'] as bool?,
        name = json['name'] as String?,
        type = (json['type'] as Map<String, dynamic>?) != null
            ? TypeModel.fromJson(json['type'] as Map<String, dynamic>)
            : null,
        supported = json['supported'] as bool?;

  Map<String, dynamic> toJson() => {
        'unitMillis': unitMillis,
        'precise': precise,
        'name': name,
        'type': type?.toJson(),
        'supported': supported
      };
}

class Type {
  final String? name;

  Type({
    this.name,
  });

  Type.fromJson(Map<String, dynamic> json) : name = json['name'] as String?;

  Map<String, dynamic> toJson() => {'name': name};
}

class DurationField {
  final int? unitMillis;
  final bool? precise;
  final String? name;
  final TypoModelName? type;
  final bool? supported;

  DurationField({
    this.unitMillis,
    this.precise,
    this.name,
    this.type,
    this.supported,
  });

  DurationField.fromJson(Map<String, dynamic> json)
      : unitMillis = json['unitMillis'] as int?,
        precise = json['precise'] as bool?,
        name = json['name'] as String?,
        type = (json['type'] as Map<String, dynamic>?) != null
            ? TypoModelName.fromJson(json['type'] as Map<String, dynamic>)
            : null,
        supported = json['supported'] as bool?;

  Map<String, dynamic> toJson() => {
        'unitMillis': unitMillis,
        'precise': precise,
        'name': name,
        'type': type?.toJson(),
        'supported': supported
      };
}

class TypoModelName {
  final String? name;

  TypoModelName({
    this.name,
  });

  TypoModelName.fromJson(Map<String, dynamic> json)
      : name = json['name'] as String?;

  Map<String, dynamic> toJson() => {'name': name};
}

class TypeModel {
  final DurationTypeModel? durationType;
  final dynamic rangeDurationType;
  final String? name;

  TypeModel({
    this.durationType,
    this.rangeDurationType,
    this.name,
  });

  TypeModel.fromJson(Map<String, dynamic> json)
      : durationType = (json['durationType'] as Map<String, dynamic>?) != null
            ? DurationTypeModel.fromJson(
                json['durationType'] as Map<String, dynamic>)
            : null,
        rangeDurationType = json['rangeDurationType'],
        name = json['name'] as String?;

  Map<String, dynamic> toJson() => {
        'durationType': durationType?.toJson(),
        'rangeDurationType': rangeDurationType,
        'name': name
      };
}

class DurationTypeModel {
  final String? name;

  DurationTypeModel({
    this.name,
  });

  DurationTypeModel.fromJson(Map<String, dynamic> json)
      : name = json['name'] as String?;

  Map<String, dynamic> toJson() => {'name': name};
}

class CustomFields {
  final dynamic lotacao;
  final dynamic responsavel;
  final dynamic cargaHoraria;
  final dynamic horarioEntrada;
  final dynamic horarioSaida;
  final dynamic limiteDeEntrega;
  final dynamic escolaridade;
  final dynamic perfilCandidato;
  final dynamic conhecimentosNecessarios;
  final dynamic atividadeDesempenhadas;
  final dynamic emailDoResponsavel;
  final dynamic salarioEBeneficios;
  final String? paginaPersonalizada;
  final dynamic exibirComoHtml;
  final dynamic tipoConteudo;
  final dynamic modeloHeader;
  final dynamic headerSubtitulo;
  final dynamic imagemDoHeader;
  final dynamic exibirSidebar;
  final dynamic escolhaUmaSidebar;
  final dynamic escolhaUmaSidebarPersonalizada;
  final dynamic htmlSidebar;
  final dynamic imagemPersonalizadaSidebar;

  CustomFields({
    this.lotacao,
    this.responsavel,
    this.cargaHoraria,
    this.horarioEntrada,
    this.horarioSaida,
    this.limiteDeEntrega,
    this.escolaridade,
    this.perfilCandidato,
    this.conhecimentosNecessarios,
    this.atividadeDesempenhadas,
    this.emailDoResponsavel,
    this.salarioEBeneficios,
    this.paginaPersonalizada,
    this.exibirComoHtml,
    this.tipoConteudo,
    this.modeloHeader,
    this.headerSubtitulo,
    this.imagemDoHeader,
    this.exibirSidebar,
    this.escolhaUmaSidebar,
    this.escolhaUmaSidebarPersonalizada,
    this.htmlSidebar,
    this.imagemPersonalizadaSidebar,
  });

  CustomFields.fromJson(Map<String, dynamic> json)
      : lotacao = json['lotacao'],
        responsavel = json['responsavel'],
        cargaHoraria = json['carga_horaria'],
        horarioEntrada = json['horario_entrada'],
        horarioSaida = json['horario_saida'],
        limiteDeEntrega = json['limite_de_entrega'],
        escolaridade = json['escolaridade'],
        perfilCandidato = json['perfil_candidato'],
        conhecimentosNecessarios = json['conhecimentos_necessarios'],
        atividadeDesempenhadas = json['atividade_desempenhadas'],
        emailDoResponsavel = json['email_do_responsavel'],
        salarioEBeneficios = json['salario_e_beneficios'],
        paginaPersonalizada = json['pagina_personalizada'] as String?,
        exibirComoHtml = json['exibir_como_html'],
        tipoConteudo = json['tipo_conteudo'],
        modeloHeader = json['modelo_header'],
        headerSubtitulo = json['header_subtitulo'],
        imagemDoHeader = json['imagem_do_header'],
        exibirSidebar = json['exibir_sidebar'],
        escolhaUmaSidebar = json['escolha_uma_sidebar'],
        escolhaUmaSidebarPersonalizada =
            json['escolha_uma_sidebar_personalizada'],
        htmlSidebar = json['html_sidebar'],
        imagemPersonalizadaSidebar = json['imagem_personalizada_sidebar'];

  Map<String, dynamic> toJson() => {
        'lotacao': lotacao,
        'responsavel': responsavel,
        'carga_horaria': cargaHoraria,
        'horario_entrada': horarioEntrada,
        'horario_saida': horarioSaida,
        'limite_de_entrega': limiteDeEntrega,
        'escolaridade': escolaridade,
        'perfil_candidato': perfilCandidato,
        'conhecimentos_necessarios': conhecimentosNecessarios,
        'atividade_desempenhadas': atividadeDesempenhadas,
        'email_do_responsavel': emailDoResponsavel,
        'salario_e_beneficios': salarioEBeneficios,
        'pagina_personalizada': paginaPersonalizada,
        'exibir_como_html': exibirComoHtml,
        'tipo_conteudo': tipoConteudo,
        'modelo_header': modeloHeader,
        'header_subtitulo': headerSubtitulo,
        'imagem_do_header': imagemDoHeader,
        'exibir_sidebar': exibirSidebar,
        'escolha_uma_sidebar': escolhaUmaSidebar,
        'escolha_uma_sidebar_personalizada': escolhaUmaSidebarPersonalizada,
        'html_sidebar': htmlSidebar,
        'imagem_personalizada_sidebar': imagemPersonalizadaSidebar
      };
}

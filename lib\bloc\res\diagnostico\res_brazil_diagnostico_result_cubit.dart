import 'package:cooperado_minha_unimed/bloc/res/diagnostico/res_brasil_diagnostico_result_cubit.state.dart';
import 'package:cooperado_minha_unimed/models/res/res_brasil_diagnostico_model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResBrazilDiagnosticoResultCubit
    extends Cubit<ResBrazilDiagnosticoResultCubitState> {
  ResBrazilDiagnosticoResultCubit() : super(InitialResBrazilDiagnosticoState());

  List<ResBrazilDiagnosticoModel> _listDiagnosticoResult =
      List.empty(growable: true);
  List<ResBrazilDiagnosticoModel> get listDiagnosticoResult =>
      _listDiagnosticoResult;

  void listResBrazilDiagnosticoDetailResult(
      {required String crm, required String card, required String code}) async {
    try {
      emit(LoadingResBrazilDiagnosticoResultState());
      _listDiagnosticoResult = await Locator.instance!<ResGraphQlApi>()
          .resBrazilDetailDiagnosticResult(crm: crm, card: card, code: code);

      if (_listDiagnosticoResult.isEmpty) {
        emit(const NoDataResBrazilDiagnosticoResultState());
      } else {
        emit(LoadedResBrazilDiagnosticoResultState(
            listDiagnostico: listDiagnosticoResult));
      }
    } catch (e) {
      emit(ErroResBrazilDiagnosticoResultState(message: e.toString()));
    }
  }

  void listResBrazilDiagnosticoResult({
    required String crm,
    required String card,
    DateTimeRange? dataRange,
  }) async {
    emit(LoadingResBrazilDiagnosticoResultState());
    try {
      final listDiagnosticos =
          await Locator.instance!<ResGraphQlApi>().resGetAttendanceByType(
        crm: crm,
        card: card,
        type: 'diagnosticos',
        dataRange: dataRange,
      );

      if (listDiagnosticos.isEmpty) {
        emit(const NoDataResBrazilDiagnosticoResultState());
      } else {
        emit(LoadedResBrazilListDiagnosticoResultState(
            listAllDiagnostico: listDiagnosticos));
      }
    } catch (e) {
      emit(ErroResBrazilDiagnosticoResultState(message: e.toString()));
    }
  }
}

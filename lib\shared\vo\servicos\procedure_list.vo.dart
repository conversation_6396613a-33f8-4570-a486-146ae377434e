class ProcedureList {
  int? totalPaginas;
  int? pagina;
  Procedimento? procedimento;
  List<ListaProcedimentos>? listaProcedimentos;

  ProcedureList(
      {this.totalPaginas,
      this.pagina,
      this.procedimento,
      this.listaProcedimentos});

  ProcedureList.fromJson(Map<String, dynamic> json) {
    totalPaginas = json['totalPaginas'];
    pagina = json['pagina'];
    procedimento = json['procedimento'] != null
        ? Procedimento.fromJson(json['procedimento'])
        : null;
    if (json['listaProcedimentos'] != null) {
      listaProcedimentos = [];
      json['listaProcedimentos'].forEach((v) {
        listaProcedimentos!.add(ListaProcedimentos.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['totalPaginas'] = totalPaginas;
    data['pagina'] = pagina;
    if (procedimento != null) {
      data['procedimento'] = procedimento!.toJson();
    }
    if (listaProcedimentos != null) {
      data['listaProcedimentos'] =
          listaProcedimentos!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Procedimento {
  Tipo? tipo;

  Procedimento({this.tipo});

  Procedimento.fromJson(Map<String, dynamic> json) {
    tipo = json['tipo'] != null ? Tipo.fromJson(json['tipo']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (tipo != null) {
      data['tipo'] = tipo!.toJson();
    }
    return data;
  }
}

class Tipo {
  String? descricao;

  Tipo({this.descricao});

  Tipo.fromJson(Map<String, dynamic> json) {
    descricao = json['descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['descricao'] = descricao;
    return data;
  }
}

class ListaProcedimentos {
  int? codigo;
  Tipo? tipo;

  ListaProcedimentos({this.codigo, this.tipo});

  ListaProcedimentos.fromJson(Map<String, dynamic> json) {
    codigo = json['codigo'];
    tipo = json['tipo'] != null ? Tipo.fromJson(json['tipo']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codigo'] = codigo;
    if (tipo != null) {
      data['tipo'] = tipo!.toJson();
    }
    return data;
  }
}

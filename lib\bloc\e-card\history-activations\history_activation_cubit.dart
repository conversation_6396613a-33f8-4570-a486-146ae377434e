import 'package:cooperado_minha_unimed/bloc/e-card/history-activations/history_activation_state.dart';
import 'package:cooperado_minha_unimed/models/ecard-models/history_transaction_model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphqlecard.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class HistoryEcardActivationCubit extends Cubit<HistoryEcardActivationState> {
  HistoryEcardActivationCubit() : super(InitialHistoryActivationState());

  List<HistoryTransactionEcardModel> _listHistoryActivation = [];
  List<HistoryTransactionEcardModel> get listHistoryActivation => _listHistoryActivation;

  void historyEcard({
    required String codPrestador,
    required DateTime startDate,
    required DateTime endDate,
    List<String>? locations,
  }) async {
    try {
      emit(LoadingHistoryActivationState());

      var result = await Locator.instance!.get<GraphQlApiEcard>().historyEcardActivation(
            codPrestador: codPrestador,
            startDate: startDate,
            endDate: endDate,
          );

      if (result != null) {
        _listHistoryActivation = result;
      }

      // para filtrar quando localidade for passada
      if (locations != null && locations.isNotEmpty) {
        _listHistoryActivation = _listHistoryActivation.where((element) {
          return locations.contains(element.viewAddress?.streetName);
        }).toList();
      }

      if (_listHistoryActivation.isEmpty) {
        emit(NoDataHistoryActivationState());
        return;
      }

      emit(LoadedHistoryActivationState(listHistoryActivarion: _listHistoryActivation));
    } catch (e) {
      emit(ErrorHistoryActivationState(message: e.toString()));
    }
  }
}

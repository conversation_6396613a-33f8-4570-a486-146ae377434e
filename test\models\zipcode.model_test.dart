import 'package:cooperado_minha_unimed/models/zipcode.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  ZipCodeModel? modelTest;
  Map? json;
  setUpAll(
    () {
      modelTest = ZipCodeModel(adressZipCodeModel: [
        AddressZipCodeModel(
          cep: 78800000,
          codBairro: 0,
          codCidade: 1,
          codLogradouro: 0,
          codTipoLogradouro: '',
          codUf: '',
          complementoLogradouro: '',
          nomeBairro: '',
          nomeCidade: '',
          nomeLogradouro: '',
          nomeTipoLogradouro: '',
          nomeUf: '',
        )
      ]);
      json = {
        "retorno": [
          {
            "cep": 78800000,
            "codUf": "MT",
            "codCidade": 1,
            "nomeCidade": "Cuiaba",
            "codLogradouro": 0,
            "codTipoLogradouro": "",
            "nomeTipoLogradouro": "Av",
            "nomeLogradouro": "<PERSON>. silvester",
            "codBairro": 0,
            "nomeBairro": "Goiabeiras",
            "nomeUf": "Mato Grosso",
            "complementoLogradouro": ""
          }
        ]
      };
    },
  );

  group(
    "isInstanceOf ZipCodeModel model tests",
    () {
      test("Should be return instance of ZipCodeModel", () {
        expect(modelTest, isInstanceOf<ZipCodeModel>());
      });

      test("Should be return instance of String", () {
        expect(modelTest!.adressZipCodeModel![0].nomeTipoLogradouro!,
            isInstanceOf<String>());
      });

      test("Should be return instance of Int", () {
        expect(modelTest!.adressZipCodeModel![0].cep!, isInstanceOf<int>());
      });
      test("Should be return instance of AddressZipCodeModel", () {
        expect(modelTest!.adressZipCodeModel![0],
            isInstanceOf<AddressZipCodeModel>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of ZipCodeModel to json", () {
      expect(modelTest!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });

    test("Should be return instance of AddressZipCodeModel to json", () {
      expect(modelTest!.adressZipCodeModel![0].toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of AddressZipCodeModel from json", () {
      expect(AddressZipCodeModel.fromJson(json!),
          isInstanceOf<AddressZipCodeModel>());
    });
  });
  group(
    "Other tests",
    () {
      test("Should be return length 8", () {
        expect(modelTest!.adressZipCodeModel![0].cep.toString().length, 8);
      });

      test("Should be return cep length 8", () {
        expect(json!['retorno'][0]['cep'].toString().length, 8);
      });
      test("Should be return codUf length 2", () {
        expect(json!['retorno'][0]['codUf'].toString().length, 2);
      });
      test("Should be return cep != 00000000", () {
        expect(json!['retorno'][0]['cep'] != "00000000", true);
      });
      test("Can´t return if is null", () {
        expect(json!['retorno'][0]['cep'] == null, false);
        expect(json!['retorno'][0]['codUf'] == null, false);
        expect(json!['retorno'][0]['codCidade'] == null, false);
        expect(
            json!['retorno'][0]['nomeCidade'] == null ||
                json!['retorno'][0]['nomeCidade'] == "",
            false);
        expect(json!['retorno'][0]['nomeTipoLogradouro'] == null, false);
        expect(
            json!['retorno'][0]['nomeLogradouro'] == null ||
                json!['retorno'][0]['nomeLogradouro'] == "",
            false);
        expect(json!['retorno'][0]['codBairro'] == null, false);
        expect(json!['retorno'][0]['nomeBairro'] == null, false);
        expect(
            json!['retorno'][0]['nomeUf'] == null ||
                json!['retorno'][0]['nomeUf'] == "",
            false);
        expect(json!['retorno'][0]['complementoLogradouro'] == null, false);
        expect(json!['retorno'] == null, false);
      });
      test("test type json", () {
        expect(json!['retorno'][0]['cep'], isInstanceOf<int>());
        expect(json!['retorno'][0]['codUf'], isInstanceOf<String>());
        expect(json!['retorno'][0]['codCidade'], isInstanceOf<int>());
        expect(json!['retorno'][0]['nomeCidade'], isInstanceOf<String>());
        expect(
            json!['retorno'][0]['nomeTipoLogradouro'], isInstanceOf<String>());
        expect(json!['retorno'][0]['nomeLogradouro'], isInstanceOf<String>());
        expect(json!['retorno'][0]['codBairro'], isInstanceOf<int>());
        expect(json!['retorno'][0]['nomeBairro'], isInstanceOf<String>());
        expect(json!['retorno'][0]['nomeUf'], isInstanceOf<String>());
        expect(json!['retorno'][0]['complementoLogradouro'],
            isInstanceOf<String>());
        expect(json!['retorno'], isInstanceOf<List<Object>>());
      });
    },
  );

  group("Json test errors", () {
    Map<dynamic, dynamic> jsonError = {
      "retorno": [
        {
          "cep": "78800-000",
          "codUf": "",
          "codCidade": 1,
          "nomeCidade": "Cuiaba",
          "codLogradouro": 0,
          "codTipoLogradouro": "",
          "nomeTipoLogradouro": "Av",
          "nomeLogradouro": "S. silvester",
          "codBairro": 0,
          "nomeBairro": "Goiabeiras",
          "nomeUf": "MT",
          "complementoLogradouro": ""
        }
      ]
    };

    test(
        "Should be return an error 'type 'String' is not a subtype of type 'int?' in type cast",
        () {
      try {
        ZipCodeModel.fromJson(jsonError);
      } catch (e) {
        expect(e.toString(),
            "type 'String' is not a subtype of type 'int?' in type cast");

        jsonError['retorno'][0]['cep'] = int.parse(
            jsonError['retorno'][0]['cep'].replaceAll(RegExp(r'[^\w\s]+'), ''));
        expect(jsonError['retorno'][0]['cep'].toString().length, 8);
        expect(jsonError['retorno'][0]['nomeUf'].toString().length, 2);
        expect(modelTest!.adressZipCodeModel![0].toJson(),
            isInstanceOf<Map<dynamic, dynamic>>());
      }
    });
  });
}

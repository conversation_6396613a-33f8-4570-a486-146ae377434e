import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/widgets/month_picker.dart';
import 'package:flutter/material.dart';

class ChooseDateNewWidget extends StatefulWidget {
  final DateTime? date;
  final Widget textBox;
  final Function(DateTime?)? onPressed;

  const ChooseDateNewWidget(
      {super.key, required this.textBox, required this.onPressed, this.date});
  @override
  ChooseDateNewWidgetState createState() => ChooseDateNewWidgetState();
}

class ChooseDateNewWidgetState extends State<ChooseDateNewWidget> {
  @override
  Widget build(BuildContext context) {
    final DateTime now = DateTime.now();
    return InkWell(
      onTap: () {
        showMonthPicker(
          context: context,
          initialDate: widget.date ?? now,
          lastDate: DateTime(now.year, now.month - 1),
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
        decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(5)),
            color: Colors.grey.withOpacity(0.2)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(5)),
                color: Colors.white,
              ),
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
              child: const Row(
                children: [
                  Icon(
                    Icons.calendar_today_sharp,
                    color: CooperadoColors.tealGreen,
                  ),
                  SizedBox(
                    width: 10,
                  ),
                  Text(
                    'Buscar',
                    style: TextStyle(color: CooperadoColors.grayDark),
                  )
                ],
              ),
            ),
            const SizedBox(),
            widget.textBox,
            const Icon(
              Icons.arrow_drop_down,
              size: 40,
            ),
          ],
        ),
      ),
    );
  }

  Future<DateTime?> showMonthPicker({
    required BuildContext context,
    required DateTime initialDate,
    DateTime? firstDate,
    DateTime? lastDate,
  }) async {
    return await showDialog<DateTime>(
        context: context,
        builder: (context) => MonthPickerCustom(
              initialDate: initialDate,
              firstDate: firstDate,
              lastDate: lastDate,
              onPressed: widget.onPressed,
            ));
  }
}

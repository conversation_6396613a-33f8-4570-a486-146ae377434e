class OfficeNoticeModel {
  final int? codNotificacao;
  final int? codUnimed;
  final int? codPrestador;
  final int? codPreSolic;
  final String? nomeBeneficiario;
  final int? unimedCarteira;
  final int? codCarteira;
  final String? dvCarteira;
  final int? codTipoNotificacao;
  final String? descTipoNotificacao;
  final int? codTipoDestNotif;
  final String? notificacao;
  final String? dataNotificacao;
  final String? dataLeitura;
  final String? dataOcultacao;
  final bool? exibirRelatorio;
  final String? indicacaoClinicaGuia;
  final String? justificativaGuia;
  final String? codCid;
  final int? codHospital;
  final int? quantidadeDiarias;
  final int? codRegimeInternacao;
  final int? codTipoInternacao;
  final bool? possuiPendencia;

  OfficeNoticeModel({
    this.codNotificacao,
    this.codUnimed,
    this.codPrestador,
    this.codPreSolic,
    this.nomeBeneficiario,
    this.unimedCarteira,
    this.codCarteira,
    this.dvCarteira,
    this.codTipoNotificacao,
    this.descTipoNotificacao,
    this.codTipoDestNotif,
    this.notificacao,
    this.dataNotificacao,
    this.dataLeitura,
    this.dataOcultacao,
    this.exibirRelatorio,
    this.indicacaoClinicaGuia,
    this.justificativaGuia,
    this.codCid,
    this.codHospital,
    this.quantidadeDiarias,
    this.codRegimeInternacao,
    this.codTipoInternacao,
    this.possuiPendencia,
  });

  OfficeNoticeModel.fromJson(Map<String, dynamic> json)
      : codNotificacao = json['codNotificacao'] as int?,
        codUnimed = json['codUnimed'] as int?,
        codPrestador = json['codPrestador'] as int?,
        codPreSolic = json['codPreSolic'] as int?,
        nomeBeneficiario = json['nomeBeneficiario'] as String?,
        unimedCarteira = json['unimedCarteira'] as int?,
        codCarteira = json['codCarteira'] as int?,
        dvCarteira = json['dvCarteira'] as String?,
        codTipoNotificacao = json['codTipoNotificacao'] as int?,
        descTipoNotificacao = json['descTipoNotificacao'] as String?,
        codTipoDestNotif = json['codTipoDestNotif'] as int?,
        notificacao = json['notificacao'] as String?,
        dataNotificacao = json['dataNotificacao'] as String?,
        dataLeitura = json['dataLeitura'] as String?,
        dataOcultacao = json['dataOcultacao'] as String?,
        exibirRelatorio = json['exibirRelatorio'] as bool?,
        indicacaoClinicaGuia = json['indicacaoClinicaGuia'] as String?,
        justificativaGuia = json['justificativaGuia'] as String?,
        codCid = json['codCid'] as String?,
        codHospital = json['codHospital'] as int?,
        quantidadeDiarias = json['quantidadeDiarias'] as int?,
        codRegimeInternacao = json['codRegimeInternacao'] as int?,
        codTipoInternacao = json['codTipoInternacao'] as int?,
        possuiPendencia = json['possuiPendencia'] as bool?;

  Map<String, dynamic> toJson() => {
        'codNotificacao': codNotificacao,
        'codUnimed': codUnimed,
        'codPrestador': codPrestador,
        'codPreSolic': codPreSolic,
        'nomeBeneficiario': nomeBeneficiario,
        'unimedCarteira': unimedCarteira,
        'codCarteira': codCarteira,
        'dvCarteira': dvCarteira,
        'codTipoNotificacao': codTipoNotificacao,
        'descTipoNotificacao': descTipoNotificacao,
        'codTipoDestNotif': codTipoDestNotif,
        'notificacao': notificacao,
        'dataNotificacao': dataNotificacao,
        'dataLeitura': dataLeitura,
        'dataOcultacao': dataOcultacao,
        'exibirRelatorio': exibirRelatorio,
        'indicacaoClinicaGuia': indicacaoClinicaGuia,
        'justificativaGuia': justificativaGuia,
        'codCid': codCid,
        'codHospital': codHospital,
        'quantidadeDiarias': quantidadeDiarias,
        'codRegimeInternacao': codRegimeInternacao,
        'codTipoInternacao': codTipoInternacao,
        'possuiPendencia': possuiPendencia
      };
}

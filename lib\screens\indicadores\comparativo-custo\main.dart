import 'package:cooperado_minha_unimed/bloc/indicators/cost_comparative/cost_comparative_cubit.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/comparativo-custo/card_comparativo_custo.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ComparativoCustoScreen extends StatefulWidget {
  final DateTime lastDate;
  const ComparativoCustoScreen({super.key, required this.lastDate});

  @override
  ComparativoCustoScreenState createState() => ComparativoCustoScreenState();
}

class ComparativoCustoScreenState extends State<ComparativoCustoScreen> {
  DateTime? lastProduction;

  @override
  void initState() {
    _selectDate();
    super.initState();
  }

  void _selectDate() {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      lastProduction = widget.lastDate;
      context.read<CostComparativeCubit>().selectDate(lastProduction);
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CostComparativeCubit, CostComparativeState>(
      listener: (context, state) {
        if (state is CostComparativeSelectDate) {
          context
              .read<CostComparativeCubit>()
              .getCostComparative(state.date!, lastProduction!);
        }
      },
      child: CardComparativoCusto(
        refresh: () => _selectDate(),
      ),
    );
  }
}

import 'package:flutter_test/flutter_test.dart';
import 'package:cooperado_minha_unimed/models/medicine.model.dart';

void main() {
  group('MedicineModel', () {
    test('Deve criar uma instância correta a partir de JSON', () {
      final json = {
        'codProduto': '12345',
        'descricaoProduto': 'Medicamento A',
        'codigoUnidade': '1',
        'descricaoUnidade': 'Unidade 1',
        'descricaoClasse': 'Classe A',
        'valorFormatadoUltimaEntrada': 'R\$ 150,00',
        'descricaoSubstancia': 'Substância A',
        'dataUltimaEntrada': '2024-09-17',
        'createdAt': '2024-09-01',
      };

      final medicine = MedicineModel.fromJson(json);

      expect(medicine.codProduto, '12345');
      expect(medicine.descricaoProduto, 'Medicamento A');
      expect(medicine.codigoUnidade, '1');
      expect(medicine.descricaoUnidade, 'Unidade 1');
      expect(medicine.descricaoClasse, 'Classe A');
      expect(medicine.valorFormatadoUltimaEntrada, 'R\$ 150,00');
      expect(medicine.descricaoSubstancia, 'Substância A');
      expect(medicine.dataUltimaEntrada, '2024-09-17');
      expect(medicine.createdAt, '2024-09-01');
    });

    test('Deve converter a instância correta para JSON', () {
      final medicine = MedicineModel(
        codProduto: '12345',
        descricaoProduto: 'Medicamento A',
        codigoUnidade: '1',
        descricaoUnidade: 'Unidade 1',
        descricaoClasse: 'Classe A',
        valorFormatadoUltimaEntrada: 'R\$ 150,00',
        descricaoSubstancia: 'Substância A',
        dataUltimaEntrada: '2024-09-17',
        createdAt: '2024-09-01',
      );

      final json = medicine.toJson();

      expect(json['codProduto'], '12345');
      expect(json['descricaoProduto'], 'Medicamento A');
      expect(json['codigoUnidade'], '1');
      expect(json['descricaoUnidade'], 'Unidade 1');
      expect(json['descricaoClasse'], 'Classe A');
      expect(json['valorFormatadoUltimaEntrada'], 'R\$ 150,00');
      expect(json['descricaoSubstancia'], 'Substância A');
      expect(json['dataUltimaEntrada'], '2024-09-17');
      expect(json['createdAt'], '2024-09-01');
    });

    test('Deve formatar valor da última entrada corretamente', () {
      final medicine = MedicineModel(
        valorFormatadoUltimaEntrada: 'R\$ 150.00',
      );

      expect(medicine.valorUltimaEntrada, 150.0);
    });

    test('Deve retornar null quando valorFormatadoUltimaEntrada é null', () {
      final medicine = MedicineModel(
        valorFormatadoUltimaEntrada: null,
      );

      expect(medicine.valorUltimaEntrada, isNull);
    });

    test('Deve retornar a data formatada corretamente', () {
      final medicine = MedicineModel(
        dataUltimaEntrada: '2024-09-17',
      );

      expect(medicine.dataUltimaEntradaFormatada, '17/09/2024');
    });

    test('Deve retornar null para dataUltimaEntradaFormatada quando a data for inválida', () {
      final medicine = MedicineModel(
        dataUltimaEntrada: 'data-invalida',
      );

      expect(medicine.dataUltimaEntradaFormatada, isNull);
    });

    test('Deve retornar null para dataUltimaEntradaFormatada quando a data for null', () {
      final medicine = MedicineModel(
        dataUltimaEntrada: null,
      );

      expect(medicine.dataUltimaEntradaFormatada, isNull);
    });

    // test('Deve retornar null para valorUltimaEntradaFormatado quando o valor é inválido', () {
    //   final medicine = MedicineModel(
    //     valorFormatadoUltimaEntrada: 'valor-invalido',
    //   );

    //   expect(medicine.valorUltimaEntradaFormatado, isNull);
    // });
  });
}

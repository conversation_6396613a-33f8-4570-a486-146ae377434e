import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/allerts/res_allerts_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/app_bar_res.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/empty_list.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/erro_service.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/expandable_card.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/filters_widget.dart';
import 'package:cooperado_minha_unimed/shared/utils/router_observer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:intl/intl.dart';

class ResAlertsScreen extends StatefulWidget {
  const ResAlertsScreen({super.key, required this.nameBeneficiary, required this.beneficiaryCard});
  final String nameBeneficiary;
  final String beneficiaryCard;

  @override
  State<ResAlertsScreen> createState() => _ResAlertsScreenState();
}

class _ResAlertsScreenState extends State<ResAlertsScreen> with RouteAware {
  final bool _isLoading = false;
  bool loadingDetail = false;
  int? _filterMonthSelected;
  DateTimeRange? _dateRangeToFilter;
  final List<int> _lastMonthsToFilter = [3, 6];

  List<ExpansionTileController> _controllers = [];
  int? _previusSelectedIndex;

  void _loadAllerts() {
    setState(() {
      _controllers = [];
      _previusSelectedIndex = null;
    });
    context.read<ResBrazilAllertsCubit>().listResAllerts(
          crm: context.read<AuthCubit>().credentials.crm,
          card: widget.beneficiaryCard,
          dataRange: _dateRangeToFilter,
        );
  }

  @override
  void initState() {
    _loadAllerts();
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarRes(
        title: 'Alertas',
        nameBeneficiary: widget.nameBeneficiary,
      ),
      backgroundColor: CooperadoColors.backgroundWhiteColor,
      body: Padding(
        padding: const EdgeInsets.only(left: 20, right: 20, top: 10),
        child: Column(
          children: <Widget>[
            _filterDate(),
            BlocBuilder<ResBrazilAllertsCubit, ResBrazilAllertsState>(
              builder: (context, state) {
                if (state is LoadingResAllertsState) {
                  return const Expanded(
                    child: Center(
                      child: SpinKitCircle(color: CooperadoColors.tealGreen),
                    ),
                  );
                } else if (state is LoadedResAllertsState) {
                  return Expanded(
                    child: RefreshIndicator(
                      onRefresh: () async {
                        _loadAllerts();
                      },
                      child: ListView.builder(
                        physics: const ClampingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                        itemCount: state.listAllerts.length,
                        itemBuilder: (context, index) {
                          ExpansionTileController controller = ExpansionTileController();

                          _controllers.add(controller);
                          return Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: ExpandableCard(
                              controller: controller,
                              pathIcon: 'assets/svg/icon_allert.svg',
                              title: state.listAllerts[index].typeOrigin,
                              subtitle: state.listAllerts[index].dateFormatted,
                              loading: loadingDetail,
                              onExpansionChanged: (value) {
                                setState(() {
                                  if (value && _previusSelectedIndex != index) {
                                    if (_previusSelectedIndex != null) {
                                      _controllers[_previusSelectedIndex!].collapse();
                                    }
                                    _previusSelectedIndex = index;
                                  }
                                });
                              },
                              additionalInfo: [
                                {
                                  'title': 'Tipo',
                                  'description': state.listAllerts[index].typeOrigin,
                                },
                                {
                                  'title': 'Origem',
                                  'description': state.listAllerts[index].origin,
                                },
                                {
                                  'title': 'Alerta',
                                  'description': state.listAllerts[index].alert,
                                },
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  );
                } else if (state is NoDataResAllertsState) {
                  return Expanded(
                    child: EmptyList(
                      pathIcon: 'assets/svg/icon_file.svg',
                      message: _dateRangeToFilter != null ? 'Não foi encontrado nenhum alerta para o período de ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.start)} até  ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.end)}' : 'Não foi encontrado nenhum alerta.',
                    ),
                  );
                } else if (state is ErrorResAllertsState) {
                  return Expanded(
                    child: Center(
                      child: ErroService(
                        message: state.message,
                        onPressed: () {
                          _loadAllerts();
                        },
                      ),
                    ),
                  );
                }
                return Container();
              },
            ),
          ],
        ),
      ),
    );
  }

    DateTime _getFirstDate() {
  DateTime now = DateTime.now();
  int year = now.year;
  bool isLeapYear = (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
  return now.subtract(Duration(days: isLeapYear ? 366 : 365));
}

  Future<DateTime?> _selectDateToFilter(BuildContext context, String helpText, {DateTime? firstDate}) async {
    return await showDatePicker(
      context: context,
      helpText: helpText,
      initialDate: DateTime.now(),
      firstDate: firstDate ?? _getFirstDate(),
      lastDate: DateTime.now(),
      keyboardType: TextInputType.text,
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: const ColorScheme.light(
              primary: CooperadoColors.tealGreen,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );
  }

  Widget _filterDate() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(
            top: 15,
            bottom: 20,
          ),
          child: FiltersWidget(
            isLoading: _isLoading,
            lastMonthsToFilter: _lastMonthsToFilter,
            filterMonthSelected: _filterMonthSelected,
            dateRangeToFilter: _dateRangeToFilter,
            onMonthFilterChanged: (filterSelected) {
              setState(() {
                _filterMonthSelected = filterSelected;
                debugPrint('filterSelected: $filterSelected');
                filterSelected == null
                    ? _dateRangeToFilter = null
                    : _dateRangeToFilter = DateTimeRange(
                        start: _selectedDataStart(filterSelected),
                        end: DateTime.now(),
                      );
              });

              _loadAllerts();
            },
            onClearDateRange: () {
              setState(() {
                _dateRangeToFilter = null;
                _filterMonthSelected = null;
                _loadAllerts();
              });
            },
            selectDateToFilter: _selectDateToFilter,
            onDateRangeSelected: (dateRange) {
              setState(() {
                _filterMonthSelected = null;
                _dateRangeToFilter = dateRange;

                _loadAllerts();
              });
            },
          ),
        ),
      ],
    );
  }

  DateTime _selectedDataStart(int meses) {
    DateTime dataAtual = DateTime.now();
    DateTime novaData = DateTime(
      dataAtual.year,
      dataAtual.month - meses,
      dataAtual.day,
    );

    return novaData;
  }
}

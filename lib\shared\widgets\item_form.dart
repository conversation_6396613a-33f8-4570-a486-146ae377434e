import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ItemForm extends StatefulWidget {
  final String title;
  final FormFieldValidator<String>? validator;
  final TextEditingController controller;
  final FocusNode? focusNode;
  final bool next;
  final Function? submit;
  final List<TextInputFormatter>? formatters;
  final bool isPassword;
  final TextInputType type;
  final int? maxLength;
  const ItemForm({
    super.key,
    required this.title,
    required this.validator,
    required this.controller,
    required this.focusNode,
    required this.next,
    this.isPassword = false,
    this.type = TextInputType.text,
    this.formatters,
    this.submit,
    this.maxLength,
  });
  @override
  ItemFormState createState() => ItemFormState();
}

class ItemFormState extends State<ItemForm> {
  bool _hidePassword = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(widget.title),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: TextFormField(
              textAlign: TextAlign.left,
              keyboardType: widget.type,
              textAlignVertical: TextAlignVertical.top,
              controller: widget.controller,
              inputFormatters: widget.formatters,
              focusNode: widget.focusNode,
              obscureText: widget.isPassword && _hidePassword,
              textInputAction:
                  widget.next ? TextInputAction.next : TextInputAction.done,
              onFieldSubmitted: !widget.next
                  ? (term) {
                      widget.submit!();
                    }
                  : null,
              validator: widget.validator,
              maxLength: widget.maxLength,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              decoration: InputDecoration(
                counterText: '',
                focusedBorder: const OutlineInputBorder(
                  borderSide: BorderSide(color: CooperadoColors.tealGreen),
                ),
                border: const OutlineInputBorder(),
                contentPadding:
                    const EdgeInsets.fromLTRB(20.0, 15.0, 20.0, 15.0),
                hintText: widget.title,
                suffixIcon: !widget.isPassword
                    ? null
                    : IconButton(
                        icon: _hidePassword
                            ? const Icon(
                                Icons.visibility_off,
                                color: CooperadoColors.grayDark,
                              )
                            : const Icon(
                                Icons.remove_red_eye,
                                color: unimedGreen,
                              ),
                        onPressed: () async {
                          setState(() {
                            _hidePassword = !_hidePassword;
                          });
                        },
                      ),
              ),
              style: const TextStyle(color: CooperadoColors.tealGreenSecondary),
            ),
          ),
        ],
      ),
    );
  }
}

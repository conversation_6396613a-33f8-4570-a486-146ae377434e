import 'dart:convert';
import 'dart:io';

import 'package:collection/collection.dart' show IterableExtension;
import 'package:http/io_client.dart';

String? _buildNumber;
const GOOGLE_CHAT_WEBHOOK =
    "https://chat.googleapis.com/v1/spaces/AAAABtFiU_o/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=QY6ZaaIgvOX-mCZIyqYY9omkdqT-Ud_2RDCSVcGnBXg";
const APP_ID_ANDROID = '1:1009540451480:android:b3a15ae6bb4e171bd414ed';

IOClient _bypassInvalidCertificate() {
  final ioc = new HttpClient();
  ioc.badCertificateCallback =
      (X509Certificate cert, String host, int port) => true;
  return IOClient(ioc);
}

Future<String?> _getBuildNumber() async {
  _buildNumber = null;

  List<String> lines =
      await new File('./android/local.properties').readAsLines();

  String? lineSelected;

  lineSelected = lines.firstWhereOrNull(
      (element) => element.indexOf('flutter.versionCode') >= 0);

  if (lineSelected != null) {
    _buildNumber = lineSelected.split("=")[1];
  }

  return _buildNumber;
}

void _sendGoogleChatMessage(String message) async {
  final httpClient = _bypassInvalidCertificate();

  try {
    final response = await httpClient.post(
      Uri.parse(GOOGLE_CHAT_WEBHOOK),
      body: jsonEncode({"text": message}),
      headers: {"Content-Type": "application/json"},
      encoding: Encoding.getByName('utf-8'),
    );
    print('google chat send status ${response.statusCode}');
  } catch (ex) {
    print('Error send google chat message $ex');
  }
}

void _uploadFirebase() {
  final _parameters = [
    'appdistribution:distribute',
    'build/app/outputs/flutter-apk/app-sobral-release.apk',
    '--app',
    APP_ID_ANDROID,
    '--release-notes',
    'Versao PROD',
    '--groups',
    'testers-prod'
    // '--testers',
    // '<EMAIL>'
  ];
  // firebase appdistribution:distribute build/app/outputs/apk/release/app-release.apk --app 1:1031315899788:android:48edd931a685a61c --release-notes "Versão deploy by firebase cli" --groups "usuarios"
  Process.start('firebase', _parameters).then((Process process) {
    process.exitCode.then((exitCode) {
      print('exit code: $exitCode');

      final message =
          "Cooperado Minha Unimed SOBRAL - build $_buildNumber\n\nEnviado com sucesso.";

      _sendGoogleChatMessage(message);
    });

    process.stderr.transform(utf8.decoder).listen((data) {
      print(data);
    });

    process.stdout.transform(utf8.decoder).listen((data) {
      print(data);
    });
  }).catchError((onError) {
    print('onError $onError');
  });
}

void _buildApp(List<String> args) {
  final message = "Cooperado Minha Unimed SOBRAL \n\Buildando o projeto...";

  _sendGoogleChatMessage(message);

  Process.start('flutter', [
    'build',
    'apk',
    '--flavor',
    'sobral',
    '-t',
    'lib/main_sobral_prod.dart',
    '--dart-define=CLIENT_ID=UNIMED_SOBRAL',
    '--verbose',
    '--release',
    ...args,
  ]).then((Process process) {
    process.exitCode.then((exitCode) {
      print('exit code: $exitCode');

      _getBuildNumber().then((buildNumber) {
        if (buildNumber != null) {
          print('Criando versão $buildNumber');
          final message =
              "Cooperado Minha Unimed SOBRAL - build $buildNumber\n\nEnviando para Firebase...";
          _sendGoogleChatMessage(message);
        }

        _uploadFirebase();
      }).catchError((onError) {
        print('Error get buildNumber $onError');
      });
    });
    process.stdout.transform(utf8.decoder).listen((data) {
      print(data);
    });

    process.stderr.transform(utf8.decoder).listen((data) {
      print(data);
    });
  }).catchError((onError) {
    print('onError $onError');
  });
}

void main(List<String> args) {
  _buildApp(args);
}

import 'package:cooperado_minha_unimed/models/address.model.dart';
import 'package:cooperado_minha_unimed/models/zipcode.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  AddressModel? addressModel;
  Map? addressJson;

  setUpAll(
    () {
      addressModel = AddressModel(
        code: 1,
        complement: 'complement',
        contacts: [
          ContactModel(
            contact: 'contact',
            type: TypeContactModel(
              code: 1,
              name: 'Name',
            ),
          ),
        ],
        correspondence: 'correspondence',
        number: 'number',
        type: 'type',
        zipcode: ZipCodeModel(adressZipCodeModel: [
          AddressZipCodeModel(
              cep: 60150160,
              codUf: "CE",
              codCidade: 9533,
              nomeCidade: "FORTALEZA",
              codLogradouro: 79099,
              codTipoLogradouro: "AV",
              nomeTipoLogradouro: "AVENIDA",
              nomeLogradouro: "SANTOS DUMONT",
              codBairro: 20,
              nomeBairro: "CENTRO",
              nomeUf: "CEARA ",
              complementoLogradouro: "ATE 978 979"),
        ]),
      );

      addressJson = {
        "codigo": 1,
        "tipo": "type",
        "numero": "number",
        "complemento": "complement",
        "correspondencia": "correspondence",
        "cep": {
          "endereco": [
            {
              "cep": 60150160,
              "codUf": "CE",
              "codCidade": 9533,
              "nomeCidade": "FORTALEZA",
              "codLogradouro": 79099,
              "codTipoLogradouro": "AV",
              "nomeTipoLogradouro": "AVENIDA",
              "nomeLogradouro": "SANTOS DUMONT",
              "codBairro": 20,
              "nomeBairro": "CENTRO",
              "nomeUf": "CEARA ",
              "complementoLogradouro": "ATE 978 979"
            }
          ]
        },
        "contatos": [
          {
            "contato": "contact",
            "tipo": {"codigo": 1, "nome": "Name"}
          }
        ]
      };
    },
  );

  group(
    "Type AddressModel model tests",
    () {
      test("Should be return instance of AddressModel", () {
        expect(addressModel, isInstanceOf<AddressModel>());
      });
      test("Should be return instance of AddressModel to json", () {
        expect(addressModel!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
      });
      test("Should be return instance of AddressModel from json", () {
        expect(
            AddressModel.fromJson(addressJson!), isInstanceOf<AddressModel>());
      });
      test("Should be return instance of ContactModel to json", () {
        expect(addressModel!.contacts![0].toJson(),
            isInstanceOf<Map<dynamic, dynamic>>());
      });
      test("Should be return instance of TypeContactModel to json", () {
        expect(addressModel!.contacts![0].type!.toJson(),
            isInstanceOf<Map<dynamic, dynamic>>());
      });
      test("Should be return instance of ContactModel", () {
        expect(addressModel!.contacts![0], isInstanceOf<ContactModel>());
      });
      test("Should be return instance of TypeContactModel", () {
        expect(
            addressModel!.contacts![0].type, isInstanceOf<TypeContactModel>());
      });

      test("Should be return instance of city ZipCodeModel", () {
        expect(addressModel!.zipcode, isInstanceOf<ZipCodeModel>());
      });
      test("Should be return instance of AddressZipCodeModel", () {
        expect(addressModel!.zipcode!.adressZipCodeModel![0],
            isInstanceOf<AddressZipCodeModel>());
      });
      test("Should be return instance of int", () {
        expect(addressModel!.zipcode!.adressZipCodeModel![0].cep,
            isInstanceOf<int>());
      });
      test("Should be return instance of String", () {
        expect(addressModel!.correspondence, isInstanceOf<String>());
      });
    },
  );

  group(
    "Cep model tests",
    () {
      test("Should be return length Cep", () {
        expect(
            addressModel!.zipcode!.adressZipCodeModel![0].cep!
                .toString()
                .length,
            8);
      });

      test("Should be return length Country", () {
        expect(
            addressModel!.zipcode!.adressZipCodeModel![0].codUf
                .toString()
                .length,
            2);
      });

      test("Should be return concat address", () {
        final concat =
            "${addressModel!.zipcode!.adressZipCodeModel![0].nomeTipoLogradouro} ${addressModel!.zipcode!.adressZipCodeModel![0].nomeLogradouro}";
        expect(concat, "AVENIDA SANTOS DUMONT");
      });
    },
  );

  group(
    "AddressZipCodeModel json tests",
    () {
      test("Should be return cep length 8", () {
        expect(addressJson!['cep']["endereco"][0]['cep'].toString().length, 8);
      });

      test("Should be return cep != 00000000", () {
        expect(addressJson!['cep']["endereco"][0]['cep'] != "00000000", true);
      });

      test("Should be return codUf length 2", () {
        expect(
            addressJson!['cep']["endereco"][0]['codUf'].toString().length, 2);
      });
      test("Can´t return if is null", () {
        expect(addressJson!['cep']["endereco"][0]['cep'] == null, false);
        expect(addressJson!['cep']["endereco"][0]['codUf'] == null, false);
        expect(addressJson!['cep']["endereco"][0]['codCidade'] == null, false);
        expect(
            addressJson!['cep']["endereco"][0]['nomeCidade'] == null ||
                addressJson!['cep']["endereco"][0]['nomeCidade'] == "",
            false);
        expect(addressJson!['cep']["endereco"][0]['nomeTipoLogradouro'] == null,
            false);
        expect(
            addressJson!['cep']["endereco"][0]['nomeLogradouro'] == null ||
                addressJson!['cep']["endereco"][0]['nomeLogradouro'] == "",
            false);
        expect(addressJson!['cep']["endereco"][0]['codBairro'] == null, false);
        expect(addressJson!['cep']["endereco"][0]['nomeBairro'] == null, false);
        expect(
            addressJson!['cep']["endereco"][0]['nomeUf'] == null ||
                addressJson!['cep']["endereco"][0]['nomeUf'] == "",
            false);
        expect(
            addressJson!['cep']["endereco"][0]['complementoLogradouro'] == null,
            false);
        expect(addressJson!['cep']["endereco"] == null, false);
      });
    },
  );
  group(
    "AddressZipCodeModel json tests",
    () {
      test("Should be return cep length 8", () {
        expect(addressJson!['cep']["endereco"][0]['cep'].toString().length, 8);
      });

      test("Should be return cep != 00000000", () {
        expect(addressJson!['cep']["endereco"][0]['cep'] != "00000000", true);
      });

      test("Should be return codUf length 2", () {
        expect(
            addressJson!['cep']["endereco"][0]['codUf'].toString().length, 2);
      });
      test("Can´t return if is null", () {
        expect(addressJson!['cep']["endereco"][0]['cep'] == null, false);
        expect(addressJson!['cep']["endereco"][0]['codUf'] == null, false);
        expect(addressJson!['cep']["endereco"][0]['codCidade'] == null, false);
        expect(
            addressJson!['cep']["endereco"][0]['nomeCidade'] == null ||
                addressJson!['cep']["endereco"][0]['nomeCidade'] == "",
            false);
        expect(addressJson!['cep']["endereco"][0]['nomeTipoLogradouro'] == null,
            false);
        expect(
            addressJson!['cep']["endereco"][0]['nomeLogradouro'] == null ||
                addressJson!['cep']["endereco"][0]['nomeLogradouro'] == "",
            false);
        expect(addressJson!['cep']["endereco"][0]['codBairro'] == null, false);
        expect(addressJson!['cep']["endereco"][0]['nomeBairro'] == null, false);
        expect(
            addressJson!['cep']["endereco"][0]['nomeUf'] == null ||
                addressJson!['cep']["endereco"][0]['nomeUf'] == "",
            false);
        expect(
            addressJson!['cep']["endereco"][0]['complementoLogradouro'] == null,
            false);
        expect(addressJson!['cep']["endereco"] == null, false);
        expect(addressJson!['cep'] == null, false);
        expect(addressJson!['codigo'] == null, false);
        expect(addressJson!['tipo'] == null, false);
        expect(addressJson!['numero'] == null, false);
        expect(addressJson!['complemento'] == null, false);
        expect(addressJson!['correspondencia'] == null, false);
        expect(addressJson!['contatos'] == null, false);
        expect(addressJson!['contatos'][0]["contato"] == null, false);
        expect(addressJson!['contatos'][0]["tipo"] == null, false);
        expect(addressJson!['contatos'][0]["tipo"]["codigo"] == null, false);
        expect(addressJson!['contatos'][0]["tipo"]["nome"] == null, false);
      });

      test("test type json", () {
        expect(addressJson!['cep']["endereco"][0]['cep'], isInstanceOf<int>());
        expect(addressJson!['cep']["endereco"][0]['codUf'],
            isInstanceOf<String>());
        expect(addressJson!['cep']["endereco"][0]['codCidade'],
            isInstanceOf<int>());
        expect(addressJson!['cep']["endereco"][0]['nomeCidade'],
            isInstanceOf<String>());
        expect(addressJson!['cep']["endereco"][0]['nomeTipoLogradouro'],
            isInstanceOf<String>());
        expect(addressJson!['cep']["endereco"][0]['nomeLogradouro'],
            isInstanceOf<String>());
        expect(addressJson!['cep']["endereco"][0]['codBairro'],
            isInstanceOf<int>());
        expect(addressJson!['cep']["endereco"][0]['nomeBairro'],
            isInstanceOf<String>());
        expect(addressJson!['cep']["endereco"][0]['nomeUf'],
            isInstanceOf<String>());
        expect(addressJson!['cep']["endereco"][0]['complementoLogradouro'],
            isInstanceOf<String>());
        expect(addressJson!['cep']["endereco"], isInstanceOf<List<Object>>());
        expect(addressJson!['cep'], isInstanceOf<Object>());
        expect(addressJson!['codigo'], isInstanceOf<int>());
        expect(addressJson!['tipo'], isInstanceOf<String>());
        expect(addressJson!['numero'], isInstanceOf<String>());
        expect(addressJson!['complemento'], isInstanceOf<String>());
        expect(addressJson!['correspondencia'], isInstanceOf<String>());
        expect(addressJson!['contatos'], isInstanceOf<List<Object>>());
        expect(addressJson!['contatos'][0]["contato"], isInstanceOf<Object>());
        expect(addressJson!['contatos'][0]["tipo"], isInstanceOf<Object>());
        expect(
            addressJson!['contatos'][0]["tipo"]["codigo"], isInstanceOf<int>());
        expect(addressJson!['contatos'][0]["tipo"]["nome"],
            isInstanceOf<String>());
      });
    },
  );
}

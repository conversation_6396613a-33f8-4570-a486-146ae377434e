import 'dart:convert';

import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:http_client/http_client.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/vo/score_comparison.vo.dart';

class ScoreApi {
  final UnimedHttpClient httpClient;

  ScoreApi(this.httpClient);

  final logger = UnimedLogger(className: 'ScoreApi');
  Future<ResponseScore> getScoreComparison() async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();
    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";

    try {
      final url =
          '${FlavorConfig.instance!.values.portal.url}cooperativo/pontuacao?token=$token';
      final response = await httpClient
          .post(Uri.parse(url), headers: {"Content-Type": "application/json"});

      final data = jsonDecode(utf8.decode(response.bodyBytes));

      if (response.statusCode == 200) {
        final result = ResponseScore.fromJson(
            jsonDecode(utf8.decode(response.bodyBytes))['retorno']);

        logger.d(
            'getScoreComparison success pontuacao total ${result.pontuacaoTotal}');

        return result;
      } else {
        final message = data['mensagem'] ?? 'Não foi possível no momento.';

        logger.e(
            'getScoreComparison statusCode : ${response.statusCode} ${response.body}');
        throw ScoreComparisonException('$message');
      }
    } on UnimedException catch (ex) {
      logger.e('getScoreComparison ${ex.runtimeType} : $ex');
      throw ScoreComparisonException(ex.message);
    } catch (ex) {
      logger.e('getScoreComparison exception : $ex');
      throw ScoreComparisonException('Não foi possível no momento.');
    }
  }
}

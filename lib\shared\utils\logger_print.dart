import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:logger/logger.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';

class UnimedLogPrinter extends LogPrinter {
  final String? className;
  late RemoteLog _remoteLog;

  UnimedLogPrinter(this.className) {
    _remoteLog = Locator.instance!.get<RemoteLog>();
  }

  @override
  List<String> log(LogEvent event) {
    var color = PrettyPrinter().levelColors?[event.level];
    // var emoji = PrettyPrinter.levelEmojis[event.level];
    const isRelease = bool.fromEnvironment("dart.vm.product");

    if (event.level == Level.error) {
      _remoteLog.error(event.message);
      return _localLog(color: color, isRelease: isRelease, event: event);
    } else if (event.level == Level.debug) {
      _remoteLog.debug(event.message);
      return _localLog(color: color, isRelease: isRelease, event: event);
    } else if (event.level == Level.warning) {
      _remoteLog.warning(event.message);
      return _localLog(color: color, isRelease: isRelease, event: event);
    } else {
      _remoteLog.info(event.message);
      return _localLog(color: color, isRelease: isRelease, event: event);
    }
  }

  List<String> _localLog({
    AnsiColor? color,
    required bool isRelease,
    required LogEvent event,
  }) {
    if (!isRelease) {
      if (color != null) {
        return [
          color(
            'class: $className - mode: ${FlavorConfig.instance!.name} - msg: ${event.message}',
          )
        ];
      } else {
        return [
          'class: $className - mode: ${FlavorConfig.instance!.name} - msg: ${event.message}',
        ];
      }
    } else {
      return [];
    }
  }
}

class ConsoleOutput extends LogOutput {
  @override
  void output(OutputEvent event) {}
}

class UnimedLogFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    return true;
  }
}

class UnimedLogger extends Logger {
  final String? className;

  UnimedLogger({this.className})
      : super(
          printer: UnimedLogPrinter(className),
          filter: UnimedLogFilter(),
        );
}

class BiometryLogger {
  static final logger = UnimedLogger(className: 'BiometryLoggerAppCooperado');

  static biometryLogger(String message, String mode) {
    switch (mode) {
      case 'd':
        {
          logger.d(message);
          break;
        }
      case 'e':
        {
          logger.e(message);
          break;
        }
      default:
        {
          logger.i(message);
        }
    }
  }
}

import 'package:cooperado_minha_unimed/models/service_historic.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  HistoricService? modelTest;
  HistoricoServico? modelTest2;
  Map<String, dynamic>? json;
  Map<String, dynamic>? json2;
  ResponseServiceHistoric? model3;
  setUpAll(
    () {
      modelTest = HistoricService(
        mesPgto: "janeiro",
        anoPgto: "2022",
        unimedPrestador: "unimedPrestador",
        codigoPrestador: "1",
        tipo: "tipo",
        codEspecialidade: "2",
        nomeEspecialidade: "nomeEspecialidade",
        total: "20",
      );
      modelTest2 = HistoricoServico(
        mesPgto: "janeiro",
        anoPgto: "2022",
        unimedPrestador: "unimedPrestador",
        codigoPrestador: "1",
        tipo: "tipo",
        codEspecialidade: "2",
        nomeEspecialidade: "nomeEspecialidade",
        total: "20",
        media: "10",
      );

      json2 = {
        "historicoServico": [
          {
            "mesPgto": "janeiro",
            "anoPgto": "2022",
            "unimedPrestador": "unimedPrestador",
            "codigoPrestador": "1",
            "tipo": "HONORÁRIOS",
            "codEspecialidade": "2",
            "nomeEspecialidade": "nomeEspecialidade",
            "total": "20",
            "media": "10"
          }
        ]
      };
      json = {
        "mediaAtendimentosEspecialidade": [
          {
            "mesPgto": "janeiro",
            "anoPgto": "2022",
            "unimedPrestador": "unimedPrestador",
            "codigoPrestador": "1",
            "tipo": "HONORÁRIOS",
            "codEspecialidade": "2",
            "nomeEspecialidade": "nomeEspecialidade",
            "total": "20",
            "media": "10"
          },
          {
            "mesPgto": "janeiro",
            "anoPgto": "2022",
            "unimedPrestador": "unimedPrestador",
            "codigoPrestador": "1",
            "tipo": "HONORÁRIOS",
            "codEspecialidade": "2",
            "nomeEspecialidade": "nomeEspecialidade",
            "total": "20",
            "media": "10"
          },
          {
            "mesPgto": "janeiro",
            "anoPgto": "2022",
            "unimedPrestador": "unimedPrestador",
            "codigoPrestador": "1",
            "tipo": "HONORÁRIOS",
            "codEspecialidade": "2",
            "nomeEspecialidade": "nomeEspecialidade",
            "total": "20",
            "media": "10"
          }
        ],
        "mediaAtendimentosPrestador": [
          {
            "mesPgto": "janeiro",
            "anoPgto": "2022",
            "unimedPrestador": "unimedPrestador",
            "codigoPrestador": "1",
            "tipo": "HONORÁRIOS",
            "codEspecialidade": "2",
            "nomeEspecialidade": "nomeEspecialidade",
            "total": "20",
            "media": "10"
          },
          {
            "mesPgto": "janeiro",
            "anoPgto": "2022",
            "unimedPrestador": "unimedPrestador",
            "codigoPrestador": "1",
            "tipo": "HONORÁRIOS",
            "codEspecialidade": "2",
            "nomeEspecialidade": "nomeEspecialidade",
            "total": "20",
            "media": "10"
          },
        ],
      };
      model3 = ResponseServiceHistoric.fromJson(json!);
    },
  );
  group("json type", () {
    test("Json type test", () {
      if (json!["mediaAtendimentosPrestador"] != null) {
        expect(json!["mediaAtendimentosPrestador"][0]["mesPgto"],
            isInstanceOf<String>());
        expect(json!["mediaAtendimentosPrestador"][0]["anoPgto"],
            isInstanceOf<String>());

        expect(json!["mediaAtendimentosPrestador"][0]["unimedPrestador"],
            isInstanceOf<String>());

        expect(json!["mediaAtendimentosPrestador"][0]["codigoPrestador"],
            isInstanceOf<String>());

        expect(json!["mediaAtendimentosPrestador"][0]["tipo"],
            isInstanceOf<String>());

        expect(json!["mediaAtendimentosPrestador"][0]["codEspecialidade"],
            isInstanceOf<String>());

        expect(json!["mediaAtendimentosPrestador"][0]["nomeEspecialidade"],
            isInstanceOf<String>());

        expect(json!["mediaAtendimentosPrestador"][0]["total"],
            isInstanceOf<String>());
        expect(json!["mediaAtendimentosPrestador"][0]["media"],
            isInstanceOf<String>());
      }
      if (json!["mediaAtendimentosEspecialidade"] != null) {
        expect(json!["mediaAtendimentosEspecialidade"][0]["mesPgto"],
            isInstanceOf<String>());

        expect(json!["mediaAtendimentosEspecialidade"][0]["anoPgto"],
            isInstanceOf<String>());

        expect(json!["mediaAtendimentosEspecialidade"][0]["unimedPrestador"],
            isInstanceOf<String>());

        expect(json!["mediaAtendimentosEspecialidade"][0]["codigoPrestador"],
            isInstanceOf<String>());

        expect(json!["mediaAtendimentosEspecialidade"][0]["tipo"],
            isInstanceOf<String>());

        expect(json!["mediaAtendimentosEspecialidade"][0]["codEspecialidade"],
            isInstanceOf<String>());

        expect(json!["mediaAtendimentosEspecialidade"][0]["nomeEspecialidade"],
            isInstanceOf<String>());

        expect(json!["mediaAtendimentosEspecialidade"][0]["total"],
            isInstanceOf<String>());
        expect(json!["mediaAtendimentosEspecialidade"][0]["media"],
            isInstanceOf<String>());
      }
    });
  });

  group("model3 tests", () {
    test("Should be return instance of filterProviderHistoryByType 0", () {
      expect(model3!.filterProviderHistoryByType(0),
          isInstanceOf<List<HistoricoServico>>());
    });
    test("Should be return instance of filterProviderHistoryByType 1", () {
      expect(model3!.filterProviderHistoryByType(1),
          isInstanceOf<List<HistoricoServico>>());
    });
    test("Should be return instance of filterSpecialtyHistoryByType 0", () {
      expect(model3!.filterSpecialtyHistoryByType(0),
          isInstanceOf<List<HistoricoServico>>());
    });
    test("Should be return instance of filterSpecialtyHistoryByType 1", () {
      expect(model3!.filterSpecialtyHistoryByType(1),
          isInstanceOf<List<HistoricoServico>>());
    });
  });
  group(
    "isInstanceOf HistoricService model tests",
    () {
      test("Should be return instance of HistoricService", () {
        expect(modelTest, isInstanceOf<HistoricService>());
      });

      test("Should be return instance of String", () {
        expect(modelTest!.tipo!, isInstanceOf<String>());
      });
      test("Should be return instance of HistoricoServico", () {
        expect(modelTest2, isInstanceOf<HistoricoServico>());
      });

      test("Should be return instance of String", () {
        expect(modelTest2!.tipo!, isInstanceOf<String>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of HistoricService to json", () {
      expect(modelTest!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of HistoricService from json", () {
      expect(HistoricService.fromJson(json2!), isInstanceOf<HistoricService>());
    });
    test("Should be return instance of HistoricoServico to json", () {
      expect(modelTest2!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of HistoricoServico from json", () {
      expect(
          HistoricoServico.fromJson(json!["mediaAtendimentosEspecialidade"][0]),
          isInstanceOf<HistoricoServico>());
    });
    test("Should be return instance of HistoricoServico from json", () {
      expect(HistoricoServico.fromJson(json!["mediaAtendimentosPrestador"][0]),
          isInstanceOf<HistoricoServico>());
    });
    test("Should be return instance of ResponseServiceHistoric from json", () {
      expect(ResponseServiceHistoric.fromJson(json!),
          isInstanceOf<ResponseServiceHistoric>());
      expect(
          ResponseServiceHistoric.fromJson(json!)
              .mediaAtendimentosEspecialidade,
          isInstanceOf<List<HistoricoServico>>());
    });
    test("Should be return instance of ResponseServiceHistoric to json", () {
      expect(ResponseServiceHistoric.fromJson(json!).toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
  });
  group(
    "Other tests",
    () {
      test("Should be return length 2", () {
        expect(modelTest2!.ano2Digitos.length, 2);
      });
      test("Should be return length year 22", () {
        expect(modelTest2!.ano2Digitos, "22");
      });
      test("Should be return mes ano janeiro/2022", () {
        expect(modelTest2!.mesAno, "janeiro/2022");
      });

      test("Should be return totalInt 0", () {
        expect(modelTest2!.getDataToShow(0), 0);
      });
      test("Should be return consults 0", () {
        expect(modelTest2!.getDataToShow(1), 0);
      });
      test("Should be return services 0", () {
        expect(modelTest2!.getDataToShow(2), 0);
      });
      test("Should be return fees 0", () {
        expect(modelTest2!.getDataToShow(3), 0);
      });
      test("Should be return default 0", () {
        expect(modelTest2!.getDataToShow(4), 0);
      });

      test("Should be return instance of _ajustList null", () {
        json = {
          "mediaAtendimentosPrestador": [],
          "mediaAtendimentosEspecialidade": [
            modelTest2!.toJson(),
          ]
        };
        final modelTest3 = ResponseServiceHistoric.fromJson(json!);
        expect(modelTest3.mediaAtendimentosEspecialidade == null, false);
      });
    },
  );
}

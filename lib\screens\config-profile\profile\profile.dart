// ignore_for_file: unused_local_variable

import 'package:cooperado_minha_unimed/bloc/config-profile/config_profile_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/screens/config-profile/profile/alert_update_phone.dart';
import 'package:cooperado_minha_unimed/screens/config-profile/profile/search_address_cep.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/profile/profile-payload.vo.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:cooperado_minha_unimed/shared/widgets/unimed_card.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import 'alert_update_email.dart';

class Profile extends StatefulWidget {
  final User user;
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const Profile({
    super.key,
    required this.user,
    required this.analytics,
    required this.observer,
  });

  @override
  ProfileTabState createState() => ProfileTabState();
}

class ProfileTabState extends State<Profile> {
  TextEditingController emailController = TextEditingController();
  bool emailEdited = false;

  @override
  void initState() {
    super.initState();
    setState(() {
      emailController.text = widget.user.email!;
    });

    widget.analytics.logScreenView(
      screenName: 'Perfil',
      screenClass: 'Profile',
    );
  }

  @override
  Widget build(BuildContext context) {
    final padding = MediaQuery.of(context).size.height / 4;
    return BlocBuilder<ConfigProfileCubit, ConfigProfileState>(
      builder: (context, state) {
        if (state is LoadedAllProfileAddressesState) {
          return contentProfile(state.payloadAddress!);
        } else if (state is LoadingAllAdressesState) {
          return Padding(
            padding: EdgeInsets.symmetric(vertical: padding),
            child: const SpinKitCircle(color: CooperadoColors.tealGreen),
          );
        } else if (state is ErrorConfigProfileState) {
          return Padding(
            padding: EdgeInsets.symmetric(vertical: padding),
            child: ErrorBanner(message: state.message),
          );
        } else {
          final payloadAddress =
              BlocProvider.of<ConfigProfileCubit>(context).payloadAddress;
          return payloadAddress != null
              ? contentProfile(payloadAddress)
              : Container();
        }
      },
    );
  }

  Widget contentProfile(PayloadAddress payloadAddress) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 15.0, horizontal: 10.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _personalDataCard(payloadAddress),
          const SizedBox(height: 15.0),
          _homeAddressDataCard(payloadAddress),
          const SizedBox(height: 15.0),
          _comercialAddressDataCard(payloadAddress),
        ],
      ),
    );
  }

  Widget _personalDataCard(PayloadAddress payloadAddress) {
    const style = TextStyle(fontSize: 14);

    return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
      builder: (context, state) {
        bool isSensitiveDataVisible = state.isSensitiveDataVisible;

        String? nome = isSensitiveDataVisible
            ? payloadAddress.nome
            : '*' * payloadAddress.nome!.length;

        List<Widget> especialidades = payloadAddress.especialidades!
            .map((specialtie) => Text(
                  isSensitiveDataVisible
                      ? specialtie.descricao!
                      : '*' * specialtie.descricao!.length,
                ))
            .toList();

        String crm = isSensitiveDataVisible
            ? payloadAddress.crm.toString()
            : '*' * payloadAddress.crm.toString().length;

        return UnimedCard(
          child: Padding(
            padding: const EdgeInsets.all(15.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _item(value: nome, style: style),
                ...especialidades,
                Text('($crm)'),
                _textDeviceIdOneSignal(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _textDeviceIdOneSignal() {
    if (FlavorConfig.isProduction()) {
      return Container();
    } else {
      return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
        builder: (context, state) {
          bool isSensitiveDataVisible = state.isSensitiveDataVisible;
          final deviceId =
              Locator.instance!.get<AuthApi>().user?.FCMUserId ?? '';
          final displayText =
              isSensitiveDataVisible ? deviceId : '*' * deviceId.length;

          return SelectableText(displayText);
        },
      );
    }
  }

  Widget _item(
      {String? value, Function? onEdit, bool? isEditable, TextStyle? style}) {
    if (value == null) return Container();
    const defaultStyle = TextStyle(fontSize: 14, fontWeight: FontWeight.w500);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Flexible(flex: 5, child: Text(value, style: style ?? defaultStyle)),
            const SizedBox(width: 10),
            if (isEditable ?? false)
              Flexible(
                flex: 1,
                child: InkWell(
                  onTap: onEdit as void Function()? ?? () {},
                  child: const Icon(
                    Icons.create,
                    color: CooperadoColors.tealGreen,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 10)
      ],
    );
  }

  Widget _homeAddressDataCard(PayloadAddress payloadAddress) {
    final address = payloadAddress.enderecos!.firstWhere(
      (element) => element!.tipoEndereco == 'R',
      orElse: () => null,
    );

    return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
      builder: (context, state) {
        bool isSensitiveDataVisible = state.isSensitiveDataVisible;

        String enderecoCompl = address != null
            ? (isSensitiveDataVisible
                ? address.enderecoCompl
                : '*' * address.enderecoCompl.length)
            : '';

        return UnimedCard(
          child: Padding(
            padding: const EdgeInsets.all(15.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Residencial',
                  style: TextStyle(
                    fontSize: 14,
                    color: CooperadoColors.grayLight2,
                  ),
                ),
                const SizedBox(height: 20),
                if (address != null)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Text(enderecoCompl),
                      const SizedBox(height: 15),
                      Text(
                        'Correspondência: ${address.correspondencia == 'S' ? 'SIM' : 'NÃO'}',
                        style: const TextStyle(
                            fontSize: 12, color: CooperadoColors.grayLight2),
                      ),
                      const SizedBox(height: 5),
                    ],
                  ),
                if (address == null)
                  const Text('Não há endereço residencial cadastrado.')
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _comercialAddressDataCard(PayloadAddress payloadAddress) {
    final comercialAddresses = payloadAddress.enderecos!
        .where((element) => element!.tipoEndereco == 'C')
        .toList();

    return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
      builder: (context, state) {
        bool isSensitiveDataVisible = state.isSensitiveDataVisible;

        List<Widget> addressWidgets = comercialAddresses.map((address) {
          String enderecoCompl = isSensitiveDataVisible
              ? address!.enderecoCompl
              : '*' * address!.enderecoCompl.length;

          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(enderecoCompl),
              const SizedBox(height: 15),
              Text(
                'Correspondência: ${address.correspondencia == 'S' ? 'SIM' : 'NÃO'}',
                style: const TextStyle(
                    fontSize: 12, color: CooperadoColors.grayLight2),
              ),
              const SizedBox(height: 5),
            ],
          );
        }).toList();

        return UnimedCard(
          child: Padding(
            padding: const EdgeInsets.all(15.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const Text(
                  'Consultórios',
                  style: TextStyle(
                    color: CooperadoColors.grayLight2,
                  ),
                ),
                const SizedBox(height: 20),
                _comercialAddresses(comercialAddresses),
                if (comercialAddresses.isEmpty)
                  const Text('Não há endereço de consultório cadastrado. ')
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _comercialAddresses(List<Enderecos?> addresses) {
    const style = TextStyle(fontSize: 14, fontWeight: FontWeight.w500);

    return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
      builder: (context, state) {
        bool isSensitiveDataVisible = state.isSensitiveDataVisible;

        return ListView.builder(
          primary: false,
          shrinkWrap: true,
          itemCount: addresses.length,
          itemBuilder: (context, i) {
            String enderecoCompl = isSensitiveDataVisible
                ? addresses[i]!.enderecoCompl
                : '*' * addresses[i]!.enderecoCompl.length;

            List<String?> emails = isSensitiveDataVisible
                ? addresses[i]!.getEmails
                : addresses[i]!
                    .getEmails
                    .map((email) => '*' * email!.length)
                    .toList();

            List<String?> phones = isSensitiveDataVisible
                ? addresses[i]!.getPhones
                : addresses[i]!
                    .getPhones
                    .map((phone) => '*' * phone!.length)
                    .toList();

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Divider(height: 30, thickness: 1.5),
                _item(
                  value: enderecoCompl,
                  style: style,
                  onEdit: () => {_updateAddress(addresses[i])},
                  isEditable: true,
                ),
                _items(
                  label: 'E-mail',
                  values: emails,
                  isEditable: true,
                  onEdit: () => _dialogListEditables(
                      address: addresses[i],
                      contatos: addresses[i]?.contatosEmail,
                      title: 'E-mail'),
                ),
                _items(
                  label: 'Telefone/Celular',
                  values: phones,
                  isEditable: true,
                  onEdit: () => _dialogListEditables(
                    address: addresses[i],
                    contatos: addresses[i]?.contatosPhone,
                    title: 'Telefone/Celular',
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _items({label, required List<String?> values, onEdit, isEditable}) {
    if (values.isEmpty) return Container();
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Flexible(
            flex: 5,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                      color: CooperadoColors.grayLight2, fontSize: 12),
                ),
                const SizedBox(height: 3),
                Text(values.join(' / '))
                // Wrap(
                //   alignment: WrapAlignment.start,
                //   children: values.map<Widget>((e) {
                //     return Text('$e');
                //   }).toList(),
                // ),
              ],
            ),
          ),
          const SizedBox(width: 10),
          if (isEditable ?? false)
            Flexible(
              flex: 1,
              child: InkWell(
                onTap: onEdit ?? () {},
                child: const Icon(
                  Icons.create,
                  color: CooperadoColors.tealGreen,
                ),
              ),
            ),
        ],
      ),
    );
  }

  _dialogListEditables({
    Enderecos? address,
    List<Contatos?>? contatos,
    String? title,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title!),
        content: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: contatos!
              .map<Widget>((contato) => Column(
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.pop(context);
                          if (contato!.tipoContato == ContactType.telefone ||
                              contato.tipoContato == ContactType.celular) {
                            _updateTelefoneOrCelular(
                                address: address, contato: contato);
                          } else if (contato.tipoContato == ContactType.email) {
                            _updateEmail(address: address, contato: contato);
                          }
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            const Icon(Icons.fiber_manual_record_outlined,
                                color: CooperadoColors.tealGreen, size: 16),
                            Flexible(child: Text(contato!.numeroContato!)),
                            const Flexible(
                              child: Icon(
                                Icons.keyboard_arrow_right_outlined,
                                color: CooperadoColors.tealGreen,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Divider(thickness: 1.5, height: 20),
                      const SizedBox(height: 10),
                    ],
                  ))
              .toList(),
        ),
      ),
    );
  }

  _updateTelefoneOrCelular({Enderecos? address, Contatos? contato}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertUpdateTelefone(
        address: address,
        contato: contato,
        onPressed: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }

  _updateEmail({Enderecos? address, Contatos? contato}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertUpdateEmail(
        address: address,
        contato: contato,
        onPressed: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }

  _updateAddress(Enderecos? address) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape:
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
        content: SearchAddressByCEP(
          address: address,
          analytics: widget.analytics,
          observer: widget.observer,
        ),
      ),
    );
  }
}

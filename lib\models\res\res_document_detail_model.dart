import 'package:intl/intl.dart';

class ResDocumentDetailModel {
  late String nomeMedico;
  late String especialidadeMedico;
  late String conteudo;
  late String streamDocumento;
  late String tipoDocumento;
  late String tipoArquivo;
  late String urlDocumento;
  late String data;

  ResDocumentDetailModel(
      {required this.nomeMedico,
      required this.especialidadeMedico,
      required this.conteudo,
      required this.streamDocumento,
      required this.tipoDocumento,
      required this.tipoArquivo,
      required this.urlDocumento,
      required this.data});

  String get dataFormatted {
    final DateTime dateTime = DateTime.parse(data).toLocal();
    return DateFormat('dd/MM/yyyy HH:mm:ss').format(dateTime);
  }

  ResDocumentDetailModel.fromJson(Map<String, dynamic> json) {
    nomeMedico = json['nomeMedico'];
    especialidadeMedico = json['especialidadeMedico'];
    conteudo = json['conteudo'];
    streamDocumento = json['streamDocumento'];
    tipoDocumento = json['tipoDocumento'];
    tipoArquivo = json['tipoArquivo'];
    urlDocumento = json['urlDocumento'];
    data = json['data'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['nomeMedico'] = nomeMedico;
    json['especialidadeMedico'] = especialidadeMedico;
    json['conteudo'] = conteudo;
    json['streamDocumento'] = streamDocumento;
    json['tipoDocumento'] = tipoDocumento;
    json['tipoArquivo'] = tipoArquivo;
    json['urlDocumento'] = urlDocumento;
    json['data'] = data;
    return json;
  }
}

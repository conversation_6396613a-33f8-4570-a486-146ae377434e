import 'package:cooperado_minha_unimed/bloc/transparency/loss_ratio/loss_ratio.cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/graph-transparency/transparency_barchart.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/loss_ratio/loss_ratio_datail.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/indices.dart';
import 'package:cooperado_minha_unimed/shared/widgets/buttons.dart';
import 'package:cooperado_minha_unimed/shared/widgets/card_refresh.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:cooperado_minha_unimed/shared/widgets/graphics/indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class LossRatioCardCard extends StatefulWidget {
  final bool isVisible;

  final Orientation orientation;

  const LossRatioCardCard(
      {super.key, this.isVisible = true, required this.orientation});

  @override
  CardIndiceSinistralidade createState() => CardIndiceSinistralidade();
}

class CardIndiceSinistralidade extends State<LossRatioCardCard> {
  double total = 0.0;
  DateTime? selectedDateTime;
  int dataLimit = 6;
  List<String> years = [];

  @override
  void initState() {
    if (widget.isVisible) _getLossRatio();
    super.initState();
  }

  void _getLossRatio() {
    context.read<LossRatioCubit>().getLossRatio();
  }

  List<String> extractYearsFromData(List<VODataModel> data) {
    List<VODataModel> limitedData;
    if (widget.orientation == Orientation.portrait) {
      int startIndex = data.length - dataLimit;
      limitedData = data.sublist(startIndex >= 0 ? startIndex : 0);
    } else {
      limitedData = data.take(dataLimit).toList();
    }

    Set<String> years = {};

    for (var item in limitedData) {
      if (item.referenceYear != null) {
        years.add(item.referenceYear?.toString() ?? '');
      }
    }

    List<String> yearsList = years.toList()..sort();

    return yearsList;
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: widget.isVisible,
      child: CardRefresh(
        title: BlocBuilder<LossRatioCubit, LossRatioState>(
          builder: (context, state) {
            if (state is LoadedLossRatioState) {
              years = extractYearsFromData(state.lossRatio.data!.reversed
                  .toList()
                  .sublist(0, dataLimit));
              return RichText(
                textAlign: TextAlign.center,
                text: const TextSpan(
                  text: 'Índice de sinistralidade',
                  style: TextStyle(
                    color: CooperadoColors.blackText,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              );
            } else {
              return const Text('Índice de sinistralidade',
                  style: TextStyle(
                    color: CooperadoColors.blackText,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ));
            }
          },
        ),
        refresh: _iconRefresh(),
        child: BlocBuilder<LossRatioCubit, LossRatioState>(
          builder: (context, state) {
            if (state is LoadedLossRatioState) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.only(left: 8.0, bottom: 20),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Indicator(
                          color: CooperadoColors.chartColors[9],
                          text: 'Mensal',
                          isSquare: true,
                        ),
                        const SizedBox(width: 20),
                        Indicator(
                          color: CooperadoColors.chartColors[7],
                          text: 'Acumulado',
                          isSquare: true,
                        ),
                        const SizedBox(width: 20),
                        Indicator(
                          color: CooperadoColors.chartColors[8],
                          text: 'Projetado',
                          isSquare: true,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 200,
                    child: TransparencyBarChart(
                      animate: true,
                      data: state.lossRatio.data,
                      colors: [
                        CooperadoColors.chartColors[9],
                        CooperadoColors.chartColors[7],
                        CooperadoColors.chartColors[8]
                      ],
                      orientation: MediaQuery.of(context).orientation,
                      dataLimit: dataLimit,
                    ),
                  ),
                  const SizedBox(height: 30),
                  ButtonSeeMore(onPress: () {
                    _getLossRatio();
                    Navigator.push(
                      context,
                      FadeRoute(
                        page: LossRatioDetail(lossRatio: state.lossRatio),
                      ),
                    );
                  }),
                ],
              );
            } else if (state is LoadingLossRatioState) {
              return const SpinKitCircle(color: CooperadoColors.tealGreen);
            } else if (state is ErrorLossRatioState) {
              return ErrorBanner(message: state.message);
            } else {
              return Container();
            }
          },
        ),
      ),
    );
  }

  Widget _iconRefresh() {
    return BlocBuilder<LossRatioCubit, LossRatioState>(
        builder: (context, state) {
      if (state is ErrorLossRatioState) {
        return InkWell(
            child: const Icon(Icons.refresh), onTap: () => _getLossRatio());
      } else {
        return Container();
      }
    });
  }
}

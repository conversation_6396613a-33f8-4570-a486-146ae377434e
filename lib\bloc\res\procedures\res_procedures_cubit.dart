import 'package:cooperado_minha_unimed/models/res/res_attendance_model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'res_procedures_state.dart';

class ResProcedureCubit extends Cubit<ResProceduresState> {
  ResProcedureCubit() : super(InitialResProceduresState());

  List<ResAttendanceModel> _listProcedures = List.empty(growable: true);
  List<ResAttendanceModel> get listProcedures => _listProcedures;

  void listResProcedures(
      {required String crm,
      required String card,
      DateTimeRange? dataRange}) async {
    try {
      emit(LoadingResProceduresState());

      _listProcedures = await Locator.instance!<ResGraphQlApi>()
          .resGetAttendanceByType(
              crm: crm, card: card, type: "procedimento", dataRange: dataRange);

      if (_listProcedures.isEmpty) {
        emit(NoDataResProceduresState());
      } else {
        emit(LoadedResProceduresState(listResProcedures: _listProcedures));
      }
    } catch (e) {
      emit(ErrorResProceduresState(message: e.toString()));
    }
  }
}

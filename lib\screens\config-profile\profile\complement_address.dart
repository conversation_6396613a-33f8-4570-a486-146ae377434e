import 'package:biometria_perfilapps/main.dart';
import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/config-profile/config_profile_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/profile/profile_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/models/zipcode.model.dart';
import 'package:cooperado_minha_unimed/screens/config-profile/config_screen.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/utils/biometry_utils.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/vo/profile/profile-payload.vo.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class ComplementAddress extends StatefulWidget {
  final Enderecos? currentAddress;
  final AddressZipCodeModel? selectedAddress;
  final String cep;
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const ComplementAddress({
    super.key,
    this.currentAddress,
    this.selectedAddress,
    required this.cep,
    required this.analytics,
    required this.observer,
  });

  @override
  ComplementAddressState createState() => ComplementAddressState();
}

class ComplementAddressState extends State<ComplementAddress> {
  TextEditingController numberController = TextEditingController();
  final numberFormatter = MaskTextInputFormatter(
    mask: '########',
    filter: {"#": RegExp(r'[0-9]')},
  );
  TextEditingController complementController = TextEditingController();

  bool isEnableConfirm = false;
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ConfigProfileCubit, ConfigProfileState>(
        builder: (context, state) {
      return IgnorePointer(
          ignoring: state is UpdatingProfileState ? true : false,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _addressItemTile('CEP', widget.cep),
              _addressItemTile(
                  'Logradouro', widget.selectedAddress!.nomeLogradouro!),
              _addressItemTile('Bairro', widget.selectedAddress!.nomeBairro!),
              _addressItemTile('Cidade - UF',
                  '${widget.selectedAddress!.nomeCidade!} - ${widget.selectedAddress!.nomeUf!}'),
              const SizedBox(height: 10.0),
              const Text('Número', style: TextStyle(fontSize: 11)),
              TextFormField(
                  inputFormatters: [numberFormatter],
                  controller: numberController,
                  decoration: _decorationInput(),
                  keyboardType: TextInputType.number,
                  onChanged: (value) =>
                      setState(() => isEnableConfirm = value.isNotEmpty)),
              const SizedBox(height: 10.0),
              const Text('Complemento', style: TextStyle(fontSize: 11)),
              TextFormField(
                  controller: complementController,
                  decoration: _decorationInput()),
              const SizedBox(height: 10.0),
              _indicatorStateUpdateProfile(),
              const SizedBox(height: 10.0),
              Row(
                children: [
                  Expanded(child: _buttonCancelar()),
                  const SizedBox(width: 10.0),
                  Expanded(child: _buttonConfirmar()),
                ],
              ),
            ],
          ));
    });
  }

  Widget _indicatorStateUpdateProfile() {
    return BlocConsumer<ConfigProfileCubit, ConfigProfileState>(
      listener: (context, state) {
        if (state is UpdatedProfileState) {
          Alert.open(context,
              title: 'Sucesso',
              text: 'Endereço atualizado com sucesso.', callbackClose: () {
            Navigator.pop(context);
            Navigator.pushReplacement(
              context,
              FadeRoute(
                page: ConfigProfileScreen(
                  analytics: widget.analytics,
                  observer: widget.observer,
                ),
              ),
            );
          });
        }
      },
      builder: (context, state) {
        if (state is UpdatingProfileState) {
          return const SpinKitThreeBounce(
              color: CooperadoColors.tealGreen, size: 30);
        } else if (state is ErrorUpdateProfileState) {
          return Text(state.message,
              style: const TextStyle(color: Colors.red, fontSize: 12));
        }

        return Container();
      },
    );
  }

  Widget _buttonCancelar() {
    return BlocBuilder<ConfigProfileCubit, ConfigProfileState>(
      builder: (context, state) {
        if (state is LoadingAddressesByZipcodeState ||
            state is UpdatingProfileState) {
          return ElevatedButton(
              style: ElevatedButton.styleFrom(
                  backgroundColor: CooperadoColors.grayLight),
              onPressed: null,
              child: const Text('Cancelar'));
        } else {
          return ElevatedButton(
              style: ElevatedButton.styleFrom(
                  backgroundColor: CooperadoColors.grayLight2),
              child: const Text('Cancelar'),
              onPressed: () => Navigator.pop(context));
        }
      },
    );
  }

  Widget _buttonConfirmar() {
    return BlocBuilder<ConfigProfileCubit, ConfigProfileState>(
      builder: (context, state) {
        if (state is LoadingAddressesByZipcodeState ||
            state is UpdatingProfileState ||
            !isEnableConfirm) {
          return ElevatedButton(
              style: ElevatedButton.styleFrom(
                  backgroundColor: CooperadoColors.tealGreenSecondary),
              onPressed: null,
              child: const Text('Confirmar'));
        } else {
          return ElevatedButton(
              style: ElevatedButton.styleFrom(
                  backgroundColor: CooperadoColors.tealGreenSecondary),
              onPressed: _onConfirm,
              child: const Text('Confirmar'));
        }
      },
    );
  }

  _onConfirm() {
    final User user = context.read<ProfileCubit>().user;
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BiometryUnimed(
          perfilAppsCredentials: BiometriaUtils.convertDefaultCredentials(
            FlavorConfig.instance!.values.profilePermissions,
          ),
          codPrestador: user.codPrestador,
          name: user.nome ?? '',
          environment: FlavorConfig.instance!.values.validadeBioIdEnv,
          maxHeight: 720,
          maxQuality: 60,
          cameraPreview: true,
          theme: ThemeData(
            useMaterial3: false,
            primaryColor: cooperadoTealGreen,
            colorScheme: ColorScheme.fromSwatch().copyWith(
              secondary: CooperadoColors.grayDark2,
            ),
            progressIndicatorTheme: const ProgressIndicatorThemeData(
              color: cooperadoTealGreen,
            ),
            elevatedButtonTheme: ElevatedButtonThemeData(
              style: ElevatedButton.styleFrom(
                backgroundColor: cooperadoTealGreen,
                foregroundColor: Colors.white,
              ),
            ),
            fontFamily: 'UnimedSans',
            primaryIconTheme: const IconThemeData(color: cooperadoTealGreen),
          ),
          onValid: () {
            Navigator.of(context).pop();

            final String crm = context.read<AuthCubit>().credentials.crm;
            final String cep =
                widget.cep.replaceAll('.', '').replaceAll('-', '');
            final newAdders =
                Enderecos.fromJson(widget.currentAddress!.toJson());
            newAdders.cep = cep;
            newAdders.nomeLogradouro = widget.selectedAddress!.nomeLogradouro;
            newAdders.codLogradouro = widget.selectedAddress!.codLogradouro;
            newAdders.nomeBairro = widget.selectedAddress!.nomeBairro;
            newAdders.nomeCidade = widget.selectedAddress!.nomeCidade;
            newAdders.numEndereco = numberController.text;
            newAdders.complEndereco = complementController.text;
            context
                .read<ConfigProfileCubit>()
                .updateEndereco(crm: crm, endereco: newAdders);
          },
          onCancel: () {
            Navigator.of(context).pop();
          },
          logger: BiometryLogger.biometryLogger,
        ),
      ),
    );
  }

  _decorationInput() {
    return const InputDecoration(
      border: OutlineInputBorder(),
      contentPadding: EdgeInsets.symmetric(vertical: 0, horizontal: 10.0),
    );
  }

  Widget _addressItemTile(String label, String value) {
    const lblStyle = TextStyle(fontSize: 11);

    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text(label, style: lblStyle),
      Text(value),
      const SizedBox(height: 10.0)
    ]);
  }
}

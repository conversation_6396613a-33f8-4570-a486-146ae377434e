part of 'res_documents_detail_cubit.dart';

abstract class ResDocumentDetailState extends Equatable {
  const ResDocumentDetailState();

  @override
  List<Object> get props => [];
}

class InitialResDocumentDetailState extends ResDocumentDetailState {}

class LoadingResDocumentDetailState extends ResDocumentDetailState {
  @override
  List<Object> get props => [];
}

class NoDataResDocumentDetailState extends ResDocumentDetailState {
  const NoDataResDocumentDetailState();
}

class ErrorResDocumentDetailState extends ResDocumentDetailState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResDocumentDetailState({required this.message});
}

class LoadedResDocumentDetailState extends ResDocumentDetailState {
  final List<ResDocumentDetailModel> resDocumentDetailModel;
  final int index;

  @override
  List<Object> get props => [resDocumentDetailModel, index];

  const LoadedResDocumentDetailState(
      {required this.resDocumentDetailModel, required this.index});
}

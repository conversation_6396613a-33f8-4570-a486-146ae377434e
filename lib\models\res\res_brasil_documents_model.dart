import 'package:intl/intl.dart';

class ResBrazilDocumentModel {
  final String? nomeMedico;
  final String? especialidadeMedico;
  final String? conteudo;
  final String? streamDocumento;
  final String? tipoDocumento;
  final String? tipoArquivo;
  final String? urlDocumento;
  final String? data;

  ResBrazilDocumentModel({
    this.conteudo,
    this.streamDocumento,
    this.tipoDocumento,
    this.tipoArquivo,
    this.urlDocumento,
    this.data, 
    this.nomeMedico,
    this.especialidadeMedico,
  });


   String get dateDiagnosticoFormatted {
    return DateFormat('dd/MM/yyyy HH:mm').format(DateTime.parse(data!));
  }


  ResBrazilDocumentModel.fromJson(Map<String, dynamic> json)
    : conteudo = json['conteudo'],
    streamDocumento = json['streamDocumento'],
    tipoDocumento = json['tipoDocumento'],
    tipoArquivo = json['tipoArquivo'],
    urlDocumento = json['urlDocumento'],
    data = json['data'],
    nomeMedico = json['nomeMedico'],
    especialidadeMedico = json['especialidadeMedico'];

  Map<String, dynamic> toJson(){
    final Map<String, dynamic> dados = <String, dynamic>{};
    dados['conteudo'] = conteudo;
    dados['streamDocumento'] = streamDocumento;
    dados['tipoDocumento'] = tipoDocumento;
    dados['tipoArquivo'] = tipoArquivo;
    dados['urlDocumento'] = urlDocumento;
    dados['data'] = data;
    dados['nomeMedico'] = nomeMedico;
    dados['especialidadeMedico'] = especialidadeMedico;

    return dados;
    }
  }




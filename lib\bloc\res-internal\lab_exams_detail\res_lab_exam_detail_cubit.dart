import 'package:cooperado_minha_unimed/models/res-internal/lab_exam_detail.model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res-internal.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'res_lab_exam_detail_cubit_state.dart';

class ResLabExamDetailCubit extends Cubit<ResLabExamDetailState> {
  ResLabExamDetailCubit() : super(InitialResLabExamDetailState());

  List<ResLabExamDetailModel> _listLabExamDetails = List.empty(growable: true);
  List<ResLabExamDetailModel> get listLabExamDetails => _listLabExamDetails;

  void listResLabExamDetails(
      {required String card, required String orderId}) async {
    try {
      emit(LoadingResLabExamDetailState());

      _listLabExamDetails = await Locator.instance!<GraphQlApiInternal>()
          .resInternalLabExamDetailsByCardAndOrderId(
              card: card, orderId: orderId);

      emit(
          LoadedResLabExamDetailState(listLabExamDetails: _listLabExamDetails));
    } catch (e) {
      emit(ErrorResLabExamDetailState(message: e.toString()));
    }
  }

  void getExamDetailsWidget(List<ResLabExamDetailModel> widgetList) {
    try {
      emit(LoadingResLabExamDetailState());

      emit(LoadedResLabExamDetailState(listLabExamDetails: widgetList));
    } catch (e) {
      emit(ErrorResLabExamDetailState(message: e.toString()));
    }
  }

  void searchListLabExamDetails({
    required String searchKey,
    required List<ResLabExamDetailModel> widgetList,
  }) async {
    try {
      emit(LoadingResLabExamDetailState());

      final filtredList = widgetList
          .where((element) => element
              .toJson()
              .toString()
              .toLowerCase()
              .contains(searchKey.toLowerCase()))
          .toList();

      emit(LoadedResLabExamDetailState(listLabExamDetails: filtredList));
    } catch (e) {
      emit(ErrorResLabExamDetailState(message: e.toString()));
    }
  }
}

part of 'res_procedures_detail_cubit.dart';

abstract class ResProcedureDetailState extends Equatable {
  const ResProcedureDetailState();

  @override
  List<Object> get props => [];
}

class InitialResProcedureDetailState extends ResProcedureDetailState {}

class LoadingResProcedureDetailState extends ResProcedureDetailState {
  @override
  List<Object> get props => [];
}

class NoDataResProcedureDetailState extends ResProcedureDetailState {
  @override
  List<Object> get props => [];
}

class ErrorResProcedureDetailState extends ResProcedureDetailState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResProcedureDetailState({required this.message});
}

class LoadedResProcedureDetailState extends ResProcedureDetailState {
  final List<ResProcedureDetailModel> resProcedureDetailModel;
  final int index;

  @override
  List<Object> get props => [resProcedureDetailModel, index];

  const LoadedResProcedureDetailState(
      {required this.resProcedureDetailModel, required this.index});
}

class LoadingResAllergiesSearchState extends ResProcedureDetailState {
  @override
  List<Object> get props => [];
}

class ErrorResAllergiesSearchState extends ResProcedureDetailState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResAllergiesSearchState({required this.message});
}

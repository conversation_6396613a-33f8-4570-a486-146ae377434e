# Teste unitário

### Rodar todos de uma vez
- Abra o terminal
```$ flutter test ```

Terá como resposta todos os testes passados ou falhados

### Rodar em arquivo específico
- Abra o arquivo de teste
- Dentro do arquivo para cada função tem um run|debug
- A função main rodará todos os testes do arquivo
- A função Group rodará apenas o grupo selecionado e todos os testes dentro
- A função test rodará apenas esse único teste

### Mudando os dados recebidos

Na função setUpAll() é definido os dados do serviço, logo para novos testes mudar esses valores

ex:
```
 setUpAll(() {
    model = AnswerModel(
      protocolNumber: '231312',
      questionId: 1,
    );
    json = {
      "protocolNumber": "231312",
      "questionId": 1,
    };
    //trocar pelo novo json
    json = {
      "protocolNumber": 231312,
      "questionId": 1,
    };
  });

//Terá mensagem de erro pois protocolNumber deveria ser uma String
```
### Testar cobertura dos testes nos models

- Instalar extensão coverage -> https://marketplace.visualstudio.com/items?itemName=ryanluker.vscode-coverage-gutters
- Rodar ```$ flutter test --coverage```
- Entre no arquivo model
- Na barra de baixo do VSCode clique no botão "Watch"
- Terá a porcetagem de cobertura e no próprio arquivo indicando locais verde e vermelho para cobertura ou não.

import 'package:intl/intl.dart';

class ResBrazilListDiagnosticoModel {
  late String? codigo;
  late String? tipo;
  late String? nomeLocal;
  late String? dataEntrada;
  late dynamic dataAlta;
  late String? codigoAtendimentoEncode;
  List<ItensAtendimento>? itensAtendimento;

  ResBrazilListDiagnosticoModel({
    this.codigo,
    this.tipo,
    this.nomeLocal,
    this.dataEntrada,
    this.dataAlta,
    this.codigoAtendimentoEncode,
    this.itensAtendimento,
  });

  String get solicitationDateFormatted {
    return DateFormat('dd/MM/yyyy HH:mm').format(DateTime.parse(dataEntrada!));
  }

  ResBrazilListDiagnosticoModel.fromJson(Map<String, dynamic> json) {
    codigo = json['codigo'] as String?;
    tipo = json['tipo'] as String?;
    nomeLocal = json['nomeLocal'] as String?;
    dataEntrada = json['dataEntrada'] as String?;
    dataAlta = json['dataAlta'];
    codigoAtendimentoEncode = json['codigoAtendimentoEncode'] as String?;
    if (json['itensAtendimento'] != null) {
      itensAtendimento = <ItensAtendimento>[];
      json['itensAtendimento'].forEach((v) {
        itensAtendimento!.add(ItensAtendimento.fromJson(v));
      });
    }
  }
}

class ItensAtendimento {
  late String descricao;

  ItensAtendimento({
    required this.descricao,
  });

  ItensAtendimento.fromJson(Map<String, dynamic> json) {
    descricao = json['descricao'] as String;
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['descricao'] = descricao;
    return data;
  }
}

import 'package:bloc/bloc.dart';
import 'package:permission_handler/permission_handler.dart';

part 'location_permission_state.dart';

class LocationPermissionCubit extends Cubit<LocationPermissionState>{
  LocationPermissionCubit() : super(LocationPermissionInitial());

  Future<void> checkLocationPermission() async {
    emit(LocationPermissionLoading());
    PermissionStatus status = await Permission.location.status;
    if(status.isGranted){
       emit(LocationPermissionGranted());
    } else {
      emit(LocationPermissionDenied());
    }
  }

  Future<void> requestLocationPermission() async {
    emit(LocationPermissionLoading());
    PermissionStatus status = await Permission.location.request();
    if(status.isGranted){
       emit(LocationPermissionGranted());
    } else if(status.isPermanentlyDenied){
      emit(LocationPermissionPermanentlyDenied());
    } else {
      emit(LocationPermissionDenied());
    }
  }
}
import 'package:cooperado_minha_unimed/shared/utils/graph_utils.dart';
import 'package:cooperado_minha_unimed/shared/widgets/comparative-production/bloc/comparative_production_cubit.dart';
import 'package:cooperado_minha_unimed/shared/widgets/comparative-production/view/widget/bargraph_production.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ProductionChart extends StatelessWidget {
  const ProductionChart({super.key});

  @override
  Widget build(BuildContext context) {
    List<double> allValues = [];
    List<ProductionSerie> productionSerie =
        context.read<ComparativeProductionCubit>().prodcutionList;
    for (ProductionSerie serie in productionSerie) {
      if (serie.valor != null) {
        if (serie.valor! > 0.0) {
          allValues.add(serie.valor!);
        }
      }
    }
    double maxY = GraphUtils.findMax(allValues);

    return ProductionBarGraph(maxY: maxY, data: productionSerie);
  }
}

class ProductionSerie {
  final double? valor;
  final String title;
  final Color barColor;

  ProductionSerie(
      {required this.valor, required this.title, required this.barColor});
}

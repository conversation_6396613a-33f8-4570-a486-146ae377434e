import 'package:cooperado_minha_unimed/shared/vo/servicos/honorary_solicitation.vo.dart';
import 'package:equatable/equatable.dart';

abstract class SolicitationParticipationStatusState extends Equatable {
  const SolicitationParticipationStatusState();
}

class SolicitationParticipationStatusInitial
    extends SolicitationParticipationStatusState {
  @override
  List<Object> get props => [];
}

class LoadingGetSolicitationParticipationStatusState
    extends SolicitationParticipationStatusState {
  @override
  List<Object> get props => [];
}

class ErrorGetSolicitationParticipationStatusState
    extends SolicitationParticipationStatusState {
  final String message;
  @override
  List<Object> get props => [message];
  const ErrorGetSolicitationParticipationStatusState(this.message);
}

class DoneGetSolicitationParticipationStatusState
    extends SolicitationParticipationStatusState {
  final Iterable<HonorarySolicitation>? list;
  @override
  List<Object?> get props => [list];

  const DoneGetSolicitationParticipationStatusState(this.list);
}

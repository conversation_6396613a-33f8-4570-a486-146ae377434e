import 'package:flutter/material.dart';

class UnimedCard extends StatefulWidget {
  final Widget? child;
  const UnimedCard({super.key, this.child});

  @override
  UnimedCardState createState() => UnimedCardState();
}

class UnimedCardState extends State<UnimedCard> {
  @override
  Widget build(BuildContext context) {
    return Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(10),
            topLeft: Radius.circular(10),
            topRight: Radius.circular(10),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey,
              offset: Offset(0.0, 1.0), //(x,y)
              blurRadius: 6.0,
            ),
          ],
        ),
        child: widget.child);
  }
}

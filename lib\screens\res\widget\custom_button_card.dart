import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';

class CustomButtonCard extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;

  const CustomButtonCard({
    super.key,
    required this.text,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        elevation: 0,
        foregroundColor: CooperadoColors.greenDark,
        backgroundColor: const Color.fromARGB(255, 236, 250, 248),
        side: const BorderSide(
          color: Color.fromRGBO(10, 95, 85, 1),
          width: 1,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
      ),
      onPressed: onPressed,
      child: Text(
        text,
        style: const TextStyle(
          color: Color.fromRGBO(10, 95, 85, 1),
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}

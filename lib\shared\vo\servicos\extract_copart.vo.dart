import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';

class ExtractCopartVO {
  int? codigo;
  String? codigoUnimed;
  String? nome;
  String? nomeFantasia;
  bool? suspensaoCovid;
  String? enderecos;
  String? especialidades;
  String? produtosAtendidos;
  String? qualificacoes;
  String? grupoPrestador;
  String? mensagens;
  String? crm;
  String? cpf;
  String? dataNascimento;
  String? usaApp;
  dynamic saldoIntegralizar;
  dynamic saldoIntegralizado;
  dynamic totalDevolucao;
  dynamic totalSubscricao;
  List<Extrato>? extrato;
  List<ExtratoAporte>? extratoAporte;
  String? especialidadePrincipal;

  ExtractCopartVO(
      {this.codigo,
      this.codigoUnimed,
      this.nome,
      this.nomeFantasia,
      this.suspensaoCovid,
      this.enderecos,
      this.especialidades,
      this.produtosAtendidos,
      this.qualificacoes,
      this.grupoPrestador,
      this.mensagens,
      this.crm,
      this.cpf,
      this.dataNascimento,
      this.usaApp,
      this.saldoIntegralizar,
      this.saldoIntegralizado,
      this.totalDevolucao,
      this.totalSubscricao,
      this.extrato,
      this.extratoAporte,
      this.especialidadePrincipal});

  String getSaldoIntegralizadoFormat() =>
      StringUtils.formatMoneyDynamic(saldoIntegralizado!);

  String getSaldoIntegralizarFormat() =>
      StringUtils.formatMoneyDynamic(saldoIntegralizar!);

  String getTotalSubscricaoFormat() =>
      StringUtils.formatMoneyDynamic(totalSubscricao!);

  String getTotalDevolucaoFormat() =>
      StringUtils.formatMoneyDynamic(totalDevolucao!);

  ExtractCopartVO.fromJson(Map<String, dynamic> json) {
    codigo = json['codigo'];
    codigoUnimed = json['codigoUnimed'];
    nome = json['nome'];
    nomeFantasia = json['nomeFantasia'];
    suspensaoCovid = json['suspensaoCovid'];
    enderecos = json['enderecos'];
    especialidades = json['especialidades'];
    produtosAtendidos = json['produtosAtendidos'];
    qualificacoes = json['qualificacoes'];
    grupoPrestador = json['grupoPrestador'];
    mensagens = json['mensagens'];
    crm = json['crm'];
    cpf = json['cpf'];
    dataNascimento = json['dataNascimento'];
    usaApp = json['usaApp'];
    saldoIntegralizar = json['saldoIntegralizar'];
    saldoIntegralizado = json['saldoIntegralizado'];
    totalDevolucao = json['totalDevolucao'];
    totalSubscricao = json['totalSubscricao'];
    if (json['extrato'] != null) {
      extrato = [];
      json['extrato'].forEach((v) {
        extrato!.add(Extrato.fromJson(v));
      });
    }
    if (json['extratoAporte'] != null) {
      extratoAporte = [];
      json['extratoAporte'].forEach((v) {
        extratoAporte!.add(ExtratoAporte.fromJson(v));
      });
    }
    especialidadePrincipal = json['especialidadePrincipal'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codigo'] = codigo;
    data['codigoUnimed'] = codigoUnimed;
    data['nome'] = nome;
    data['nomeFantasia'] = nomeFantasia;
    data['suspensaoCovid'] = suspensaoCovid;
    data['enderecos'] = enderecos;
    data['especialidades'] = especialidades;
    data['produtosAtendidos'] = produtosAtendidos;
    data['qualificacoes'] = qualificacoes;
    data['grupoPrestador'] = grupoPrestador;
    data['mensagens'] = mensagens;
    data['crm'] = crm;
    data['cpf'] = cpf;
    data['dataNascimento'] = dataNascimento;
    data['usaApp'] = usaApp;
    data['saldoIntegralizar'] = saldoIntegralizar;
    data['saldoIntegralizado'] = saldoIntegralizado;
    data['totalDevolucao'] = totalDevolucao;
    data['totalSubscricao'] = totalSubscricao;
    if (extrato != null) {
      data['extrato'] = extrato!.map((v) => v.toJson()).toList();
    }
    if (extratoAporte != null) {
      data['extratoAporte'] = extratoAporte!.map((v) => v.toJson()).toList();
    }
    data['especialidadePrincipal'] = especialidadePrincipal;
    return data;
  }
}

class Extrato {
  String? verba;
  String? descricaoVerba;
  String? statusVerba;
  String? data;
  dynamic
      valorLancamento; //foi colocado como dynamic pq as vezes ele vem int, e as vezes double
  String? numeroBoleto;
  String? statusBoleto;
  String? valorBoleto;
  String? saldo;
  dynamic subscricao;
  dynamic integralizacao;

  Extrato(
      {this.verba,
      this.descricaoVerba,
      this.statusVerba,
      this.data,
      this.valorLancamento,
      this.numeroBoleto,
      this.statusBoleto,
      this.valorBoleto,
      this.saldo,
      this.subscricao,
      this.integralizacao});

  Extrato.fromJson(Map<String, dynamic> json) {
    verba = json['verba'];
    descricaoVerba = json['descricaoVerba'];
    statusVerba = json['statusVerba'];
    data = json['data'];
    valorLancamento = json['valorLancamento'];
    numeroBoleto = json['numeroBoleto'];
    statusBoleto = json['statusBoleto'];
    valorBoleto = json['valorBoleto'];
    saldo = json['saldo'];
    subscricao = json['subscricao'];
    integralizacao = json['integralizacao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['verba'] = verba;
    data['descricaoVerba'] = descricaoVerba;
    data['statusVerba'] = statusVerba;
    data['data'] = this.data;
    data['valorLancamento'] = valorLancamento;
    data['numeroBoleto'] = numeroBoleto;
    data['statusBoleto'] = statusBoleto;
    data['valorBoleto'] = valorBoleto;
    data['saldo'] = saldo;
    data['subscricao'] = subscricao;
    data['integralizacao'] = integralizacao;
    return data;
  }
}

class ExtratoAporte {
  dynamic verba;
  String? descricaoVerba;
  String? statusVerba;
  String? data;
  dynamic valorLancamento;
  int? numeroBoleto;
  String? statusBoleto;
  dynamic valorBoleto;
  String? saldo;
  String? subscricao;
  String? integralizacao;

  ExtratoAporte(
      {this.verba,
      this.descricaoVerba,
      this.statusVerba,
      this.data,
      this.valorLancamento,
      this.numeroBoleto,
      this.statusBoleto,
      this.valorBoleto,
      this.saldo,
      this.subscricao,
      this.integralizacao});

  ExtratoAporte.fromJson(Map<String, dynamic> json) {
    verba = json['verba'];
    descricaoVerba = json['descricaoVerba'];
    statusVerba = json['statusVerba'];
    data = json['data'];
    valorLancamento = json['valorLancamento'];
    numeroBoleto = json['numeroBoleto'];
    statusBoleto = json['statusBoleto'];
    valorBoleto = json['valorBoleto'];
    saldo = json['saldo'];
    subscricao = json['subscricao'];
    integralizacao = json['integralizacao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['verba'] = verba;
    data['descricaoVerba'] = descricaoVerba;
    data['statusVerba'] = statusVerba;
    data['data'] = this.data;
    data['valorLancamento'] = valorLancamento;
    data['numeroBoleto'] = numeroBoleto;
    data['statusBoleto'] = statusBoleto;
    data['valorBoleto'] = valorBoleto;
    data['saldo'] = saldo;
    data['subscricao'] = subscricao;
    data['integralizacao'] = integralizacao;
    return data;
  }
}

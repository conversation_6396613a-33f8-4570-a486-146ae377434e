import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/res/res_exam_result_laboratory_detail_model.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/info_row.dart';
import 'package:flutter/material.dart';

class ExamResultLaboratoryDetail extends StatelessWidget {
  final String tuss;
  final List<ResExamResultLaboratoryDetailModel> listResultExam;

  const ExamResultLaboratoryDetail(
      {super.key, required this.listResultExam, required this.tuss});

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                height: 4,
                width: 100,
                margin: const EdgeInsets.symmetric(
                  vertical: 10,
                ),
                decoration: BoxDecoration(
                  color: CooperadoColors.grayLight3,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            Padding(
                padding: const EdgeInsets.only(top: 27),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Exame - TUSS:$tuss',
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: CooperadoColors.blackText,
                      ),
                    ),
                  ],
                )),
            const SizedBox(
              height: 20,
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    ...listResultExam.map(
                      (detail) => Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Container(
                          decoration: BoxDecoration(
                            border:
                                Border.all(color: CooperadoColors.grayLight3),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: Column(
                              children: [
                                InfoRow(
                                    label: 'Solicitante',
                                    value: detail.applicants.first.name),
                                const SizedBox(height: 16),
                                InfoRow(label: 'Descrição', value: detail.name),
                                const SizedBox(height: 16),
                                InfoRow(label: 'TUSS', value: tuss),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ));
  }
}

import 'dart:convert';

import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:http_client/http_client.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:cooperado_minha_unimed/shared/widgets/comparative-production/models/comparative_production_model.dart';
import 'package:intl/intl.dart';

class ComparativeProductionService {
  final UnimedHttpClient httpClient;

  ComparativeProductionService(this.httpClient);

  final logger = UnimedLogger(className: 'ComparativeProductionService');
  final Duration timeout = const Duration(seconds: 30);

  Future<ComparativeProductionModel> getIndicadoresCompProducao(
      {required DateTime periodo}) async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();
    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";
    final tokenPerfilApps =
        await Locator.instance!.get<AuthApi>().tokenPerfilApps();

    try {
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/transparencia/indicadores/periodo/${DateFormat('MM-yyyy').format(periodo)}?tokenPortal=$token&crm=${credentials?.crm}';
      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      if (response.statusCode == 200) {
        final data = ComparativeProductionModel.fromJson(
            jsonDecode(utf8.decode(response.bodyBytes))['retorno']);
        logger.d(
            'getIndicadoresCompProducao success ${StringUtils.limitString(response.body, 200)}');
        return data;
      } else {
        final message = jsonDecode(response.body)['message'] ??
            'Não foi possível no momento';
        logger.e(
            'getIndicadoresCompProducao statusCode : ${response.statusCode} ${StringUtils.limitString(response.body, 200)}');
        throw TransparenciaException('$message');
      }
    } on UnimedException catch (ex) {
      logger.e('getIndicadoresCompProducao ${ex.runtimeType}: $ex');
      throw TransparenciaException(ex.message);
    } catch (ex) {
      logger.e('getIndicadoresCompProducao exception: $ex');
      throw TransparenciaException('Não foi possível no momento.');
    }
  }
}

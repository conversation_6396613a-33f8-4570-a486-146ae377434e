import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:rxdart/rxdart.dart';

class GeolocationService {
  final logger = UnimedLogger(className: 'GeolocationService');

  Position? _position;

  final BehaviorSubject<Position?> _positionController =
      BehaviorSubject<Position?>();

  GeolocationService();

  Future<void> requestPermission() async {
    PermissionStatus permission = await Permission.location.request();
    if (permission == PermissionStatus.restricted) {
      logger.d('Permission status restricted');
    } else if (permission == PermissionStatus.granted) {
      logger.d('Permission status granted');
    } else if (permission == PermissionStatus.denied) {
      logger.d('Permission status denied');
    } else if (permission == PermissionStatus.permanentlyDenied) {
      openAppSettings();
      logger.d('Permission status permanently denied');
    } else if (permission == PermissionStatus.limited) {
      logger.d('Permission status unknown, requesting permission');
      PermissionStatus statusRequest = await Permission.location.request();
      logger.d('statusRequest : $statusRequest');
    } else {
      logger.d('Permission status $permission');
    }

    return;
  }

  Future<Position?> getCurrentPosition({
    bool force = false,
    String textConcat = "",
  }) async {
    if (force || _position == null) {
      final permission = await Permission.location.status;
      if (permission == PermissionStatus.granted) {
        _position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.best,
        );
      }
    }

    logger.d('getCurrentPosition $textConcat: $_position');

    return _position;
  }

  Future<ValueStream<Position?>> getCurrentPositionUpdates() async {
    try {
      // _positionController = StreamController<Position>.broadcast();

      Geolocator.getPositionStream(
        desiredAccuracy: LocationAccuracy.high,
        distanceFilter: 1,
        forceAndroidLocationManager: true,
      ).listen((event) {
        _positionController.add(event);
      });

      return _positionController.stream;
    } catch (e) {
      logger.e('getCurrentPositionUpdates error: $e');
      rethrow;
    }
  }

  Future<void> closePositionStream() async {
    await _positionController.close();
    // _positionController = null;
  }

  void cleanData() {
    _position = null;
  }
}

import 'package:cooperado_minha_unimed/bloc/servicos/consultar-cid/consult_cid_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/consultar-cid/consult_cid_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/vo/servicos/cid_list.vo.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:cooperado_minha_unimed/shared/widgets/snack.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class CidListScreen extends StatefulWidget {
  const CidListScreen({super.key});

  @override
  CidListScreenState createState() => CidListScreenState();
}

class CidListScreenState extends State<CidListScreen> {
  FocusNode? focusTextFieldSearch;
  final TextEditingController _tecTexto = TextEditingController();
  ScrollController? scrollController;
  int currentPage = 1;
  int maxPages = 1;
  late CidList cidList;
  String? lastKeySearch;
  bool isEnableSearch = false;

  @override
  void initState() {
    super.initState();

    cidList = CidList();
    lastKeySearch = "";

    scrollController = ScrollController();

    context.read<ConsultCidCubit>().setInitialState();

    scrollController!.addListener(() {
      if (scrollController!.position.atEdge) {
        final value = _tecTexto.text.trim();
        if (currentPage <= maxPages && value.isNotEmpty) {
          context
              .read<ConsultCidCubit>()
              .getConsultCidEvent(value, currentPage);
          currentPage++;
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text("Consulta Cid"),
          backgroundColor: CooperadoColors.tealGreenDark,
        ),
        body: SafeArea(
          child: SingleChildScrollView(
              controller: scrollController,
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Container(
                        color: CooperadoColors.tealGreen,
                        child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 5),
                            child: _textField(context, _tecTexto))),
                    const SizedBox(height: 10),
                    BlocBuilder<ConsultCidCubit, ConsultCidState>(
                        builder: (context, state) {
                      if (state is DoneGetConsultCidState) {
                        maxPages = state.cidList.totalPaginas!;

                        if (cidList.listaCid == null) {
                          cidList = state.cidList;
                        } else {
                          cidList.listaCid!.addAll(state.cidList.listaCid!);
                        }

                        return _listSolicitations(cidList);
                      } else if (state is LoadingGetConsultCidState) {
                        return Column(
                          children: <Widget>[
                            _listSolicitations(cidList),
                            const SpinKitCircle(
                              color: CooperadoColors.tealGreen,
                            )
                          ],
                        );
                      }
                      if (state is ErrorGetConsultCidState) {
                        return Center(
                            child: ErrorBanner(message: state.message));
                      }

                      return Container(
                          alignment: Alignment.center,
                          child: const Text(
                              "O termo pesquisado deve ter no mínimo 3 caracteres",
                              textAlign: TextAlign.center,
                              style:
                                  TextStyle(color: CooperadoColors.grayDark)));
                    })
                  ])),
        ));
  }

  Widget _listSolicitations(CidList list) {
    List<Widget> widgets = [];

    if (list.listaCid == null || list.listaCid!.isEmpty) return Container();

    for (Cid e in list.listaCid!) {
      widgets.add(Padding(
          padding: const EdgeInsets.only(left: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const SizedBox(
                height: 10,
              ),
              // Text(
              //   "Fornecedor - " + e.fornecedor.nome,
              //   style: TextStyle(fontWeight: FontWeight.bold),
              // ),
              const SizedBox(
                height: 5,
              ),
              Text("${e.codigo} - ${e.descricao!}"),
              const SizedBox(
                height: 10,
              ),
            ],
          )));

      widgets.add(const Divider(color: CooperadoColors.grayDark));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  Widget _textField(BuildContext context, TextEditingController controller) {
    return Stack(
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.only(top: 4.0, right: 52.0, bottom: 4.0),
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(10.0),
                  bottomLeft: Radius.circular(10.0)),
            ),
            child: TextFormField(
              enableInteractiveSelection: false,
              autofocus: false,
              controller: controller,
              textInputAction: TextInputAction.search,
              style: const TextStyle(color: unimedGreen),
              onChanged: (term) {
                setState(() =>
                    isEnableSearch = term.trim().length > 2 ? true : false);
              },
              onFieldSubmitted: (term) {
                if (term.trim().length > 2) _search(context);
              },
              maxLength: 27,
              maxLines: 1,
              decoration: const InputDecoration(
                counter: Offstage(),
                hintText: 'Consulta Cid',
                hintMaxLines: null,
                contentPadding: EdgeInsets.only(
                  top: 12,
                  left: 10,
                  right: 10,
                  bottom: 6,
                ),
                border: InputBorder.none,
              ),
            ),
          ),
        ),
        Align(
          alignment: Alignment.centerRight,
          child: Padding(
              padding: const EdgeInsets.only(top: 4, bottom: 10   ),
              child: Container(
                  decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(20.0),
                      )),
                  child: Padding(
                    padding: const EdgeInsets.only(top: 2, right: 2, bottom: 2),
                    child: InkWell(
                      splashColor: unimedGreen.shade400, // inkwell color
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(10.0),
                              topRight: Radius.circular(20.0),
                              bottomLeft: Radius.circular(20.0)),
                          //border: Border.all(color: Colors.white, width: 3),
                          color: isEnableSearch
                              ? unimedGreen
                              : CooperadoColors.grayLight2.withAlpha(100),
                        ),
                        width: 52,
                        height: 44,
                        child: const Icon(
                          Icons.send,
                          color: Colors.white,
                        ),
                      ),
                      onTap: () {
                        if (isEnableSearch) _search(context);
                      },
                    ),
                  ))),
        ),
      ],
    );
  }

  void _search(BuildContext context) {
    final value = _tecTexto.text.trim();
    final ConsultCidState currentState = context.read<ConsultCidCubit>().state;

    if (value != lastKeySearch || currentState is ErrorGetConsultCidState) {
      currentPage = 1;
      maxPages = 1;
      cidList.listaCid = [];
    }

    if (value.isEmpty || value.length < 3) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          Snack.warning(
            'Digite o que deseja procurar',
            duration: const Duration(seconds: 1),
          ),
        );
      });
    } else {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        if (currentPage <= maxPages) {
          context
              .read<ConsultCidCubit>()
              .getConsultCidEvent(value, currentPage);

          currentPage++;
          lastKeySearch = value;
        }
      });
    }
  }
}

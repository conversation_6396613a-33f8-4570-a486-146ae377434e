import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';

class TitleResult extends StatelessWidget {
  final String title;
  final String quantity;
  const TitleResult({super.key, required this.title, required this.quantity});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.w500,
            color: CooperadoColors.blackText,
          ),
        ),
        Text(
          quantity,
          style: const TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.w500,
            color: CooperadoColors.greenDark,
          ),
        ),
      ],
    );
  }
}

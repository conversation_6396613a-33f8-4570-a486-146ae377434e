// ignore_for_file: sdk_version_since

import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:graphql/client.dart';
import 'package:http_client/http_client.dart';

class GraphQlApi {
  final UnimedHttpClient httpClient;

  GraphQlApi(this.httpClient);

  final logger = UnimedLogger(className: 'GraphQlApi');

  Future<GraphQLClient> getGithubGraphQLClient() async {
    final tokenPerfilApps =
        await Locator.instance!.get<AuthApi>().tokenPerfilApps();

    final Link link = HttpLink(
      FlavorConfig.instance!.values.graphql.url,
      defaultHeaders: {
        'Authorization': 'Bearer $tokenPerfilApps',
      },
    );

    return GraphQLClient(
      cache: GraphQLCache(),
      link: link,
      queryRequestTimeout: const Duration(seconds: 60),
    );
  }
}

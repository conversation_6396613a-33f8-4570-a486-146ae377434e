import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/shared/api/notice.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/demostrativo_resultado.dart';
import 'package:equatable/equatable.dart';

part 'read_state.dart';

class ReadCubit extends Cubit<ReadState> {
  ReadCubit() : super(ReadInitial());
  readNewsEvent(Noticia noticiasPortal) async {
    emit(LoadingReadNewsState());
    try {
      final String? message =
          await Locator.instance!<NoticeApi>().readNews(noticiasPortal);

      emit(DoneReadNewsState(message: message));
    } catch (ex) {
      emit(ErrorReadNewsState('$ex'));
    }
  }
}

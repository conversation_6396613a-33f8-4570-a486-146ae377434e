import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/vo/score_comparison.vo.dart';
import 'package:cooperado_minha_unimed/shared/widgets/show_up_animation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BodyCard extends StatefulWidget {
  final List<Categorias>? list;
  const BodyCard({super.key, this.list});
  @override
  BodyCardState createState() => BodyCardState();
}

class BodyCardState extends State<BodyCard> {
  @override
  Widget build(BuildContext context) {
    return _expanded();
  }

 Widget _expanded() {
  return ShowUp(
    child: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
      builder: (context, sensitiveState) {
        return ListView.builder(
          physics: const PageScrollPhysics(),
          shrinkWrap: true,
          itemCount: widget.list!.length,
          itemBuilder: (context, index) {
            final item = widget.list!.elementAt(index);
            return Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Row(
                    children: <Widget>[
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Categorias.howIcon(item.codigo),
                      ),
                      Container(
                        alignment: Alignment.centerLeft,
                        width: 130,
                        child: Text(
                          item.nome ?? '',
                        ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: <Widget>[
                      Text(
                        !sensitiveState.isSensitiveDataVisible
                            ? '*****'
                            : item.scoreFormatted,
                        style: const TextStyle(
                            color: CooperadoColors.limaColor, fontSize: 22),
                      ),
                      const Text(
                        "PONTOS",
                        style: TextStyle(fontSize: 12),
                      )
                    ],
                  )
                ],
              ),
            );
          },
        );
      },
    ),
  );
}
}

import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/beneficiary/beneficiary_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/financial/invoice_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/invoice.model.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/constants.dart';
import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:cooperado_minha_unimed/shared/widgets/pdf_view/pdf_view_platform.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:share_plus/share_plus.dart';

const textBottom =
    'Copie o código de barras e pague sua invoice em aplicativos de internet banking.';
const double fontSize = 13;

class CardFatura extends StatefulWidget {
  final InvoiceModel invoice;

  const CardFatura({super.key, required this.invoice});

  @override
  CardinvoicesState createState() => CardinvoicesState();
}

class CardinvoicesState extends State<CardFatura> {
  bool canClick = true;
  late User user;

  @override
  void initState() {
    super.initState();

    user = context.read<AuthCubit>().userLogged;
  }

  @override
  Widget build(BuildContext context) {
    return (widget.invoice.situacao == FaturaListaSituacao.pago)
        ? _invoicePaga()
        : _invoiceAberta();
  }

  Widget _invoiceAberta() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: <Widget>[
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.0),
                color: Colors.grey[200],
                boxShadow: const [
                  BoxShadow(
                    color: Colors.grey,
                    offset: Offset(0.0, 0.8), //(x,y)
                    blurRadius: 2.0,
                  ),
                ],
              ),
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[_panelLeft(), _buttonGerarinvoice()],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: <Widget>[
                  Flexible(child: Container()),
                  // Flexible(
                  //   child: ButtonSendInvoice(
                  //     fatura: widget.fatura,
                  //   ),
                  // ),
                  // Flexible(child: _sendFaturaEmail()),
                  Flexible(child: _buttonCopiarCodigoBarras())
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _invoicePaga() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                const Text(
                  'VALOR',
                  style: TextStyle(
                      color: CooperadoColors.grayDark, fontSize: fontSize),
                ),
           BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
  builder: (context, sensitiveState) {
    bool isSensitiveDataVisible = !sensitiveState.isSensitiveDataVisible;

    String displayValue = isSensitiveDataVisible
        ? '*****'
        : (widget.invoice.valorAtualizado == 0.0 && widget.invoice.situacao == 1)
            ? widget.invoice.valorFormatted
            : widget.invoice.valorAtualizadoFormatted;

    String displayVencimento = isSensitiveDataVisible
        ? '*****'
        : widget.invoice.dataVencimentoFormatted;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          displayValue,
          style: const TextStyle(
              color: CooperadoColors.grayDark, fontSize: fontSize * 2),
        ),
        Text(
          'VENCIMENTO: $displayVencimento',
          style: const TextStyle(
              color: CooperadoColors.grayDark, fontSize: fontSize),
        ),
      ],
    );
  },
)
              ],
            ),
            const Row(
              children: <Widget>[
                Icon(
                  Icons.check_box,
                  color: CooperadoColors.green,
                ),
                Text(
                  "PAGA",
                  style: TextStyle(color: Colors.black45),
                )
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _panelLeft() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text(
          'VALOR',
          style: TextStyle(color: CooperadoColors.grayDark, fontSize: fontSize),
        ),
       BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
  builder: (context, sensitiveState) {
    bool isSensitiveDataVisible = !sensitiveState.isSensitiveDataVisible;

    String displayValue = isSensitiveDataVisible
        ? '*****'
        : (widget.invoice.valorAtualizado == 0.0 && widget.invoice.situacao == 1)
            ? widget.invoice.valorFormatted
            : widget.invoice.valorAtualizadoFormatted;

    String displayVencimento = isSensitiveDataVisible
        ? '*****'
        : widget.invoice.dataVencimentoFormatted;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          displayValue,
          style: const TextStyle(
              color: CooperadoColors.green,
              fontSize: fontSize * 2,
              fontWeight: FontWeight.bold),
        ),
        Text(
          'VENCIMENTO: $displayVencimento',
          style: const TextStyle(
              color: CooperadoColors.grayDark, fontSize: fontSize),
        ),
      ],
    );
  },
)
      ],
    );
  }

  Widget _buttonGerarinvoice() {
    return Container(
        decoration: const BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.grey,
              offset: Offset(0.0, 0.8), //(x,y)
              blurRadius: 2.0,
            ),
          ],
          color: CooperadoColors.green,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(10.0),
              topRight: Radius.circular(10.0),
              bottomLeft: Radius.circular(10.0)),
        ),
        child: BlocConsumer<InvoiceCubit, InvoiceState>(
            listener: (context, state) {
          if (state is LoadingInvoiceState) {
            setState(() {
              canClick = false;
            });
          }
          if (state is LoadedInvoicePdfState) {
            setState(() => canClick = true);
            if (state.invoiceDate == widget.invoice.dataReferencia) {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PDFViewPlatform(
                    state.pathFile,
                    isPath: true,
                    share: true,
                    filename:
                        'Fatura_${StringUtils.formatMonth(widget.invoice.mesVencimento)}_${StringUtils.formatMonth(widget.invoice.anoVencimento)}',
                    title:
                        'Fatura - ${StringUtils.formatMonth(widget.invoice.mesVencimento)}',
                  ),
                ),
              );
            }
          } else if (state is ErrorInvoiceState) {
            setState(() {
              canClick = true;
            });
            showDialog(
              context: context,
              builder: (context) => CooperadoAlertDialog(
                textWidget: Text(state.message, textAlign: TextAlign.center),
                onPressed: () => Navigator.of(context).pop(),
              ),
            );
          }
        }, builder: (context, state) {
          if (state is LoadingInvoiceState &&
              state.dataReferencia == widget.invoice.dataReferencia &&
              state.tipoRetorno == TipoRetornoFaturas.pdf) {
            return const Padding(
              padding: EdgeInsets.all(16.0),
              child: Column(
                children: <Widget>[
                  SpinKitThreeBounce(
                    color: Colors.white,
                    size: 20,
                  ),
                  Text(
                    "Gerando",
                    style: TextStyle(color: Colors.white),
                  )
                ],
              ),
            );
          } else {
            return InkWell(
                splashColor: CooperadoColors.green,
                onTap: canClick
                    ? () {
                        BlocProvider.of<InvoiceCubit>(context).getFatura(
                            carteira: BlocProvider.of<BeneficiaryCubit>(context)
                                .selectedCard
                                .carteira,
                            data: widget.invoice.dataReferencia,
                            tipoRetorno: TipoRetornoFaturas.pdf);
                      }
                    : null,
                child: const Padding(
                    padding:
                        EdgeInsets.symmetric(vertical: 16.0, horizontal: 20.0),
                    child: Column(
                      children: <Widget>[
                        Icon(
                          Icons.line_style,
                          size: 32.0,
                          color: Colors.white,
                        ),
                        Text(
                          'GERAR',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: fontSize,
                          ),
                        ),
                      ],
                    )));
          }
        }));
  }

  Widget _buttonCopiarCodigoBarras() {
    return BlocConsumer<InvoiceCubit, InvoiceState>(
      listener: (context, state) {
        if (state is LoadedInvoiceBarCodeState) {
          setState(() {
            canClick = true;
          });

          if (state.invoiceDate == widget.invoice.dataReferencia) {
            Share.share(state.barCode, subject: 'Codigo de Barras');
          }
        }
      },
      builder: (context, state) {
        if (state is LoadingInvoiceState &&
            state.dataReferencia == widget.invoice.dataReferencia &&
            state.tipoRetorno == TipoRetornoFaturas.codigoDeBarras) {
          return const SpinKitThreeBounce(
            color: CooperadoColors.green,
            size: 20,
          );
        } else {
          return Container(
            decoration: BoxDecoration(
              border: Border.all(color: unimedGreen),
              borderRadius: const BorderRadius.all(Radius.circular(5.0)),
            ),
            child: InkWell(
              splashColor: CooperadoColors.green,
              onTap: canClick
                  ? () {
                      BlocProvider.of<InvoiceCubit>(context).getFatura(
                          carteira: BlocProvider.of<BeneficiaryCubit>(context)
                              .selectedCard
                              .carteira,
                          data: widget.invoice.dataReferencia,
                          tipoRetorno: TipoRetornoFaturas.codigoDeBarras);
                    }
                  : null,
              child: const Wrap(
                children: <Widget>[
                  Padding(
                    padding: EdgeInsets.all(9.0),
                    child: Text(
                      'CÓDIGO DE BARRAS',
                      overflow: TextOverflow.clip,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: CooperadoColors.green,
                        fontSize: fontSize,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }
      },
    );
  }
}

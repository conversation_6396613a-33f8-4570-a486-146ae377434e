import 'package:cooperado_minha_unimed/bloc/e-card/activation-check/activation_check_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/history-activations/history_activation_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/history-activations/history_activation_state.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/timer-card/timer_card.dart';
import 'package:cooperado_minha_unimed/bloc/profile/profile_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/ecard-models/history_transaction_model.dart';
import 'package:cooperado_minha_unimed/screens/e-card/widgets/card_ecard_widget.dart';
import 'package:cooperado_minha_unimed/screens/e-card/widgets/datar_range_picker.dart';
import 'package:cooperado_minha_unimed/screens/e-card/widgets/multiselection_buttons_widget.dart';
import 'package:cooperado_minha_unimed/screens/e-card/widgets/transaction_history_tile.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/empty_list.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

class EcardScreen extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const EcardScreen({super.key, required this.analytics, required this.observer});

  @override
  EcardScreenState createState() => EcardScreenState();
}

List<String> _historyLocations = [];
List<String> _selectedLocations = [];
final List<String> _periodos = ['7', '15', '30', '60', '90'];
List<String> _selectedPeriods = [];
DateTime? _startDate;
DateTime? _endDate;

late String _codPrestador;

class EcardScreenState extends State<EcardScreen> {
  late HistoryEcardActivationCubit _historyCubit;

  Future<void> _fetchHistory({List<String>? locations, String? periods}) async {
   
    if (periods != null && periods.isNotEmpty) {
      int days = int.tryParse(periods) ?? 30;
      _startDate = DateTime.now().toUtc().subtract(Duration(days: days));
      _endDate = DateTime.now().toUtc();
    } 

    else if (_startDate == null || _endDate == null) {
      _startDate = DateTime.now().toUtc().subtract(const Duration(days: 30));
      _endDate = DateTime.now().toUtc();
    }
    

    context.read<HistoryEcardActivationCubit>().historyEcard(
          codPrestador: _codPrestador,
          startDate: _startDate!,
          endDate: _endDate!,
          locations: locations,
        );

    _loadLocations();
  }

  _fetchEcard() {
    context.read<EcardActivationCheckCubit>().ecardActivationCheck(
          codPrestador: context.read<ProfileCubit>().user.codPrestador.toString(),
        );
    context.read<TimerEcardCubit>().setStateTimerEcard(
          isTimerRunner: false,
        );
  }

  void _loadLocations() {
    _historyLocations.clear();
    for (var element in _historyCubit.listHistoryActivation) {
      if (!_historyLocations.contains(element.viewAddress!.streetName)) {
        _historyLocations.add(element.viewAddress!.streetName!);
      }
    }
  }

  @override
  void initState() {
    super.initState();

    _codPrestador = context.read<ProfileCubit>().user.codPrestador.toString();
    _historyCubit = BlocProvider.of<HistoryEcardActivationCubit>(context);

    widget.analytics.logScreenView(
      screenName: 'E-card',
      screenClass: 'EcardScreen',
    );

    _fetchHistory();

    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  }

  Map<String, List<HistoryTransactionEcardModel>> _groupTransactionsByDate(
      List<HistoryTransactionEcardModel> transactions) {
    transactions.sort((a, b) => DateTime.parse(b.startDate!).compareTo(DateTime.parse(a.startDate!)));

    final Map<String, List<HistoryTransactionEcardModel>> groupedTransactions = {};
    for (var transaction in transactions) {
      DateTime dateTime = DateTime.parse(transaction.startDate!);
      String formattedDate =
          '${DateFormat('dd').format(dateTime)} de ${DateFormat('MMMM', 'pt_BR').format(dateTime)} de ${DateFormat('yyyy').format(dateTime)}';

      if (!groupedTransactions.containsKey(formattedDate)) {
        groupedTransactions[formattedDate] = [];
      }
      groupedTransactions[formattedDate]!.add(transaction);
    }
    _loadLocations();
    return groupedTransactions;
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    super.dispose();
    _selectedLocations.clear();

    _selectedPeriods.clear();
    _startDate = null;
    _endDate = null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: false,
      appBar: AppBar(
        centerTitle: true,
        title: const Text("E-card"),
        backgroundColor: CooperadoColors.tealGreenDark,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 15.0),
            child: IconButton(
              icon: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                builder: (context, state) {
                  return Icon(
                    state.isSensitiveDataVisible ? Icons.visibility : Icons.visibility_off,
                  );
                },
              ),
              onPressed: () {
                context.read<SensitiveDataCubit>().toggleSensitiveDataVisibility();
              },
            ),
          ),
        ],
      ),
      backgroundColor: CooperadoColors.grayLight,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: RefreshIndicator(
            onRefresh: () async {
              _selectedLocations.clear();
              _selectedPeriods.clear();
              _startDate = null;
              _endDate = null;
              _fetchEcard();
              _fetchHistory();
            },
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                children: <Widget>[
                  const CardWidget(),
                  Container(
                    width: double.infinity,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16.0),
                        topRight: Radius.circular(16.0),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'Histórico de Transações',
                                style: TextStyle(
                                  fontSize: 18.0,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.filter_list),
                                onPressed: () {
                                  _fetchHistory(locations: null, periods: null);
                                  showFilterModal(context);
                                },
                              ),
                            ],
                          ),
                          const Divider(
                            color: CooperadoColors.opcionalGray3,
                            thickness: 1.0,
                          ),
                          BlocBuilder<HistoryEcardActivationCubit, HistoryEcardActivationState>(
                            builder: (context, state) {
                              if (state is LoadedHistoryActivationState) {
                                final groupedTransactions = _groupTransactionsByDate(state.listHistoryActivarion);
                                _loadLocations();
                                return Column(
                                  children: groupedTransactions.entries.map((entry) {
                                    final date = entry.key;
                                    final transactions = groupedTransactions[date]!;
                                    return TransactionHistory(transactions: transactions);
                                  }).toList(),
                                );
                              } else if (state is LoadingHistoryActivationState) {
                                return Center(
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                      bottom: 24,
                                      top: MediaQuery.of(context).size.height * 0.2,
                                    ),
                                    child: const Column(
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        CircularProgressIndicator(),
                                        SizedBox(height: 16),
                                      ],
                                    ),
                                  ),
                                );
                              } else if (state is ErrorHistoryActivationState) {
                                return EmptyList(
                                  pathIcon: 'assets/svg/icon_file.svg',
                                  message: state.message,
                                );
                              } else if (state is NoDataHistoryActivationState) {
                                return const EmptyList(
                                  pathIcon: 'assets/svg/icon_file.svg',
                                  message: "Nenhum registro encontrado.",
                                );
                              } else {
                                return Container();
                              }
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void showFilterModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(25.0)),
      ),
      isScrollControlled: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.8,
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Padding(
                            padding: EdgeInsets.symmetric(vertical: 16.0),
                            child: Text(
                              'Filtros',
                              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                            ),
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'Locais',
                            style: TextStyle(fontSize: 18),
                          ),
                          const SizedBox(height: 16),
                          MultiSelecionButton(
                            defaultValues: _historyLocations,
                            selectedValues: _selectedLocations,
                            onSelectionChanged: (local, selected) {
                              setState(() {
                                if (selected) {
                                  _selectedLocations.add(local);
                                } else {
                                  _selectedLocations.remove(local);
                                }
                              });
                            },
                          ),
                          const SizedBox(height: 35),
                          DateRangePicker(
                            startDate: _startDate,
                            endDate: _endDate,
                            onStartDateChanged: (date) {
                              setState(() {
                                _startDate = date;
                                  _selectedPeriods.clear();
                              });
                            },
                            onEndDateChanged: (date) {
                              setState(() {
                                _endDate = date;
                                 _selectedPeriods.clear();
                              });
                            },
                          ),
                          MultiSelecionButton(
                            isMultiSelection: false,
                            isPeriodInDays: true,
                            defaultValues: _periodos,
                            selectedValues: _selectedPeriods,
                            onSelectionChanged: (date, selected) {
                               setState(() {
                                if (selected) {
                                  _selectedPeriods = [date];
                                  
                                  
                                  _endDate = DateTime.now();
                                  
                                  
                                  int days = int.tryParse(date) ?? 30;
                                  _startDate = _endDate!.subtract(Duration(days: days));
                                } else {
                                  _selectedPeriods.clear();
                                 
                                }
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        _fetchHistory(
                            locations: _selectedLocations,
                            periods: _selectedPeriods.isNotEmpty ? _selectedPeriods.first : null);

                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 16.0),
                      ),
                      child: const Text('Confirmar'),
                    ),
                  ),
                  const SizedBox(height: 20),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        /*  _selectedLocations.clear();
                        _selectedPeriods.clear();
                        _startDate = null;
                        _endDate = null; */

                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.0),
                          side: const BorderSide(color: Colors.grey),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 16.0),
                      ),
                      child: const Text(
                        'Cancelar',
                        style: TextStyle(color: Colors.black),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}

// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:camera/camera.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class CameraScreen extends StatefulWidget {
  final Function? onTakePhoto;
  const CameraScreen({super.key, this.onTakePhoto});

  @override
  CameraScreenState createState() => CameraScreenState();
}

class CameraScreenState extends State<CameraScreen> {
  final logger = UnimedLogger(className: "CameraScreen");
  CameraController? _cameraController;
  CameraDescription? _cameraFront;
  CameraDescription? _cameraBack;
  List<CameraDescription>? cameras;

  late bool isCameraFront;
  bool imagePickerIsOpen = false;
  String? photoPath;
  bool canPressButton = true;
  bool showAlertCadastrar = false;
  bool _initingCamera = false;
  bool inited = false;
  bool _closing = false;

  double cameraButtonSize = 75;
  Color cameraButtonColor = Colors.black;

  void _initCamera(BuildContext context, {bool front = false}) async {
    try {
      if (_initingCamera) return;
      setState(() => _initingCamera = true);

      cameras ??= await availableCameras();

      if (cameras!.isEmpty) {
        throw UnimedException('Dispositivo não possui câmera.');
      }

      for (CameraDescription camera in cameras!) {
        _getCameraBackFront(camera);
      }

      isCameraFront = front;
      CameraDescription camera =
          isCameraFront ? _cameraFront ?? cameras!.first : _cameraBack!;

      _cameraController = CameraController(
        camera,
        ResolutionPreset.medium,
        imageFormatGroup: ImageFormatGroup.jpeg,
        enableAudio: false,
      );

      await _cameraController!.initialize();

      //in current version disable flash breaks in some devices
      // _disableFlash();

      if (!mounted) {
        return;
      }
      await _cameraController!
          .lockCaptureOrientation(DeviceOrientation.portraitUp);
      SchedulerBinding.instance.addPostFrameCallback((_) {
        setState(() {
          debugPrint('Update state');
        });
      });
    } on UnimedException catch (e) {
      logger.e("Error on _initCamera UnimedException $e");

      Alert.open(context,
          title: "Alerta",
          text: '${e.message}',
          callbackClose: () => _close(context));
    } catch (e) {
      logger.e("Error on _initCamera Exception $e");

      Alert.open(context,
          title: "Alerta",
          text: 'Não foi possível acessar a câmera.',
          callbackClose: () => _close(context));
    } finally {
      Future.delayed(const Duration(milliseconds: 250), () {
        setState(() => _initingCamera = false);
      });
    }
  }

  void _getCameraBackFront(CameraDescription camera) {
    if (camera.lensDirection == CameraLensDirection.back) {
      _cameraBack ??= camera;
    } else if (camera.lensDirection == CameraLensDirection.front) {
      _cameraFront ??= camera;
    }
  }

  @override
  void dispose() {
    debugPrint('Disposing Authorization CameraScreen');
    _cameraController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!inited) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _initCamera(context);
        inited = true;
      });
    }
    final waitOpenCamera =
        (_cameraController == null || !_cameraController!.value.isInitialized);

    if (waitOpenCamera) {
      return const Scaffold(
        body: Center(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            //Text('Abrindo câmera, aguarde'),
            SpinKitThreeBounce(
              color: CooperadoColors.tealGreen,
              size: 20,
            )
          ],
        )),
      );
    } else {
      return Scaffold(
          backgroundColor: Colors.black,
          body: photoPath == null
              ? _cameraPreview(context)
              : _photoPreview(context));
    }
  }

  Widget _cameraPreview(BuildContext context) {
    return Stack(
      children: [
        //   Column(
        // children: [
        CameraPreview(_cameraController!),
        // Expanded(
        //     child:
        SafeArea(
          child: Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              margin: const EdgeInsets.only(bottom: 12.0),
              height: 100,
              color: Colors.black,
              padding: const EdgeInsets.all(24.0),
              child: _cameraOptions(context),
            ),
          ),
        )
        // )
      ],
      //   ),
      // ],
    );
  }

  Widget _photoPreview(BuildContext context) {
    return Stack(
      children: [
        Column(
          children: [
            Container(color: Colors.black, child: Image.file(File(photoPath!))),
            Expanded(
                child: Container(
              color: Colors.black,
              padding: const EdgeInsets.all(24.0),
              child: _photoOptions(context),
            ))
          ],
        ),
      ],
    );
  }

  Widget _cameraOptions(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          flex: 1,
          child: _buttonClose(context),
        ),
        Expanded(
          flex: 1,
          child: _buttonTakePicture(context),
        ),
        Expanded(flex: 1, child: _buttonChangeCameraOrientation(context)),
      ],
    );
  }

  Widget _photoOptions(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        TextButton(
          onPressed: () {
            Future.delayed(const Duration(milliseconds: 250), () {
              setState(() {
                photoPath = null;
              });
            });
          },
          child: const Icon(
            Icons.cached,
            color: Colors.white,
            size: 32,
          ),
        ),
        const SizedBox(
          width: 20,
        ),
        FloatingActionButton(
          key: const Key('checkFoto'),
          onPressed: _closing
              ? null
              : () {
                  widget.onTakePhoto!(File(photoPath!), context);
                  _close(context);
                },
          child: const Icon(
            Icons.check,
            size: 24,
          ),
        ),
      ],
    );
  }

  // Widget _photoCard() {
  //   return Center(
  //       child: Card(
  //           child: Padding(
  //               padding: EdgeInsets.all(8.0),
  //               child: Image.file(
  //                 File(photoPath),
  //                 filterQuality: FilterQuality.low,
  //               ))));
  // }

  Widget _buttonTakePicture(BuildContext context) {
    return InkWell(
      key: const Key('btnTakePhoto'),
      onTap: !canPressButton
          ? null
          : () async {
              debugPrint('take photo');
              setState(() {
                cameraButtonSize = 85;
                cameraButtonColor = Colors.red;
              });

              await _takePhoto(context);

              setState(() {
                cameraButtonSize = 75;
                cameraButtonColor = Colors.black;
              });
            },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 100),
        width: cameraButtonSize,
        height: cameraButtonSize,
        padding: const EdgeInsets.all(3),
        decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 3)),
        child: Container(
          decoration: BoxDecoration(
            color: cameraButtonColor,
            shape: BoxShape.circle,
          ),
        ),
      ),
    );
  }

  Future<void> _takePhoto(BuildContext context) async {
    try {
      if (canPressButton) {
        setState(() {
          canPressButton = false;
        });

        if (!_cameraController!.value.isInitialized) {
          await _cameraController!.initialize();
        }
        final file = await _cameraController!.takePicture();
        //wait to save file
        await Future.delayed(const Duration(milliseconds: 250));
        setState(() {
          photoPath = file.path;
          canPressButton = true;
        });
      }
    } catch (e) {
      _initCamera(context);
      logger.e("Error on _takePhoto $e");
      Alert.open(context,
          title: 'Anexar Foto',
          text: 'Erro ao anexar foto. Tente novamente', callbackClose: () {
        setState(() {
          canPressButton = true;
        });
      });
    }
  }

  Widget _buttonChangeCameraOrientation(BuildContext context) {
    return TextButton(
      onPressed: _initingCamera || !canPressButton
          ? null
          : () {
              _initCamera(context, front: !isCameraFront);
            },
      child: const Icon(
        Icons.flip_camera_ios_outlined,
        color: Colors.white,
        size: 32,
      ),
    );
  }

  Widget _buttonClose(BuildContext context) {
    return Platform.isAndroid
        ? Container()
        : TextButton(
            child: const FittedBox(
              child: Text(
                "Cancelar",
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
            onPressed: () => _close(context));
  }

  void _close(BuildContext context) {
    if (!_closing && Navigator.of(context).canPop()) {
      _closing = true;
      Navigator.of(context).pop();
    }
  }
}

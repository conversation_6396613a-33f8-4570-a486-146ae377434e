import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class ImageMember extends StatelessWidget {
  final String imageUrl;
  final String title;
  final String subtitle;

  const ImageMember(
      {super.key, this.imageUrl = '', this.title = '', this.subtitle = ''});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Stack(
            children: [
              Align(
                alignment: Alignment.center,
                child: CachedNetworkImage(
                  imageUrl: imageUrl,
                  placeholder: (context, url) => const SpinKitThreeBounce(
                      color: CooperadoColors.tealGreen),
                  errorWidget: (context, url, error) => const Icon(
                    Icons.account_circle,
                    size: 150,
                    color: CooperadoColors.grayLight3,
                  ),
                ),
              ),
              Align(
                alignment: Alignment.topRight,
                child: InkWell(
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    margin: const EdgeInsets.symmetric(
                        horizontal: 25, vertical: 10),
                    decoration: BoxDecoration(
                      color: Colors.grey[700]!.withAlpha(150),
                      borderRadius: const BorderRadius.all(Radius.circular(5)),
                    ),
                    child: const Padding(
                      padding: EdgeInsets.all(3),
                      child: Icon(Icons.close, color: Colors.white),
                    ),
                  ),
                ),
              )
            ],
          ),
          const SizedBox(height: 15),
          Column(
            children: [
              AutoSizeText(
                title,
                textAlign: TextAlign.center,
                style:
                    const TextStyle(color: CooperadoColors.tealGreenSecondary),
                minFontSize: 8,
                maxFontSize: 14,
              ),
              AutoSizeText(
                subtitle,
                textAlign: TextAlign.center,
                minFontSize: 6,
                maxFontSize: 12,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          )
        ],
      ),
    );
  }
}

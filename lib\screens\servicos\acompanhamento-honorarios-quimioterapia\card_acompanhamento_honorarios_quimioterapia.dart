import 'package:cooperado_minha_unimed/bloc/honorarios-quimioterapia/honorarios_quimioterapia_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:cooperado_minha_unimed/shared/widgets/graphics/choose_date.dart';
import 'package:cooperado_minha_unimed/shared/widgets/pdf_view/pdf_view_platform.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:intl/intl.dart';

class CardHonorariosQuimioterapia extends StatefulWidget {
  const CardHonorariosQuimioterapia({super.key});

  @override
  State<CardHonorariosQuimioterapia> createState() =>
      _CardHonorariosQuimioterapiaState();
}

class _CardHonorariosQuimioterapiaState
    extends State<CardHonorariosQuimioterapia> {
  DateTime? selectedDateTime;
  String? crmId;
  String? lastProduction;
  String? specialities;
    
  @override
  void initState() {
    context.read<HonorarioCubit>().resetState();
    selectedDateTime = initDate();
    super.initState();  
  }

  DateTime initDate(){
    return selectedDateTime == null ? DateTime.now() : selectedDateTime!;
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Container(
        padding: const EdgeInsets.all(10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const Text(
              "Acompanhamento Honorários Quimioterapia",
              style: TextStyle(
                  color: CooperadoColors.blackText,
                  fontSize: 16,
                  fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            indicatorErrorState(),
            const SizedBox(height: 5),
            Row(
              children: <Widget>[
                Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: ChooseDateWidget(
                      firstDate:
                          DateTime.now().subtract(const Duration(days: 30 * 3)),
                      textBox:
                          Text(DateFormat("MM/yyyy").format(selectedDateTime!)),
                      date: selectedDateTime,
                      lastDate: DateTime.now(),
                      onPressed: (date) {
                        if (date != null) {
                          setState(() {
                            selectedDateTime = date;
                          });
                        }
                        context.read<HonorarioCubit>().resetState();
                        context.read<HonorarioCubit>();
                      },
                    )),
                _buttonGeneratePDF(),
              ],
            ),
            const SizedBox(height: 5)
          ],
        ),
      ),
    );
  }

  Widget indicatorErrorState() {
    return BlocBuilder<HonorarioCubit, ReportHonorarioState>(
      builder: (context, state) {
        if (state is ErrorGetReportHonorarioState) {
          return Center(child: ErrorBanner(message: state.message));
        } else {
          return Container();
        }
      },
    );
  }

  Widget _buttonGeneratePDF() {
    bool activate = false;
    return BlocConsumer<HonorarioCubit, ReportHonorarioState>(
      listener: (context, state) {
        if (state is DoneGetReportHonorarioState && activate) {
          activate = false;

          _viewPDF(url: state.url, honorarioCode: state.honorarioCode);
        }
      },
      builder: (context, state) {
        if (state is LoadingGetReportHonorarioState) {
          return Expanded(
              child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: CooperadoColors.tealGreen,
            ),
            onPressed: () {},
            child: const SpinKitThreeBounce(
              color: Colors.white,
              size: 20,
            ),
          ));
        } else {
          return Expanded(
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                  backgroundColor: CooperadoColors.tealGreen),
              onPressed: () {
                activate = true;
                context.read<HonorarioCubit>().getReportHonorarioCodeEvent(
                    selectedDateTime: selectedDateTime, crmId: context.read<HonorarioCubit>().crmId ?? '');
              },
              child: const Text("GERAR PDF"),
            ),
          );
        }
      },
    );
  }

  void _viewPDF({required String url, required String? honorarioCode}) {
    Navigator.push(
      context,
      FadeRoute(
        page: PDFViewPlatform(
          url,
          isPath: true,
          share: true,
          filename: 'AcompanhamentoHonorarios${honorarioCode}pdf',
          title: 'Acompanhamento Honorarios Quimioterapia',
        ),
      ),
    );
  }
}

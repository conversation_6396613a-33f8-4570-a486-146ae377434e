import 'dart:convert';
import 'dart:io';

import 'package:cooperado_minha_unimed/models/agree_terms.model.dart';
import 'package:cooperado_minha_unimed/shared/api/pdf.api.dart';
import 'package:cooperado_minha_unimed/shared/api/terms.api.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';

part 'agree_terms_state.dart';

class AgreeTermsCubit extends Cubit<AgreeTermsState> {
  AgreeTermsCubit() : super(const InitialAgreeTerms(false));
  bool agreeTerm = false;
  Future<void> verifyAgree() async {
    try {
      if (FlavorConfig.instance?.values.codUnimed == codUnimedFortaleza) {
        final response = await Locator.instance!<TermsApi>().verifyAgreeTerms();
        final message = jsonDecode(response);

        final List<AgreeTermsModel> agreeTermsModel = [];
        if (message["ok"] != "0") {
          for (var element in message["retorno"]) {
            agreeTermsModel.add(AgreeTermsModel.fromJson(element));
          }

          emit(AlertState(agreeTermsModel, message["ok"], agreeTerm));
        }
      }
    } catch (e) {
      emit(ErrorState(e.toString(), agreeTerm));
    }
  }

  popTerms() {
    emit(PopAgreeTermsState(agreeTerm));
  }

  Future<bool> acceptTerms(index) async {
    try {
      await Locator.instance!<TermsApi>().acceptTerms(index);
      emit(LoadingState(agreeTerm));

      emit(PopAgreeTermsState(agreeTerm));
      return true;
    } catch (e) {
      emit(ErrorState(e.toString(), agreeTerm));
    }
    return false;
  }

  downloadPdf(pdf, title) async {
    try {
      emit(LoadingDownloadState(agreeTerm));
      final infoJson =
          await Locator.instance!<RemoteLog>().deviceInfo?.toJson();
      final androidVersion = int.parse(infoJson?['version.release'] ?? '0');
      final status = Platform.isAndroid
          ? androidVersion < 13
              ? await Permission.storage.status
              : null
          : await Permission.storage.status;

      if (status == PermissionStatus.granted ||
          status == PermissionStatus.limited ||
          androidVersion >= 13) {
        final File file =
            await Locator.instance!<PdfApi>().createPDFFileFromUrl(pdf, title);

        emit(SuccessDownloadState(file, agreeTerm));
      } else {
        emit(PopAgreeStoragePermissionState(pdf, title, status, agreeTerm));
      }
    } catch (e) {
      // throw e;
    }
  }

  setBool() {
    agreeTerm = !agreeTerm;

    emit(LoadingDownloadState(agreeTerm));
    emit(LoadedAgreeTermsState(agreeTerm));
  }
}

import 'package:cooperado_minha_unimed/models/e-card/ecard.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ECardModel', () {
    final viewAddress = ViewAddress(
      streetTypeCode: 'ST',
      streetName: 'Main Street',
      addressNumber: 123,
    );

    final eCardModel = ECardModel(
      providerCard: 'Provider123',
      activationStartDate: DateTime.parse('2023-01-01'),
      validityDate: DateTime.parse('2023-12-31'),
      status: 'Ativo',
      viewAddress: viewAddress,
    );

    test('fromJson should return a valid model', () {
      final json = {
        'providerCard': 'Provider123',
        'activationStartDate': '2023-01-01T00:00:00.000',
        'validityDate': '2023-12-31T00:00:00.000',
        'status': 'Ativo',
        'viewAddress': {
          'streetTypeCode': 'ST',
          'streetName': 'Main Street',
          'addressNumber': 123,
        },
      };

      final model = ECardModel.fromJson(json);

      expect(model.providerCard, 'Provider123');
      expect(model.activationStartDate, DateTime.parse('2023-01-01'));
      expect(model.validityDate, DateTime.parse('2023-12-31'));
      expect(model.status, 'Ativo');
      expect(model.viewAddress.streetTypeCode, 'ST');
      expect(model.viewAddress.streetName, 'Main Street');
      expect(model.viewAddress.addressNumber, 123);
    });

    test('toJson should return a valid json', () {
      final json = eCardModel.toJson();

      expect(json['providerCard'], 'Provider123');
      expect(json['activationStartDate'], '2023-01-01T00:00:00.000');
      expect(json['validityDate'], '2023-12-31T00:00:00.000');
      expect(json['status'], 'Ativo');
      expect(json['viewAddress']['streetTypeCode'], 'ST');
      expect(json['viewAddress']['streetName'], 'Main Street');
      expect(json['viewAddress']['addressNumber'], 123);
    });

    test('isStatusPending should return true for status "Pendente"', () {
      final model = eCardModel.copyWith(status: 'Pendente');
      expect(model.isStatusPending(), true);
    });

    test('isStatusActive should return true for status "Ativo"', () {
      expect(eCardModel.isStatusActive(), true);
    });

    test('getRemainingTime should return true if validityDate is in the future',
        () {
      expect(eCardModel.getRemainingTime(), false);
    });

    test('getRemainingTime should return false if validityDate is null', () {
      final model = eCardModel.copyWith(validityDate: null);
      expect(model.getRemainingTime(), false);
    });

    test('getRemainingTime should return false if validityDate is in the past',
        () {
      final model =
          eCardModel.copyWith(validityDate: DateTime.parse('2020-01-01'));
      expect(model.getRemainingTime(), false);
    });
  });

  group('ViewAddress', () {
    final viewAddress = ViewAddress(
      streetTypeCode: 'ST',
      streetName: 'Main Street',
      addressNumber: 123,
    );

    test('fromJson should return a valid model', () {
      final json = {
        'streetTypeCode': 'ST',
        'streetName': 'Main Street',
        'addressNumber': 123,
      };

      final model = ViewAddress.fromJson(json);

      expect(model.streetTypeCode, 'ST');
      expect(model.streetName, 'Main Street');
      expect(model.addressNumber, 123);
    });

    test('toJson should return a valid json', () {
      final json = viewAddress.toJson();

      expect(json['streetTypeCode'], 'ST');
      expect(json['streetName'], 'Main Street');
      expect(json['addressNumber'], 123);
    });
  });
}

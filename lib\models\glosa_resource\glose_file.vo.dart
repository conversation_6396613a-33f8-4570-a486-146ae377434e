class GlosaFileModel {
  late Anexo anexo;
  late String base64;

  GlosaFileModel({required this.anexo, required this.base64});

  GlosaFileModel.fromJson(Map<String, dynamic> json) {
    anexo = Anexo.fromJson(json['anexo']);
    base64 = json['base64'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    data['anexo'] = anexo.toJson();
    data['base64'] = base64;
    return data;
  }
}

class Anexo {
  int? sequencial;
  SpgGuiasSolicPagamento? spgGuiasSolicPagamento;
  String? dataInclusao;
  Usuario? usuario;
  late String nomeArquivo;
  String? caminhoArquivo;

  Anexo(
      {this.sequencial,
      this.spgGuiasSolicPagamento,
      this.dataInclusao,
      this.usuario,
      required this.nomeArquivo,
      this.caminhoArquivo});

  Anexo.fromJson(Map<String, dynamic> json) {
    sequencial = json['sequencial'];
    spgGuiasSolicPagamento = json['spgGuiasSolicPagamento'] != null
        ? SpgGuiasSolicPagamento.fromJson(json['spgGuiasSolicPagamento'])
        : null;
    dataInclusao = json['dataInclusao'];
    usuario =
        json['usuario'] != null ? Usuario.fromJson(json['usuario']) : null;
    nomeArquivo = json['nomeArquivo'];
    caminhoArquivo = json['caminhoArquivo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['sequencial'] = sequencial;
    if (spgGuiasSolicPagamento != null) {
      data['spgGuiasSolicPagamento'] = spgGuiasSolicPagamento!.toJson();
    }
    data['dataInclusao'] = dataInclusao;
    if (usuario != null) {
      data['usuario'] = usuario!.toJson();
    }
    data['nomeArquivo'] = nomeArquivo;
    data['caminhoArquivo'] = caminhoArquivo;
    return data;
  }
}

class SpgGuiasSolicPagamento {
  int? sequencial;

  SpgGuiasSolicPagamento({this.sequencial});

  SpgGuiasSolicPagamento.fromJson(Map<String, dynamic> json) {
    sequencial = json['sequencial'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['sequencial'] = sequencial;
    return data;
  }
}

class Usuario {
  String? codigoUsuario;

  Usuario({this.codigoUsuario});

  Usuario.fromJson(Map<String, dynamic> json) {
    codigoUsuario = json['codigoUsuario'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codigoUsuario'] = codigoUsuario;
    return data;
  }
}

import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/utils/url_launcher.utils.dart';
import 'package:cooperado_minha_unimed/shared/widgets/consultorio_online_screen.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http_client/http_client.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/widgets/webview_screen.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';

class RedirectButtonsApi {
  final UnimedHttpClient httpClient;
  RedirectButtonsApi(this.httpClient);
  final logger = UnimedLogger(className: 'RedirectButtonsApi');

  void goToGuia(
    context, {
    required FirebaseAnalytics analytics,
    required FirebaseAnalyticsObserver observer,
  }) async {
    try {
      UserCredentials? credentials =
          await Locator.instance!.get<AuthApi>().getCredentials();
      final token = credentials != null
          ? await User.createToken(credentials: credentials)
          : "";
      final url = '${FlavorConfig.instance!.values.guia.url}?token=$token';

      bool useUrlLauncher = BlocProvider.of<AuthCubit>(context)
              .modelGeneralConfigModel
              .permissions
              ?.useUrlLauncher ??
          false;

      logger.i('goToGuia - $url - useUrlLauncher: $useUrlLauncher');

      if (useUrlLauncher) {
        UrlLaucherUtils.launchURL(url);
      } else {
        Navigator.push(
          context,
          FadeRoute(
            page: WebViewScreen(
              url: url,
              title: 'Guia',
              analytics: analytics,
              observer: observer,
            ),
          ),
        );
      }
    } catch (ex) {
      logger.e('catch erro: goToGuia: $ex');
    }
  }

  void goToConsultometro(
    context, {
    required FirebaseAnalytics analytics,
    required FirebaseAnalyticsObserver observer,
  }) async {
    try {
      UserCredentials? credentials =
          await Locator.instance!.get<AuthApi>().getCredentials();
      final token = credentials != null
          ? await User.createToken(credentials: credentials)
          : "";
      final url =
          '${FlavorConfig.instance!.values.consultometro.url}?token=$token';

      bool useUrlLauncher = BlocProvider.of<AuthCubit>(context)
              .modelGeneralConfigModel
              .permissions
              ?.useUrlLauncher ??
          false;
      logger.i('goToConsultometro $url - useUrlLauncher: $useUrlLauncher');
      if (useUrlLauncher) {
        UrlLaucherUtils.launchURL(url);
      } else {
        Navigator.push(
          context,
          FadeRoute(
            page: WebViewScreen(
              url: url,
              title: 'Consultômetro',
              analytics: analytics,
              observer: observer,
            ),
          ),
        );
      }
    } catch (ex) {
      logger.e('catch erro: goToConsultometro: $ex');
    }
  }

  void goToConsultorioOnline(
    context, {
    required FirebaseAnalytics analytics,
    required FirebaseAnalyticsObserver observer,
  }) async {
    try {
      UserCredentials? credentials =
          await Locator.instance!.get<AuthApi>().getCredentials();
      final token = credentials != null
          ? await User.createToken(credentials: credentials)
          : "";
      final url =
          '${FlavorConfig.instance!.values.consultorioOnline.url}?token=${token.toUpperCase()}';

      bool useUrlLauncher = BlocProvider.of<AuthCubit>(context)
              .modelGeneralConfigModel
              .permissions
              ?.useUrlLauncher ??
          false;

      logger.i('goToConsultorioOnline $url - useUrlLauncher: $useUrlLauncher');

      if (useUrlLauncher) {
        UrlLaucherUtils.launchURL(url);
      } else {
        Navigator.push(
          context,
          FadeRoute(
            page: ConsultorioWebViewScreen(
              url: url,
              title: 'Consultório Online',
              enableOrientation: true,
              analytics: analytics,
              observer: observer,
            ),
          ),
        );
      }
    } catch (ex) {
      logger.e('catch erro: goToConsultorioOnline: $ex');
    }
  }
}

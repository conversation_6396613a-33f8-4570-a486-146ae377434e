<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Minha Unimed Sobral</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>Permissão da camera para Biometria Facial.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Permissão para entrar no sistema por biometria</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Precisamos da sua localizaçao para poder ativar o E-CARD</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Precisamos da sua localizaçao para poder ativar o E-CARD</string>
	<key>NSLocationUsageDescription</key>
	<string>Precisamos da sua localizaçao para poder ativar o E-CARD</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Precisamos da sua localizaçao para poder ativar o E-CARD</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Abrir sua galeria para anexar fotos a sua solicitação de autorização e teleconsulta.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Abrir sua galeria para anexar fotos a sua solicitação de autorização e teleconsulta.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>

part of 'profile_cubit.dart';

abstract class ProfileState extends Equatable {
  const ProfileState();

  @override
  List<Object> get props => [];
}

class InitialProfileState extends ProfileState {}

class LoadingProfileState extends ProfileState {
  @override
  List<Object> get props => [];
}

class ErrorProfileState extends ProfileState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorProfileState(this.message);
}

class LoadedProfileState extends ProfileState {
  final User user;

  @override
  List<Object> get props => [user];

  const LoadedProfileState(this.user);
}

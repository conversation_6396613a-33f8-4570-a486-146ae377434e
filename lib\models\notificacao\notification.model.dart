import 'package:intl/intl.dart';

class NotificationResponse {
  final int? totalPages;
  final List<NotificationModel>? notifications;

  NotificationResponse({
    this.totalPages,
    this.notifications,
  });

  NotificationResponse.fromJson(Map<String, dynamic> json)
      : totalPages = json['totalPages'],
        notifications = (json['notifications'] as List?)
            ?.map(
              (dynamic e) => NotificationModel.fromJson(e as Map<String, dynamic>),
            )
            .toList();

  Map<String, dynamic> toJson() => {
        'totalPages': totalPages,
        'notifications': notifications?.map((e) => e.toJson()).toList(),
      };
}

class NotificationModel {
  final String? notificationId;
  final String? title;
  final String? description;
  String? readAt;
  bool? pinned;
  final String? createdAt;

  NotificationModel({
    this.notificationId,
    this.title,
    this.description,
    this.readAt,
    this.pinned,
    this.createdAt,
  });

  String get formattedCreatedAtDate {
    if (createdAt == null) return 'Não informado';
    DateTime dateTime = DateTime.parse(createdAt!).toLocal();
    return DateFormat("dd/MM/yyyy HH:mm").format(dateTime);
  }

  NotificationModel.fromJson(Map<String, dynamic> json)
      : notificationId = json['notificationId'] as String?,
        title = json['title'] as String?,
        description = json['description'] as String?,
        readAt = json['readAt'] as String?,
        pinned = json['pinned'] as bool?,
        createdAt = json['createdAt'] as String?;

  Map<String, dynamic> toJson() => {
        'notificationId': notificationId,
        'title': title,
        'description': description,
        'readAt': readAt,
        'pinned': pinned,
        'createdAt': createdAt,
      };
}

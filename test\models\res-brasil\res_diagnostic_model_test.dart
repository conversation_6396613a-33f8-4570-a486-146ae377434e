import 'package:flutter_test/flutter_test.dart';
import 'package:cooperado_minha_unimed/models/res/res_brasil_diagnostico_model.dart';
import 'package:intl/intl.dart';

void main() {
  group('ResBrazilDiagnosticoModel', () {
    test('fromJson should correctly parse JSON data', () {
      final jsonData = {
        'descricao': 'Diagnóstico: Gripe',
        'nomeMedico': 'Dr. Fulano',
        'dataEntrada': '2024-07-26T10:00:00',
      };

      final model = ResBrazilDiagnosticoModel.fromJson(jsonData);

      expect(model.descricao, 'Diagnóstico: Gripe');
      expect(model.nomeMedico, 'Dr. Fulano');
      expect(model.dataEntrada, '2024-07-26T10:00:00');

      // Test formatted date
      final expectedFormattedDate = DateFormat('dd/MM/yyyy HH:mm')
          .format(DateTime.parse('2024-07-26T10:00:00'));
      expect(model.dateDiagnosticoFormatted, expectedFormattedDate);
    });

    test('from<PERSON><PERSON> should handle null values gracefully', () {
      final jsonData = {
        'descricao': null,
        'nomeMedico': null,
        'dataEntrada': null,
      };

      final model = ResBrazilDiagnosticoModel.fromJson(jsonData);

      expect(model.descricao, null);
      expect(model.nomeMedico, null);
      expect(model.dataEntrada, null);
    });

    test('dataEntradaFormatted getter should handle null dataEntrada', () {
      final jsonData = {
        'dataEntrada': null,
      };
      final model = ResBrazilDiagnosticoModel.fromJson(jsonData);

      expect(() => model.dateDiagnosticoFormatted, throwsA(isA<TypeError>()));
    });
  });
}

name: cooperado_minha_unimed
description: <PERSON><PERSON><PERSON> App.

publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 5.5.3+553001

environment:
  sdk: ">=2.17.0 <4.0.0"
  flutter: "3.24.3"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  collection: ^1.17.0
  cupertino_icons: ^1.0.5
  equatable: ^2.0.3
  get_it: ^7.2.0
  http: ^1.1.0
  intl:
  flutter_bloc: ^8.1.1
  logger: ^2.0.2+1
  money2: ^4.0.0
  crypto: ^3.0.1
  flutter_spinkit: ^5.0.0
  shared_preferences: ^2.3.3
  image_cropper: ^5.0.1
  file_picker: ^6.1.1
  image_picker: ^1.0.7
  camera: ^0.10.5+4


  encrypt: 5.0.1 #foi necessario fixar a versao pois as demais (^5.0.0, ^5.0.2 e ^5.0.3) nao estao conseguindo decodificar impedindo o login
  flutter_svg: ^2.0.17
  flutter_image_compress: ^2.3.0
  another_flushbar: ^1.12.30

  fl_chart: ^0.64.0
  local_auth: ^2.1.2
  path_provider: ^2.0.2
  mask_text_input_formatter: ^2.1.0

  flutter_masked_text2: ^0.9.1 #o mesmo pacote foi atualizado a referencia para nullsafety
  auto_size_text: ^3.0.0
  flutter_swiper_plus: ^2.0.4 #o mesmo pacote foi atualizado a referencia para nullsafety
  package_info: ^2.0.2
  flare_flutter: ^3.0.2
  share_plus: ^7.2.1
  bubble: ^1.1.9+1
  flutter_html: ^3.0.0
  flutter_html_iframe: ^3.0.0
  webview_flutter: ^4.10.0
  json_annotation: ^4.3.0
  cached_network_image: ^3.3.0
  permission_handler: ^11.0.1
  table_calendar: ^3.0.8
  two_dimensional_scrollables: ^0.0.4
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.10
  # firebase_crashlytics: ^3.4.8
  firebase_analytics: ^10.7.4
  bloc: ^8.1.4
  sensors_plus: ^3.1.0
  pdfx: ^2.0.1
  objectbox: ^4.0.2
  objectbox_flutter_libs: 4.0.2
  graphql: ^5.1.3
  path: ^1.9.0
  uuid: ^4.5.1
  geolocator: ^7.7.1

  remote_log_elastic:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: remote_log_elastic
      ref: remote_log_elastic-v4.0.0

  header_login:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: header_login
      ref: header_login-v2.0.2

  unimed_select:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: unimed_select
      ref: unimed_select-v1.1.1

  splash_unimed:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: splash_unimed
      ref: splash_unimed-v2.0.1

  personal_assistente_digital:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: personal_assistente_digital
      ref: personal_assistente_digital-v4.0.1

  evaluation:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: evaluation
      ref: evalution-v5.0.0

  http_client:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: http_client
      ref: http_client-v2.0.0

  password_validation:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: password_validation
      ref: password_validation-v1.0.0

  url_launcher:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: url_launcher
  rxdart: ^0.28.0

  biometria_perfilapps:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: biometria_perfilapps
      ref: biometria_perfilapps-v6.0.0
  animations: ^2.0.11

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  json_serializable: ^6.0.1
  build_runner: ^2.3.3
  flutter_launcher_icons: ^0.13.1
  flutter_lints: ^4.0.0
  objectbox_generator: ^4.0.2

dependency_overrides:
  collection: 1.18.0

flutter:
  uses-material-design: true

  assets:
    - assets/
    - assets/animations/
    - assets/icon/
    - assets/user-journey-icons/
    - assets/svg/

  fonts:
    - family: UnimedSans
      fonts:
        - asset: assets/fonts/UnimedSans-Regular.otf
        - asset: assets/fonts/UnimedSans-RegularItalic.otf
          style: italic
    - family: cooperado-unimed
      fonts:
        - asset: assets/icomoon/fonts/cooperado-unimed.ttf
    - family: personal-assistent
      fonts:
        - asset: assets/icomoon/fonts/icon-personal-assistent.ttf

import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/bloc/transparency/assistance_costs/assistance_costs_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/transparency/beneficiaries_quantitative/beneficiaries_quantitative_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/transparency/contingency_index/contingency_index_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/transparency/demonstatives_results/demonstatives_results_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/transparency/loss_ratio/loss_ratio.cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/beneficiary/card_beneficiary.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/card_custos_assistenciais.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/card_portal_transparencia.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/contingency-index/contingency_index_card.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/loss_ratio/loss_ratio_card.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/results-demonstrative/card_results_demonstrative.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TransparenciaScreen extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const TransparenciaScreen(
      {super.key, required this.analytics, required this.observer});

  @override
  TransparenciaScreenState createState() => TransparenciaScreenState();
}

class TransparenciaScreenState extends State<TransparenciaScreen> {
  @override
  void initState() {
    super.initState();
    widget.analytics.logScreenView(
      screenName: 'Transparência',
      screenClass: 'TransparenciaScreen',
    );
  }

  void _updateAllCards() {
    context.read<AssistanceCostsCubit>().getAssistanceCosts();
    context
        .read<DemonstrativesResultsCubit>()
        .getDemonstrativesResults(pagina: 1);
    context.read<LossRatioCubit>().getLossRatio();
    context.read<ContingencyIndexCubit>().getContingencyIndex();
    context
        .read<BeneficiariesQuantitativeCubit>()
        .getBeneficiariesQuantitative();
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var orientation = MediaQuery.of(context).orientation;
    if (kDebugMode) {
      print(orientation);
    }
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text("Transparência"),
        backgroundColor: CooperadoColors.tealGreenDark,
        actions: [
             Padding(
             padding: const EdgeInsets.only(right: 15.0),
             child: IconButton(
                       icon: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
              builder: (context, state) {
                return Icon(
                  state.isSensitiveDataVisible ? Icons.visibility : Icons.visibility_off,
                );
              },
                       ),
                       onPressed: () {
              context.read<SensitiveDataCubit>().toggleSensitiveDataVisibility();
                       },
                     ),
           ),
        ],
      ),
      backgroundColor: CooperadoColors.grayLight,
      body: RefreshIndicator(
        onRefresh: () async {
          _updateAllCards();
        },
        color: CooperadoColors.tealGreen,
        child: SafeArea(
          child: SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: Column(
              children: <Widget>[
                const CardPortalTransparencia(),
                const CardCustosAssistenciais(),
                LossRatioCardCard(orientation: orientation),
                 ContingencyIndexCard(orientation: orientation,),
                const CardQuantitativoBeneficiarios(),
                const CardDemonstrativoResultados(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

import 'package:cooperado_minha_unimed/bloc/res/atendimento/res_brasil_atendimento_result_cubit.state.dart';
import 'package:cooperado_minha_unimed/bloc/res/atendimento/res_brazil_atendimento_result_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/diagnostico/res_brasil_diagnostico_result_cubit.state.dart';
import 'package:cooperado_minha_unimed/bloc/res/diagnostico/res_brazil_diagnostico_result_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/documents/detail/res_documents_detail_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/exames-fisicos/detail/res_brazil_detail_examesfisicos_result_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/exames-fisicos/detail/res_brazil_detail_examesfisicos_result_cubit.state.dart';
import 'package:cooperado_minha_unimed/bloc/res/procedures/detail/res_procedures_detail_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/res/res_attendance_model.dart';
import 'package:cooperado_minha_unimed/screens/res/diagnostic-screen/res_diagnostic_detail.dart';
import 'package:cooperado_minha_unimed/screens/res/documents/res_document_detail.dart';
import 'package:cooperado_minha_unimed/screens/res/exams-phisical/res_exams_detail.dart';
import 'package:cooperado_minha_unimed/screens/res/procedures/res_procedure_detail.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/app_bar_res.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/custom_button_card.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/empty_list.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/erro_service.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/expandable_card.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/filters_widget.dart';
import 'package:cooperado_minha_unimed/shared/utils/router_observer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:intl/intl.dart';

class ResServiceScreen extends StatefulWidget {
  final String nameBeneficiary;
  final String crm;
  final String card;

  const ResServiceScreen({
    super.key,
    required this.nameBeneficiary,
    required this.crm,
    required this.card,
  });

  @override
  State<ResServiceScreen> createState() => _ResServiceScreenState();
}

class _ResServiceScreenState extends State<ResServiceScreen> with RouteAware {
  FocusNode? focusTextFieldSearch;
  final bool _isLoading = false;
  int? _filterMonthSelected;
  DateTimeRange? _dateRangeToFilter;
  final List<int> _lastMonthsToFilter = [3, 6];

  List<ExpansionTileController> _controllers = [];
  // int? _previusSelectedIndex;
  ResAttendanceModel? _attendanceModeExpanded;

  late ResAttendanceModel _selectedAttendance;

  void _carregarAtendimentos() {
    setState(() {
      _controllers = [];
      _attendanceModeExpanded = null;
    });
    context.read<ResBrasilAtendimentoResultCubit>().listResAtendimentoResult(
          crm: widget.crm,
          card: widget.card,
          startDateTime: _dateRangeToFilter?.start,
          endDateTime: _dateRangeToFilter?.end,
        );
  }

  @override
  void initState() {
    super.initState();
    _carregarAtendimentos();
    focusTextFieldSearch = FocusNode();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void dispose() {
    focusTextFieldSearch!.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarRes(
        title: 'Atendimentos',
        nameBeneficiary: widget.nameBeneficiary,
      ),
      backgroundColor: CooperadoColors.backgroundWhiteColor,
      body: Padding(
        padding: const EdgeInsets.only(left: 20, right: 20, top: 24),
        child: Column(
          children: [
            FiltersWidget(
              isLoading: _isLoading,
              lastMonthsToFilter: _lastMonthsToFilter,
              filterMonthSelected: _filterMonthSelected,
              dateRangeToFilter: _dateRangeToFilter,
              onMonthFilterChanged: (filterSelected) {
                setState(() {
                  _filterMonthSelected = filterSelected;
                  filterSelected == null
                      ? _dateRangeToFilter = null
                      : _dateRangeToFilter = DateTimeRange(
                          start: _selectedDataStart(filterSelected),
                          end: DateTime.now(),
                        );
                });
                _carregarAtendimentos();
              },
              onClearDateRange: () {
                setState(() {
                  _filterMonthSelected = null;
                  _dateRangeToFilter = null;
                  _carregarAtendimentos();
                });
              },
              selectDateToFilter: _selectDateToFilter,
              onDateRangeSelected: (dateRange) {
                setState(() {
                  _filterMonthSelected = null;
                  _dateRangeToFilter = dateRange;
                });
                _carregarAtendimentos();
              },
            ),
            BlocBuilder<ResBrasilAtendimentoResultCubit,
                ResBrasilAtendimentoResultState>(
              builder: (context, state) {
                if (state is LoadedResBrasilAtendimentoResultState) {
                  if (state.listAtendimentoResult.isEmpty &&
                      (_filterMonthSelected != null ||
                          _dateRangeToFilter != null)) {
                    return SizedBox(
                      height: MediaQuery.of(context).size.height / 2,
                      child: Column(
                        //mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Center(
                            child: EmptyList(
                              pathIcon: 'assets/svg/icon_file.svg',
                              message: _dateRangeToFilter != null
                                  ? 'Não foi encontrado nenhum diagnóstico para o período de ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.start)} até ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.end)}'
                                  : 'Não foi encontrado nenhum diagnóstico.',
                            ),
                          ),
                        ],
                      ),
                    );
                  }
                  return Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children:
                            state.listAtendimentoResult.map((atendimento) {
                          final controller = ExpansionTileController();

                          _controllers.add(controller);

                          // final atendimento = state.listAtendimentoResult[index];
                          return Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: ExpandableCard(
                              controller: controller,
                              pathIcon: 'assets/svg/icon_hospital.svg',
                              title: atendimento.tipo?.toUpperCase() ?? '',
                              subtitle: atendimento.dataEntrada != null
                                  ? DateFormat('dd/MM/yyyy - HH:mm').format(
                                      DateTime.parse(atendimento.dataEntrada!),
                                    )
                                  : '',
                              additionalInfo: [
                                {
                                  'title': 'Tipo',
                                  'description': atendimento.tipo ?? ''
                                },
                                {
                                  'title': 'Local',
                                  'description': atendimento.nomeLocal ?? ''
                                },
                              ],
                              onExpansionChanged: (value) {
                                setState(() {
                                  if (value &&
                                      _attendanceModeExpanded != atendimento) {
                                    if (_attendanceModeExpanded != null) {
                                      _controllers[state.listAtendimentoResult
                                              .indexWhere((element) =>
                                                  element ==
                                                  _attendanceModeExpanded)]
                                          .collapse();
                                    }
                                    _attendanceModeExpanded = atendimento;
                                  }
                                });
                              },
                              buttons: _buildButtons(atendimento),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  );
                } else if (state is ErrorResBrasilAtendimentoResultState) {
                  return Expanded(
                    child: Center(
                      child: ErroService(
                        message: state.message,
                        onPressed: () {
                          _carregarAtendimentos();
                        },
                      ),
                    ),
                  );
                } else if (state is LoadingResBrasilAtendimentoResultState) {
                  return const Expanded(
                      child: Center(
                    child: SpinKitCircle(color: CooperadoColors.tealGreen),
                  ));
                } else if (state is NoDataResBrasilAtendimentoResultState) {
                  return Expanded(
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.only(top: 32.0),
                        child: EmptyList(
                          pathIcon: 'assets/svg/icon_file.svg',
                          message: state.message,
                        ),
                      ),
                    ),
                  );
                }

                return Container();
              },
            ),
          ],
        ),
      ),
    );
  }

  List<CustomButtonCard> _buildButtons(ResAttendanceModel atendimento) {
    List<CustomButtonCard> buttons = [];

    if (atendimento.itensAtendimento != null) {
      for (ItensAtendimento itemAtendimento in atendimento.itensAtendimento!) {
        switch (itemAtendimento.descricao.toLowerCase()) {
          case 'documentos':
            buttons.add(
              CustomButtonCard(
                text: 'Documentos ',
                onPressed: () {
                  _selectedAttendance = atendimento;
                  context.read<ResDocumentDetailCubit>().getDocumentDetail(
                        crm: widget.crm, // Passa o CRM recebido via construtor
                        card: widget
                            .card, // Passa o cartão recebido via construtor
                        code: atendimento.codigo.toString(),
                      );
                  _showDetailsBottomSheet(context, 'Documentos');
                },
              ),
            );
            break;

          case 'diagnosticos':
            buttons.add(
              CustomButtonCard(
                text: 'Diagnóstico', // Ajusta plural e exibe quantidade
                onPressed: () {
                  _selectedAttendance = atendimento;
                  context
                      .read<ResBrazilDiagnosticoResultCubit>()
                      .listResBrazilDiagnosticoDetailResult(
                        crm: widget.crm, // Passa o CRM recebido via construtor
                        card: widget
                            .card, // Passa o cartão recebido via construtor
                        code: atendimento.codigo
                            .toString(), // Passa o código recebido via construtor
                      );
                  _showDetailsBottomSheet(context, 'Diagnóstico');
                },
              ),
            );

            break;

          case 'exame fisico':
            buttons.add(
              CustomButtonCard(
                text: 'Exames Físicos',
                onPressed: () {
                  _selectedAttendance = atendimento;
                  context
                      .read<ResBrazilDetailExamesfisicosResultCubit>()
                      .getExamesFisicosDetail(
                        crm: widget.crm,
                        card: widget.card,
                        code: atendimento.codigo.toString(),
                      );
                  _showDetailsBottomSheet(context, 'Exames Físicos');
                },
              ),
            );
            break;

          case 'exame':
            buttons.add(
              CustomButtonCard(
                text: 'Exame',
                onPressed: () {
                  _selectedAttendance = atendimento;
                  _showDetailsBottomSheet(context, 'Exame');
                },
              ),
            );
            break;

          case 'procedimento':
            buttons.add(
              CustomButtonCard(
                text: 'Procedimentos ',
                onPressed: () {
                  _selectedAttendance = atendimento;
                  context.read<ResProcedureDetailCubit>().getProcedureDetail(
                        crm: widget.crm, // Passa o CRM recebido via construtor
                        card: widget
                            .card, // Passa o cartão recebido via construtor
                        code: atendimento.codigo.toString(),
                      );
                  _showDetailsBottomSheet(context, 'Procedimentos');
                },
              ),
            );

            break;

          case 'imagem':
            buttons.add(
              CustomButtonCard(
                text: 'Imagem ',
                onPressed: () {
                  _showDetailsBottomSheet(context, 'Imagem');
                },
              ),
            );
            break;
        }
      }
    }

    return buttons;
  }

  DateTime _selectedDataStart(int meses) {
    DateTime dataAtual = DateTime.now();
    DateTime novaData = DateTime(
      dataAtual.year,
      dataAtual.month - meses,
      dataAtual.day,
      dataAtual.hour,
      dataAtual.minute,
      dataAtual.second,
      dataAtual.millisecond,
      dataAtual.microsecond,
    );

    return novaData;
  }

  void _showDetailsBottomSheet(BuildContext context, String title) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(15))),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [detail(title)],
          ),
        );
      },
    );
  }

  Widget detail(String title) {
    switch (title) {
      case 'Diagnóstico':
        return detailDiagnostico();

      case 'Documentos':
        return detailDocument();

      case 'Procedimentos':
        return detailProcedure();

      case 'Exames Físicos':
        return detailExamesFisicos();

      default:
        return const Padding(
          padding: EdgeInsets.all(12.0),
          child: SafeArea(
            child: Text(
              'Em implementação',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        );
    }
  }

  Widget detailDiagnostico() {
    return BlocBuilder<ResBrazilDiagnosticoResultCubit,
        ResBrazilDiagnosticoResultCubitState>(
      builder: (context, state) {
        // Verifica o estado do cubit e exibe a tela correspondente
        if (state is LoadingResBrazilDiagnosticoResultState) {
          return Column(
            children: [
              Center(
                child: Container(
                  height: 4,
                  width: 100,
                  margin: const EdgeInsets.symmetric(
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                    color: CooperadoColors.grayLight3,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const Center(
                child: Center(
                    child: SpinKitCircle(color: CooperadoColors.tealGreen)),
              ),
            ],
          );
        } else if (state is LoadedResBrazilDiagnosticoResultState) {
          // Carrega a lista de diagnósticos quando os dados estiverem disponíveis
          return Expanded(
            child: DiagnosticDetail(
              listDiagnostico: state.listDiagnostico,
            ),
          );
        } else if (state is ErroResBrazilDiagnosticoResultState) {
          // Exibe uma mensagem de erro caso ocorra algum problema
          return Padding(
            padding: const EdgeInsets.all(12.0),
            child: Center(
                child: ErroService(
              message: state.message,
              onPressed: () {
                context
                    .read<ResBrazilDiagnosticoResultCubit>()
                    .listResBrazilDiagnosticoDetailResult(
                      crm: widget.crm, // Passa o CRM recebido via construtor
                      card:
                          widget.card, // Passa o cartão recebido via construtor
                      code: _selectedAttendance.codigo
                          .toString(), // Passa o código recebido via construtor
                    );
              },
            )),
          );
        } else {
          return Container();
        }
      },
    );
  }

  Widget detailDocument() {
    return BlocBuilder<ResDocumentDetailCubit, ResDocumentDetailState>(
      builder: (context, state) {
        if (state is LoadingResDocumentDetailState) {
          return Column(
            children: [
              Center(
                child: Container(
                  height: 4,
                  width: 100,
                  margin: const EdgeInsets.symmetric(
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                    color: CooperadoColors.grayLight3,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const Center(
                  child: SpinKitCircle(color: CooperadoColors.tealGreen)),
            ],
          );
        } else if (state is LoadedResDocumentDetailState) {
          return Expanded(
            child: ResDocumentDetails(
              resDocumentsDetailModel: state.resDocumentDetailModel,
            ),
          );
        } else if (state is ErrorResDocumentDetailState) {
          return Padding(
            padding: const EdgeInsets.all(12.0),
            child: Center(
              child: ErroService(
                message: state.message,
                onPressed: () {
                  context.read<ResDocumentDetailCubit>().getDocumentDetail(
                        crm: widget.crm, // Passa o CRM recebido via construtor
                        card: widget
                            .card, // Passa o cartão recebido via construtor
                        code: _selectedAttendance.codigo.toString(),
                      );
                },
              ),
            ),
          );
        } else if (state is NoDataResDocumentDetailState) {
          return Padding(
            padding: const EdgeInsets.only(top: 32.0),
            child: Center(
                child: EmptyList(
              pathIcon: 'assets/svg/icon_file.svg',
              message: _dateRangeToFilter != null
                  ? 'Sem dados de documentos para o período de ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.start)} até  ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.end)}'
                  : 'Sem dados de documentos.',
            )),
          );
        } else {
          return Container();
        }
      },
    );
  }

  Widget detailExamesFisicos() {
    return BlocBuilder<ResBrazilDetailExamesfisicosResultCubit,
        ResBrazilDetailExamesFisicosResultState>(
      builder: (context, state) {
        if (state is LoadingResBrazilDetailExamesFisicosResultState) {
          return const Center(
              child: SpinKitCircle(color: CooperadoColors.tealGreen));
        } else if (state is LoadedResBrazilDetailExamesFisicosResultState) {
          return ConstrainedBox(
            // Use ConstrainedBox
            constraints:
                const BoxConstraints(maxHeight: 350), // Set maximum height
            child: ResExamsDetail(
              resExamesFisicosDetailModel: state.detailExamesFisicos,
            ),
          );
        } else if (state is ErrorResBrazilDetailExamesFisicosResultState) {
          return Padding(
            padding: const EdgeInsets.all(12.0),
            child: Center(
              child: ErroService(
                message: state.message,
                onPressed: () {
                  context
                      .read<ResBrazilDetailExamesfisicosResultCubit>()
                      .getExamesFisicosDetail(
                        crm: widget.crm,
                        card: widget.card,
                        code: _selectedAttendance.codigo.toString(),
                      );
                },
              ),
            ),
          );
        } else if (state is NoDataResBrazilDetailExamesFisicosResultState) {
          return Padding(
            padding: const EdgeInsets.only(top: 32.0),
            child: Center(
                child: EmptyList(
              pathIcon: 'assets/svg/icon_file.svg',
              message: _dateRangeToFilter != null
                  ? 'Sem dados de exames físicos para o período de ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.start)} até  ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.end)}'
                  : 'Sem dados de exames físicos.',
            )),
          );
        } else {
          return Container();
        }
      },
    );
  }

  Widget detailProcedure() {
    return BlocBuilder<ResProcedureDetailCubit, ResProcedureDetailState>(
      builder: (context, state) {
        if (state is LoadingResProcedureDetailState) {
          return Column(
            children: [
              Center(
                child: Container(
                  height: 4,
                  width: 100,
                  margin: const EdgeInsets.symmetric(
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                    color: CooperadoColors.grayLight3,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const Center(
                  child: SpinKitCircle(color: CooperadoColors.tealGreen)),
            ],
          );
        } else if (state is LoadedResProcedureDetailState) {
          return Expanded(
            child: ResProcedureDetail(
              resProcedureDetailModel: state.resProcedureDetailModel,
            ),
          );
        } else if (state is ErrorResProcedureDetailState) {
          return Padding(
            padding: const EdgeInsets.all(12.0),
            child: Center(
              child: ErroService(
                message: state.message,
                onPressed: () {
                  context.read<ResProcedureDetailCubit>().getProcedureDetail(
                        crm: widget.crm,
                        card: widget.card,
                        code: _selectedAttendance.codigo.toString(),
                      );
                },
              ),
            ),
          );
        } else if (state is NoDataResProcedureDetailState) {
          return Padding(
            padding: const EdgeInsets.only(top: 32.0),
            child: Center(
                child: EmptyList(
              pathIcon: 'assets/svg/icon_file.svg',
              message: _dateRangeToFilter != null
                  ? 'Sem dados de procedimentos para o período de ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.start)} até  ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.end)}'
                  : 'Sem dados de procedimentos.',
            )),
          );
        } else {
          return Container();
        }
      },
    );
  }

  DateTime _getFirstDate() {
    DateTime now = DateTime.now();
    int year = now.year;
    bool isLeapYear = (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    return now.subtract(Duration(days: isLeapYear ? 366 : 365));
  }

  Future<DateTime?> _selectDateToFilter(BuildContext context, String helpText,
      {DateTime? firstDate}) async {
    return await showDatePicker(
      context: context,
      helpText: helpText,
      initialDate: DateTime.now(),
      firstDate: firstDate ?? _getFirstDate(),
      lastDate: DateTime.now(),
      keyboardType: TextInputType.text,
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: const ColorScheme.light(
              primary: CooperadoColors.tealGreen,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );
  }
}

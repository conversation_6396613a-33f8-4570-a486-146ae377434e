part of 'beneficiaries_quantitative_cubit.dart';

abstract class BeneficiariesQuantitativeState extends Equatable {
  const BeneficiariesQuantitativeState();
}

class InititalBeneficiariesQuantitativeState
    extends BeneficiariesQuantitativeState {
  @override
  List<Object> get props => [];
}

class LoadingBeneficiariesQuantitativeState
    extends BeneficiariesQuantitativeState {
  @override
  List<Object> get props => [];
}

class ErrorBeneficiariesQuantitativeState
    extends BeneficiariesQuantitativeState {
  final String? message;
  @override
  List<Object?> get props => [message];
  const ErrorBeneficiariesQuantitativeState(this.message);
}

class LoadedBeneficiariesQuantitativeState
    extends BeneficiariesQuantitativeState {
  final QuantitativoBeneficiariosVO quantitativoBeneficiarios;
  @override
  List<Object> get props => [quantitativoBeneficiarios];

  const LoadedBeneficiariesQuantitativeState(this.quantitativoBeneficiarios);
}

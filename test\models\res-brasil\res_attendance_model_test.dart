import 'package:cooperado_minha_unimed/models/res/res_attendance_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ResAttendanceModel', () {
    test('fromJson should correctly parse JSON data', () {
      final jsonData = {
        'codigo': '12345',
        'tipo': 'Consulta',
        'nomeLocal': 'Hospital Unimed',
        'dataEntrada': '2024-07-26T10:00:00',
        'dataAlta': '2024-07-26T11:00:00',
        'codigoAtendimentoEncode': 'encoded_code',
        'itensAtendimento': [
          {'descricao': 'Diagnóstico: Gripe'},
          {'descricao': 'Procedimento: Medicação'},
        ],
      };

      final model = ResAttendanceModel.fromJson(jsonData);

      expect(model.codigo, '12345');
      expect(model.tipo, 'Consulta');
      expect(model.nomeLocal, 'Hospital Unimed');
      expect(model.dataEntrada, '2024-07-26T10:00:00');
      expect(model.dataAlta, '2024-07-26T11:00:00');
      expect(model.codigoAtendimentoEncode, 'encoded_code');
      expect(model.itensAtendimento!.length, 2);
      expect(model.itensAtendimento![0].descricao, 'Diagnóstico: Gripe');
      expect(model.itensAtendimento![1].descricao, 'Procedimento: Medicação');
    });

    test('fromJson should handle null values gracefully', () {
      final jsonData = {
        'codigo': null,
        'tipo': null,
        'nomeLocal': null,
        'dataEntrada': null,
        'dataAlta': null,
        'codigoAtendimentoEncode': null,
        'itensAtendimento': null,
      };

      final model = ResAttendanceModel.fromJson(jsonData);

      expect(model.codigo, null);
      expect(model.tipo, null);
      expect(model.nomeLocal, null);
      expect(model.dataEntrada, null);
      expect(model.dataAlta, null);
      expect(model.codigoAtendimentoEncode, null);
      expect(model.itensAtendimento, null);
    });
  });
}

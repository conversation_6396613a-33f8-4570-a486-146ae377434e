import 'dart:convert';
import 'dart:io';
import 'dart:ui' as ui;

// ignore: depend_on_referenced_packages
import 'package:archive/archive_io.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';

import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:uuid/uuid.dart';

class FileUtils {
  static final logger = UnimedLogger(className: 'FileUtils');

  /// Return path to generated zip file
  static Future<String> zip({required List<File> files}) async {
    final ZipFileEncoder encoder = ZipFileEncoder();
    final pathTemp = await getTemporaryDirectory();
    var uuid = const Uuid();

    final pathToSave = p.join(pathTemp.path, '${uuid.v4()}.zip');

    encoder.create(pathToSave);
    for (File f in files) {
      encoder.addFile(f);
    }
    encoder.close();

    return pathToSave;
  }

  static Future<String?> md5File({required String path}) async {
    var file = File(path);

    if (await file.exists()) {
      try {
        var hash = await md5.bind(file.openRead()).first;

        return hash.toString();
      } catch (exception) {
        logger.e('exception md5 : $exception');

        return null;
      }
    } else {
      logger.e('file in path $path do not exists');

      return null;
    }
  }

  static Future<Uint8List> getBytesFromAsset(String path, int width) async {
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: width);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  /// return path file created
  static Future<String> createFileFromString({
    String? base64String,
    FileExtension? extension,
  }) async {
    String extensionString;
    switch (extension) {
      case FileExtension.png:
        extensionString = '.png';
        break;
      default:
        extensionString = '.pdf';
    }
    Uint8List bytes = base64.decode(base64String!);
    String dir = (await getApplicationDocumentsDirectory()).path;
    File file =
        File("$dir/${DateTime.now().millisecondsSinceEpoch}$extensionString");
    await file.writeAsBytes(bytes);

    return file.path;
  }

  static Future<File?> getCompressedFile(File file) async {
    try {
      final filePath = file.path;
      final lastIndex = filePath.lastIndexOf('.');
      final splitted = filePath.substring(0, (lastIndex));
      final targetPath = "${splitted}_out${filePath.substring(lastIndex)}";
      final result = await FlutterImageCompress.compressAndGetFile(
        filePath,
        targetPath,
        quality: 10,
        minHeight: 75,
      );

      return File(result!.path);
    } catch (e) {
      return file;
    }
  }

  static Future<File> copyFileOnDocuments(File tempFile) async {
    try {
      Directory appDocDir = await getApplicationDocumentsDirectory();
      String appDocPath = appDocDir.path;
      String newPath = '$appDocPath/${basename(tempFile.path)}';

      return File(tempFile.path).copy(newPath);
    } catch (e) {
      rethrow;
    }
  }

  static void deleteFile(File file) {
    try {
      if (file.existsSync()) {
        file.deleteSync();
      }
    } catch (e) {
      debugPrint('Error deleting file: $e');
    }
  }

  static String formatFilenamePDF(String? filename) {
    if (filename == null) return 'file.pdf';
    String filenameChecked;
    filenameChecked = filename.replaceAll(RegExp(r"[ _/|\$%&#$@!`~*]"), "-");
    filenameChecked = filenameChecked.replaceAll(
        RegExp(
            r"[ÀÁÂÃÄÅàáâãäåÒÓÔÕÕÖØòóôõöøÈÉÊËèéêëðÇçÐÌÍÎÏìíîïÙÚÛÜùúûüÑñŠšŸÿýŽž]"),
        "");
    filenameChecked = filenameChecked.split('.')[0];
    return '$filenameChecked.pdf';
  }
}

enum FileExtension { pdf, png }

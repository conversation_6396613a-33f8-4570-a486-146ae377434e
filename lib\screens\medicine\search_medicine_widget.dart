import 'dart:async';

import 'package:cooperado_minha_unimed/bloc/medicines/search-medicine/search_medicines_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SearchMedicineWidget extends StatefulWidget {
  const SearchMedicineWidget({super.key});

  @override
  State<SearchMedicineWidget> createState() => _SearchMedicineWidgetState();
}

class _SearchMedicineWidgetState extends State<SearchMedicineWidget> {
  final _controllerSearch = TextEditingController();
  Timer? _debounce;

  @override
  void dispose() {
    _controllerSearch.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();

    _debounce = Timer(const Duration(milliseconds: 500), () {
      if (query.isEmpty) {
        BlocProvider.of<SearchMedicinesCubit>(context).setToInitial();
      } else {
        BlocProvider.of<SearchMedicinesCubit>(context).searchMedicine(query: query);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      enableInteractiveSelection: false,
      autofocus: false,
      controller: _controllerSearch,
      textInputAction: TextInputAction.search,
      style: const TextStyle(color: Colors.green),
      onChanged: (query) => _onSearchChanged(query),
      maxLines: 1,
      decoration: InputDecoration(
        hintText: 'Buscar medicamento',
        hintMaxLines: null,
        suffixIcon: IconButton(
          onPressed: () {
            _controllerSearch.clear();
            BlocProvider.of<SearchMedicinesCubit>(context).setToInitial();
          },
          icon: const Icon(Icons.clear),
        ),
        contentPadding: const EdgeInsets.only(
          top: 12,
          left: 10,
          right: 10,
          bottom: 6,
        ),
        border: InputBorder.none,
      ),
    );
  }
}

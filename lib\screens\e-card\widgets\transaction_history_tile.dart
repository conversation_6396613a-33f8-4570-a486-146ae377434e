import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/ecard-models/history_transaction_model.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class TransactionHistory extends StatelessWidget {
  final List<HistoryTransactionEcardModel> transactions;

  const TransactionHistory({super.key, required this.transactions});

  @override
  Widget build(BuildContext context) {
    DateTime dateTime = DateTime.parse(transactions.first.insertionDate!);
    String formattedDate = '${DateFormat('dd').format(dateTime)} de ${DateFormat('MMMM', 'pt_BR').format(dateTime)} de ${DateFormat('yyyy').format(dateTime)}';

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          color: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Column(
            children: [
              Align(
                alignment: Alignment.centerRight,
                child: Text(
                  formattedDate,
                  style: const TextStyle(
                    color: CooperadoColors.opcionalGray3,
                  ),
                ),
              ),
              const Divider(
                color: CooperadoColors.opcionalGray3,
                thickness: 1.0,
              ),
            ],
          ),
        ),
        Flexible(
          fit: FlexFit.loose,
          child: ListView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: transactions.length,
            itemBuilder: (context, index) {
              var transactionModel = transactions[index];
              DateTime? exclusionDate;
              DateTime? startDate;

              if (transactionModel.exclusionDate != null) {
                exclusionDate = DateTime.parse(transactionModel.exclusionDate!);
              }

              if (transactionModel.startDate != null) {
                startDate = DateTime.parse(transactionModel.startDate!);
              }

              return Column(
                children: [
                  ListTile(
                    onTap: () => showTransactionModal(context, transactionModel),
                    leading: const CircleAvatar(
                      radius: 23,
                      backgroundColor: Color.fromARGB(80, 66, 133, 95),
                      child: Icon(Icons.quick_contacts_mail_outlined, color: CooperadoColors.greenDark),
                    ),
                    title: Text(
                      transactionModel.viewAddress?.streetName ?? 'Endereço desconhecido',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    subtitle: (exclusionDate != null && startDate != null)
                        ? Text(
                            'Tempo ativo: ${returnActiveTime(startDate.toIso8601String(), (DateTime.parse(transactionModel.exclusionDate!)).toIso8601String())}',
                            style: const TextStyle(
                              color: Colors.grey,
                            ),
                          )
                        : null,
                    trailing: const Icon(Icons.arrow_forward_ios, color: Colors.grey),
                  ),
                  const Divider(
                    color: CooperadoColors.opcionalGray3,
                    thickness: 1.0,
                  ),
                ],
              );
            },
          ),
        )
      ],
    );
  }

  void showTransactionModal(BuildContext context, HistoryTransactionEcardModel transactionModel) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(25.0)),
      ),
      builder: (BuildContext context) {
        DateTime? dateTime;
        DateTime? exclusionDate;
        DateTime? startDate;
        String formattedDate = 'Data não disponível';

        if (transactionModel.startDate != null) {
          dateTime = DateTime.parse(transactionModel.startDate!);
          formattedDate = '${DateFormat('dd').format(dateTime)} de ${DateFormat('MMMM', 'pt_BR').format(dateTime)} de ${DateFormat('yyyy').format(dateTime)}';
          startDate = dateTime;
        }

        if (transactionModel.exclusionDate != null) {
          exclusionDate = DateTime.parse(transactionModel.exclusionDate!);
        }

        return Container(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 16.0),
                child: Text(
                  'Detalhes da solicitação',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailRow(
                        title: 'Local',
                        value: transactionModel.viewAddress?.streetName ?? 'Endereço desconhecido',
                      ),
                      _buildDivider(),
                      _buildDetailRow(
                        title: 'Data da solicitação',
                        value: formattedDate,
                      ),
                      _buildDivider(),
                      _buildDetailRow(
                        title: 'Tempo ativo',
                        value: (startDate != null && exclusionDate != null) ? returnActiveTime(startDate.toIso8601String(), exclusionDate.toIso8601String()) : '',
                      ),
                      _buildDivider(),
                      _buildDetailRow(
                        title: 'Horário da ativação',
                        value: startDate != null ? formatTime24Hours(startDate.toLocal()) : '',
                      ),
                      _buildDivider(),
                      _buildDetailRow(
                        title: 'Horário de finalização',
                        value: exclusionDate != null ? formatTime24Hours(exclusionDate.toLocal()) : '',
                      ),
                      const SizedBox(height: 100),
                    ],
                  ),
                ),
              ),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                  ),
                  child: const Text('Fechar'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  String returnActiveTime(String dataInsercao, String dataExclusao) {
    DateTime startTimeDate = DateTime.parse(dataInsercao);
    DateTime exclusionDate = DateTime.parse(dataExclusao);

    Duration activeDuration = exclusionDate.difference(startTimeDate);
    String formattedDuration =
        '${activeDuration.inHours.toString().padLeft(2, '0')}:'
        '${(activeDuration.inMinutes % 60).toString().padLeft(2, '0')}:'
        '${(activeDuration.inSeconds % 60).toString().padLeft(2, '0')}';
    return formattedDuration;
  }

  String formatTime24Hours(DateTime insertionDate) {
    int hour = insertionDate.hour;
    int minute = insertionDate.minute;

    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }

/*
decidi manter esse trecho comentando pois nao ficou claro se o horario deve ser exibido em formato 12h ou 24h pelo figma, visto 
que ele parece utilizar o formato 24 horas mas ainda sim o designer colocou am e pm no horario

 String formatTimeWithAmPm(DateTime insertionDate) { 
  int hour = insertionDate.hour; int minute = insertionDate.minute; String period = hour < 12 ? 'AM' : 'PM'; 
  int formattedHour = hour % 12; 
  formattedHour = formattedHour == 0 ? 12 : formattedHour; 
return '${formattedHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
 } */

  Widget _buildDetailRow({required String title, required String value, String? subtitle}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: const TextStyle(color: CooperadoColors.grayLight2),
          ),
          Text(
            subtitle != null ? '$value $subtitle' : value,
            style: const TextStyle(color: Colors.black),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return const Divider(
      color: CooperadoColors.grayLight2,
    );
  }
}

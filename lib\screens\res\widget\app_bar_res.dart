import 'package:flutter/material.dart';

class AppBarRes extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final String nameBeneficiary;
  const AppBarRes({
    super.key,
    required this.title,
    required this.nameBeneficiary,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Column(
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 14),
          ),
          const SizedBox(
            height: 4,
          ),
          Text(
            nameBeneficiary,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
          )
        ],
      ),
      centerTitle: true,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

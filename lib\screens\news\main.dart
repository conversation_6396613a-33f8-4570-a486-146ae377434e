import 'package:cooperado_minha_unimed/bloc/news/news_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/news/list_all_news.dart';
import 'package:cooperado_minha_unimed/screens/news/list_fiscal_council_news.dart';
import 'package:cooperado_minha_unimed/screens/news/list_others_news.dart';
import 'package:cooperado_minha_unimed/screens/news/list_transparency_news.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../bloc/theme/theme_cubit.dart';

class NewsScreen extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const NewsScreen(
      {super.key, required this.analytics, required this.observer});

  @override
  NewsScreenState createState() => NewsScreenState();
}

class NewsScreenState extends State<NewsScreen> with TickerProviderStateMixin {
  TabController? _tabController;
  late List<StatefulWidget> tabsContent;
  late List<Widget> tabsTitle;
  int? _tabIndex;
  bool searchActivated = false;
  TextEditingController searchText = TextEditingController();

  @override
  void initState() {
    _setupTabs();
    super.initState();
    widget.analytics.logScreenView(
      screenName: 'Notícias',
      screenClass: 'NewsScreen',
    );
  }

  _setupTabs() {
    tabsContent = [];
    tabsTitle = [];

    tabsContent
        .add(ListAllNews(categories: const ["todos"], searchText: searchText));
    tabsTitle.add(_tabTitle("Todas"));

    tabsContent.add(ListTransparencyNews(
      categories: const ["noticias-transparencia"],
      searchText: searchText,
    ));
    tabsTitle.add(_tabTitle("Transparência"));

    tabsContent.add(ListFiscalCouncilNews(
      categories: const ["noticias-conselho-fiscal"],
      searchText: searchText,
    ));
    tabsTitle.add(_tabTitle("Conselho Fiscal"));

    tabsContent.add(ListOthersNews(
      searchText: searchText,
      categories: const ["para-voce"],
    ));
    tabsTitle.add(_tabTitle("Outras"));

    _tabController = TabController(
      length: tabsContent.length,
      vsync: this,
      initialIndex: 0,
    );
    _tabController!.addListener(_updateIndex);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        context.read<ThemeCubit>().setTheme(ThemeCooperado.purple());
      },
      child: PopScope(
        onPopInvokedWithResult: (value, result) async {
          context.read<NewsCubit>().getListNewsEvent(
            categories: ["todos"],
            page: 1,
          );
          context.read<ThemeCubit>().setTheme(ThemeCooperado.purple());
        },
        child: DefaultTabController(
          length: 4,
          child: Scaffold(
              backgroundColor: Colors.white,
              appBar: AppBar(
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () {
                    context.read<NewsCubit>().getListNewsEvent(
                      categories: ["todos"],
                      page: 1,
                    );
                    Navigator.pop(context);
                  },
                ),
                actions: [
                  IconButton(
                      onPressed: () {
                        setState(() {
                          searchActivated = !searchActivated;

                          searchActivated
                              ? context
                                  .read<ThemeCubit>()
                                  .setTheme(ThemeCooperado.purpleWithSelector())
                              : context
                                  .read<ThemeCubit>()
                                  .setTheme(ThemeCooperado.purple());
                        });
                      },
                      icon: const Icon(Icons.search))
                ],
                centerTitle: true,
                title: const Text('Notícias'),
              ),
              body: SafeArea(
                child: Column(
                  children: <Widget>[
                    Container(
                      padding: const EdgeInsets.all(10.0),
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(30),
                            bottomRight: Radius.circular(30)),
                        color: CooperadoColors.grayLight3,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey,
                            offset: Offset(0.0, 1.0), //(x,y)
                            blurRadius: 6.0,
                          ),
                        ],
                      ),
                      child: TabBar(
                        tabAlignment: TabAlignment.start,
                        isScrollable: true,
                        controller: _tabController,
                        unselectedLabelColor: CooperadoColors.grayDark2,
                        indicator: const BoxDecoration(
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(12),
                                topRight: Radius.circular(12),
                                bottomLeft: Radius.circular(12)),
                            color: Colors.white),
                        labelColor: CooperadoColors.grayDark2,
                        tabs: tabsTitle,
                      ),
                    ),
                    //if (searchActivated)
                    AnimatedSwitcher(
                        switchInCurve: Curves.easeIn,
                        switchOutCurve: Curves.easeInOut,
                        duration: const Duration(milliseconds: 300),
                        child: searchActivated
                            ? Container(
                                decoration: const BoxDecoration(
                                    color: CooperadoColors.tealGreen,
                                    borderRadius: BorderRadius.only(
                                        topRight: Radius.circular(20),
                                        topLeft: Radius.circular(20),
                                        bottomLeft: Radius.circular(20))),
                                margin: EdgeInsets.symmetric(
                                    vertical: 10,
                                    horizontal:
                                        MediaQuery.of(context).size.width *
                                            0.1),
                                child: TextField(
                                  controller: searchText,
                                  style: const TextStyle(color: Colors.white),
                                  decoration: InputDecoration(
                                      hintStyle:
                                          const TextStyle(color: Colors.white),
                                      hintText: 'Buscar',
                                      border: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(20.0),
                                      ),
                                      suffixIcon: const Icon(
                                        Icons.search,
                                        color: Colors.white,
                                      )),
                                ),
                              )
                            : const SizedBox()),
                    Expanded(
                      child: TabBarView(
                        controller: _tabController,
                        physics: const NeverScrollableScrollPhysics(),
                        children: tabsContent,
                      ),
                    ),
                  ],
                ),
              )),
        ),
      ),
    );
  }

  Widget _tabTitle(String title) {
    return Container(
      constraints: const BoxConstraints(minWidth: 50),
      alignment: Alignment.center,
      padding: const EdgeInsets.all(0.0),
      height: 100.0,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          const Icon(
            Icons.list,
            color: CooperadoColors.tealGreen,
          ),
          Tab(
            text: title,
          )
        ],
      ),
    );
  }

  _updateIndex() {
    setState(() {
      searchText.clear();
      searchActivated = false;
      _tabIndex = _tabController!.index;
    });
    debugPrint("====== INDEX: $_tabIndex");
  }
}

import 'package:cooperado_minha_unimed/screens/transparencia/beneficiary/beneficiary-graphs/piegraph_widget.dart';
import 'package:flutter/material.dart';

class BeneficiaryPieGraph extends StatelessWidget {
  final List data;
  const BeneficiaryPieGraph({super.key, required this.data});
  //List<BeneficiaryData> listBeneficiaryData =[];

  List<BeneficiaryData> getListSeries() {
    final List<BeneficiaryData> seriesList = data
        .map<BeneficiaryData>((e) =>
            BeneficiaryData(quantity: e.quantidade, description: e.descricao))
        .toList();

    return seriesList;
  }

  @override
  Widget build(BuildContext context) {
    final data = getListSeries();
    return PieGraphWidget(
      listBenefic: data,
    );
  }
}

class BeneficiaryData {
  final int? quantity;
  final String? description;
  BeneficiaryData({this.quantity, this.description});
}

import 'package:cooperado_minha_unimed/bloc/indicators/cost_comparative/cost_comparative_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/comparativo-custo/grafico_custo.dart';
import 'package:cooperado_minha_unimed/shared/vo/indicadores/comparativo_custo.dart';
import 'package:cooperado_minha_unimed/shared/widgets/card_refresh.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:cooperado_minha_unimed/shared/widgets/graphics/choose_date.dart';
import 'package:cooperado_minha_unimed/shared/widgets/month_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class CardComparativoCusto extends StatefulWidget {
  final Function refresh;
  const CardComparativoCusto({
    super.key,
    required this.refresh,
  });
  @override
  CardComparativoCustoState createState() => CardComparativoCustoState();
}

class CardComparativoCustoState extends State<CardComparativoCusto> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return CardRefresh(
      title: const Text("Comparativo de custo",
          style: TextStyle(
            color: CooperadoColors.blackText,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          )),
      refresh: _iconRefresh(),
      child: BlocBuilder<CostComparativeCubit, CostComparativeState>(
        builder: (context, state) {
          if (state is LoadedCostComparativeState) {
            return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  const Padding(
                    padding: EdgeInsets.only(top: 8.0),
                    child: Text(
                      "GERADOS PELA UNIMED",
                      style: TextStyle(color: CooperadoColors.grayDark),
                    ),
                  ),
                  ChooseDateWidget(
                    date: context.read<CostComparativeCubit>().selectedDate,
                    onPressed: _onPressed,
                    textBox: Text(
                        context.read<CostComparativeCubit>().formattedDate!),
                  ),
                  _success(state.costComparativeVO)
                ]);
          } else if (state is LoadingCostComparativeState) {
            return const SpinKitCircle(
              color: CooperadoColors.tealGreen,
            );
          } else if (state is ErrorCostComparativeState) {
            return Column(
              children: <Widget>[
                ChooseDateWidget(
                  date: context.read<CostComparativeCubit>().selectedDate,
                  onPressed: _onPressed,
                  textBox:
                      Text(context.read<CostComparativeCubit>().formattedDate!),
                ),
                ErrorBanner(message: state.message)
              ],
            );
          } else {
            return Container();
          }
        },
      ),
    );
  }

  Widget _iconRefresh() {
    return BlocBuilder<CostComparativeCubit, CostComparativeState>(
        builder: (context, state) {
      if (state is ErrorCostComparativeState) {
        return InkWell(
            child: const Icon(Icons.refresh), onTap: () => widget.refresh());
      } else {
        return Container();
      }
    });
  }

  Widget _success(RetornoComparativoCustoVO retornoComparativoCustoVO) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: <Widget>[
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Row(
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.only(right: 4.0),
                      child: Container(
                        decoration: BoxDecoration(
                            color: CooperadoColors.paleGreenish,
                            borderRadius: BorderRadius.circular(4.0)),
                        padding: const EdgeInsets.all(8.0),
                        child: const Icon(
                          Icons.people_alt_outlined,
                          color: Colors.black,
                        ),
                      ),
                    ),
                   BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
  builder: (context, sensitiveState) {
    bool isSensitiveDataVisible = !sensitiveState.isSensitiveDataVisible;
    String displayValue = isSensitiveDataVisible
        ? '*****'
        : retornoComparativoCustoVO.getValorEspecialidadeFormat();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text("Média da \nEspecialidade"),
        Text(displayValue),
      ],
    );
  },
)
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.only(right: 4.0),
                      child: Container(
                        decoration: BoxDecoration(
                            color: CooperadoColors.limaColor,
                            borderRadius: BorderRadius.circular(4.0)),
                        padding: const EdgeInsets.all(8.0),
                        child: const Icon(
                          Icons.person_outline_outlined,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
  builder: (context, sensitiveState) {
    bool isSensitiveDataVisible = !sensitiveState.isSensitiveDataVisible;
    String displayValue = isSensitiveDataVisible
        ? '*****'
        : retornoComparativoCustoVO.getValorPrestadorFormat();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text("Prestador"),
        Text(displayValue),
      ],
    );
  },
)
                  ],
                )
              ],
            ),
          ),
          const Expanded(child: SizedBox(height: 100, child: CustoChart()))
          // SizedBox(height: 300, width: 100, child:)
        ],
      ),
    );
  }

  Future<DateTime?> showMonthPicker({
    required BuildContext context,
    required DateTime initialDate,
    DateTime? firstDate,
    DateTime? lastDate,
  }) async {
    return await showDialog<DateTime>(
        context: context,
        builder: (context) => MonthPickerCustom(
              initialDate: initialDate,
              firstDate: firstDate,
              lastDate: lastDate,
              onPressed: _onPressed,
            ));
  }

  void _onPressed(selectedDate) {
    context.read<CostComparativeCubit>().selectDate(selectedDate);
  }
}

part of 'report_production_cubit.dart';

abstract class ReportProductionState extends Equatable {
  const ReportProductionState();
}

class ReportProductionInitial extends ReportProductionState {
  @override
  List<Object> get props => [];
}

class LoadingGetReportProductionState extends ReportProductionState {
  @override
  List<Object> get props => [];
}

class ErrorGetReportProductionState extends ReportProductionState {
  final String message;
  @override
  List<Object> get props => [message];
  const ErrorGetReportProductionState(this.message);
}

class DoneGetReportProductionState extends ReportProductionState {
  final String base64Report;
  @override
  List<Object?> get props => [base64Report];

  const DoneGetReportProductionState(this.base64Report);
}

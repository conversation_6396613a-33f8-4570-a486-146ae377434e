import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/documents/detail/res_documents_detail_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/documents/res_documents_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/res/documents/res_document_detail.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/app_bar_res.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/custom_button_card.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/empty_list.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/erro_service.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/expandable_card.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/filters_widget.dart';
import 'package:cooperado_minha_unimed/shared/utils/router_observer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:intl/intl.dart';

class ResInternalDocumentsScreen extends StatefulWidget {
  final String nameBeneficiary;
  final String beneficiaryCard;
  const ResInternalDocumentsScreen(
      {super.key,
      required this.nameBeneficiary,
      required this.beneficiaryCard});

  @override
  State<ResInternalDocumentsScreen> createState() =>
      _ResInternalAllergiesScreenState();
}

class _ResInternalAllergiesScreenState extends State<ResInternalDocumentsScreen>
    with RouteAware {
  final bool _isLoading = false;
  bool loadingDetail = false;
  int? _filterMonthSelected = 0;
  DateTimeRange? _dateRangeToFilter;
  final List<int> _lastMonthsToFilter = [3, 6];

  List<ExpansionTileController> controllers = [];
  int? previusSelectedIndex;

  @override
  void initState() {
    _loadDocuments();
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CooperadoColors.backgroundWhiteColor,
      appBar: AppBarRes(
        title: 'Documentos',
        nameBeneficiary: widget.nameBeneficiary,
      ),
      body: Padding(
        padding: const EdgeInsets.only(left: 20, right: 20, top: 10),
        child: Column(
          children: [
            _filterDate(),
            BlocBuilder<ResDocumentsCubit, ResDocumentsState>(
              builder: (context, state) {
                if (state is LoadingResDocumentsState) {
                  return const Expanded(
                    child: Center(
                        child: SpinKitCircle(color: CooperadoColors.tealGreen)),
                  );
                } else if (state is LoadedResDocumentsState) {
                  return Expanded(
                    child: RefreshIndicator(
                      onRefresh: () async {
                        _loadDocuments();
                      },
                      child: ListView.builder(
                        physics: const ClampingScrollPhysics(
                            parent: AlwaysScrollableScrollPhysics()),
                        itemCount: state.listResDocuments.length,
                        itemBuilder: (context, index) {
                          ExpansionTileController controller =
                              ExpansionTileController();

                          controllers.add(controller);
                          return Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: ExpandableCard(
                              controller: controller,
                              pathIcon: 'assets/svg/icon_document.svg',
                              title: state.listResDocuments[index].codigo ?? '',
                              subtitle: state
                                  .listResDocuments[index].dataEntradaFormatted,
                              loading: loadingDetail,
                              onExpansionChanged: (value) {
                                setState(() {
                                  if (value && previusSelectedIndex != index) {
                                    if (previusSelectedIndex != null) {
                                      controllers[previusSelectedIndex!]
                                          .collapse();
                                    }
                                    previusSelectedIndex = index;
                                  }
                                });
                              },
                              buttons: [
                                CustomButtonCard(
                                  text: 'Documentos',
                                  onPressed: () {
                                    _showDetailModal(
                                        state.listResDocuments[index].codigo ??
                                            '');
                                    context
                                        .read<ResDocumentDetailCubit>()
                                        .getDocumentDetail(
                                          crm: context
                                              .read<AuthCubit>()
                                              .credentials
                                              .crm,
                                          card: widget.beneficiaryCard,
                                          code: state.listResDocuments[index]
                                                  .codigo ??
                                              '',
                                          index: index,
                                        );
                                  },
                                ),
                              ],
                              additionalInfo: [
                                {
                                  'title': 'Tipo',
                                  'description':
                                      state.listResDocuments[index].tipo ??
                                          'Sem dados no momento.'
                                },
                                {
                                  'title': 'Local',
                                  'description':
                                      state.listResDocuments[index].nomeLocal ??
                                          'Sem dados no momento.'
                                },
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  );
                } else if (state is NoDataResDocumentsState) {
                  return Expanded(
                    child: EmptyList(
                      pathIcon: 'assets/svg/icon_file.svg',
                      message: _dateRangeToFilter != null
                          ? 'Não foi encontrado nenhum documento para o período de ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.start)} até  ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.end)}'
                          : 'Não foi encontrado nenhum documento.',
                    ),
                  );
                } else if (state is NoDataResDocumentsState) {
                  return Expanded(
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.only(top: 32.0),
                        child: EmptyList(
                          pathIcon: 'assets/svg/icon_file.svg',
                          message: _dateRangeToFilter != null
                              ? 'Sem dados de documentos para o período de ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.start)} até  ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.end)}'
                              : 'Sem dados de documentos.',
                        ),
                      ),
                    ),
                  );
                } else if (state is ErrorResDocumentsState) {
                  return Expanded(
                    child: ErroService(
                      message: state.message,
                      onPressed: () {
                        _loadDocuments();
                      },
                    ),
                  );
                }

                return Container();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _filterDate() {
    return FiltersWidget(
      isLoading: _isLoading,
      lastMonthsToFilter: _lastMonthsToFilter,
      filterMonthSelected: _filterMonthSelected,
      dateRangeToFilter: _dateRangeToFilter,
      onMonthFilterChanged: (filterSelected) {
        setState(() {
          _filterMonthSelected = filterSelected;
          filterSelected == null
              ? _dateRangeToFilter = null
              : _dateRangeToFilter = DateTimeRange(
                  start: _selectedDataStart(filterSelected),
                  end: DateTime.now(),
                );
        });
        _loadDocuments();
      },
      onClearDateRange: () {
        setState(() {
          _dateRangeToFilter = null;
          _filterMonthSelected = null;
        });
        _loadDocuments();
      },
      selectDateToFilter: _selectDateToFilter,
      onDateRangeSelected: (dateRange) {
        setState(() {
          _filterMonthSelected = null;
          _dateRangeToFilter = dateRange;
        });
        _loadDocuments();
      },
    );
  }

  void _showDetailModal(String code) {
    showModalBottomSheet<void>(
      context: context,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(15))),
      builder: (BuildContext context) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            BlocProvider(
              create: (context) => ResDocumentDetailCubit()
                ..getDocumentDetail(
                  crm: context.read<AuthCubit>().credentials.crm,
                  card: widget.beneficiaryCard,
                  code: code,
                ),
              child:
                  BlocBuilder<ResDocumentDetailCubit, ResDocumentDetailState>(
                builder: (context, state) {
                  if (state is LoadingResDocumentDetailState) {
                    return Column(
                      children: [
                        Center(
                          child: Container(
                            height: 4,
                            width: 100,
                            margin: const EdgeInsets.symmetric(
                              vertical: 10,
                            ),
                            decoration: BoxDecoration(
                              color: CooperadoColors.grayLight3,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                        ),
                        const Center(
                          child:
                              SpinKitCircle(color: CooperadoColors.tealGreen),
                        ),
                        const SizedBox(height: 16),
                      ],
                    );
                  } else if (state is LoadedResDocumentDetailState) {
                    return Expanded(
                        child: ResDocumentDetails(
                            resDocumentsDetailModel:
                                state.resDocumentDetailModel));
                  } else if (state is ErrorResDocumentDetailState) {
                    return Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Center(
                          child: ErroService(
                        message: state.message,
                        onPressed: () {
                          context
                              .read<ResDocumentDetailCubit>()
                              .getDocumentDetail(
                                crm: context.read<AuthCubit>().credentials.crm,
                                card: widget.beneficiaryCard,
                                code: code,
                              );
                        },
                      )),
                    );
                  } else if (state is NoDataResDocumentDetailState) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 32.0),
                      child: Center(
                          child: EmptyList(
                        pathIcon: 'assets/svg/icon_file.svg',
                        message: _dateRangeToFilter != null
                            ? 'Sem dados de documentos para o período de ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.start)} até  ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.end)}'
                            : 'Sem dados de documentos.',
                      )),
                    );
                  } else {
                    return Container();
                  }
                },
              ),
            ),
          ],
        );
      },
    );
  }

  DateTime _getFirstDate() {
    DateTime now = DateTime.now();
    int year = now.year;
    bool isLeapYear = (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    return now.subtract(Duration(days: isLeapYear ? 366 : 365));
  }

  Future<DateTime?> _selectDateToFilter(BuildContext context, String helpText,
      {DateTime? firstDate}) async {
    return await showDatePicker(
      context: context,
      helpText: helpText,
      initialDate: DateTime.now(),
      firstDate: firstDate ?? _getFirstDate(),
      lastDate: DateTime.now(),
      keyboardType: TextInputType.text,
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: const ColorScheme.light(
              primary: CooperadoColors.tealGreen,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );
  }

  _loadDocuments() {
    setState(() {
      controllers = [];
      previusSelectedIndex = null;
    });

    context.read<ResDocumentsCubit>().listResDocuments(
          crm: context.read<AuthCubit>().credentials.crm,
          card: widget.beneficiaryCard,
          dataRange: _dateRangeToFilter,
        );
  }

  DateTime _selectedDataStart(int meses) {
    DateTime dataAtual = DateTime.now();
    DateTime novaData = DateTime(
      dataAtual.year,
      dataAtual.month - meses,
      dataAtual.day,
    );

    return novaData;
  }
}

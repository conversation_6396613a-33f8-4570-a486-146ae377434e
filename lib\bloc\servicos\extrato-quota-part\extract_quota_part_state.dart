part of 'extract_quota_part_cubit.dart';

abstract class ExtractQuotaPartState extends Equatable {
  const ExtractQuotaPartState();
}

class ExtractQuotaPartInitial extends ExtractQuotaPartState {
  @override
  List<Object> get props => [];
}

class LoadingGetExtractQuotaPartState extends ExtractQuotaPartState {
  @override
  List<Object> get props => [];
}

class ErrorGetExtractQuotaPartState extends ExtractQuotaPartState {
  final String message;
  @override
  List<Object> get props => [message];
  const ErrorGetExtractQuotaPartState(this.message);
}

class DoneGetExtractQuotaPartState extends ExtractQuotaPartState {
  final ExtractCopartVO extractQuotaPart;
  @override
  List<Object> get props => [extractQuotaPart];

  const DoneGetExtractQuotaPartState(this.extractQuotaPart);
}

class SelectDateLoading extends ExtractQuotaPartState {
  @override
  List<Object> get props => [];
}

class DoneSelectDate extends ExtractQuotaPartState {
  final DateTime? date;
  @override
  List<Object?> get props => [date];
  const DoneSelectDate(this.date);
}

import 'package:biometria_perfilapps/main.dart';
import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:evaluation/evaluation.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';

enum Flavor { dev, test, production }

const codUnimedFortaleza = 63;

class FlavorValues {
  FlavorValues({
    required this.portal,
    required this.portalV2,
    required this.remoteLogEnv,
    required this.configuracoes,
    required this.guia,
    required this.consultometro,
    required this.consultorioOnline,
    required this.servicos,
    required this.relatorioReport,
    required this.profilePermissions,
    required this.graphql,
    required this.personalAssistant,
    required this.evaluationEnv,
    required this.lostPasswordPortal,
    required this.oneSignalId,
    required this.validadeBioIdEnv,
    this.codUnimed = codUnimedFortaleza,
  });

  final RemoteLogEnv remoteLogEnv;

  final ConfiguracoesData configuracoes;
  //final String oneSignalId = "************************************";
  final String oneSignalId;
  final int quantidadeSolicitacoes = 30;
  final Portal portal;
  final Portal portalV2;
  final Guia guia;
  final Consultometro consultometro;
  final ConsultorioOnline consultorioOnline;
  final Servicos servicos;
  final DefaultCredencial profilePermissions;
  final DefaultCredencial graphql;
  final PortalRelatorioReport relatorioReport;
  final PersonalAssistantApi personalAssistant;
  final LostPasswordPortal lostPasswordPortal;
  final EvaluationEnv evaluationEnv;
  final BiometryUnimedEnv validadeBioIdEnv;

  final int codUnimed;
}

class FlavorConfig {
  final Flavor flavor;
  final String name;
  final FlavorValues values;
  static FlavorConfig? _instance;

  factory FlavorConfig({required Flavor flavor, required FlavorValues values}) {
    _instance ??= FlavorConfig._internal(
        flavor, StringUtils.enumName(flavor.toString()), values);
    return _instance!;
  }

  FlavorConfig._internal(this.flavor, this.name, this.values);
  static FlavorConfig? get instance {
    return _instance;
  }

  static bool isProduction() => _instance!.flavor == Flavor.production;
  static bool isDevelopment() => _instance!.flavor == Flavor.dev;
  static bool isTest() => _instance!.flavor == Flavor.test;
}

class PortalRelatorioReport {
  final String url;
  PortalRelatorioReport({required this.url});
}

class Portal {
  final String url;

  Portal({required this.url});
}

class DefaultCredencial {
  final String url;
  final String user;
  final String password;
  final String? codUsuarioAuditoria;

  DefaultCredencial({
    required this.url,
    required this.user,
    required this.password,
    this.codUsuarioAuditoria,
  });
}

class Guia {
  final String url;

  Guia({required this.url});
}

class Servicos {
  final String url;
  final String user;
  final String password;

  Servicos({required this.url, required this.user, required this.password});
}

class Consultometro {
  final String url;

  Consultometro({required this.url});
}

class ConsultorioOnline {
  final String url;

  ConsultorioOnline({required this.url});
}

class PersonalAssistantApi {
  final String url;

  PersonalAssistantApi({required this.url});
}

class ConfiguracoesData {
  final String? url;
  final String? token;

  ConfiguracoesData({this.url, this.token})
      : assert(
          url != null && url.isNotEmpty && token != null && token.isNotEmpty,
        );
}

class LostPasswordPortal {
  final String user;
  final String password;

  LostPasswordPortal({
    required this.user,
    required this.password,
  });
}

class FlavorTEST extends FlavorValues {
  FlavorTEST()
      : super(
          remoteLogEnv: RemoteLogEnv.TEST,
          oneSignalId: "************************************",
          configuracoes: ConfiguracoesData(
            url: "https://apiconfig.unimedfortaleza.com.br",
            token:
                "3256dde2ae32021afa7b9f96fdea200bd405ca5b3ba69a237e3dc81e208a80897acc59ca7f3a557d39b5013e928670f8",
          ),
          portal: Portal(
              url:
                  'https://portalhmg.unimedfortaleza.com.br/portal_homologacao/api/v1/cooperado/'),
          portalV2: Portal(
              url: 'https://portalhmg.unimedfortaleza.com.br/portal/api/'),
          guia: Guia(
              url:
                  'https://remuneracaovariavelhmg.unimedfortaleza.com.br/resultado-iqa'),
          consultometro: Consultometro(
              url: 'http://s01lnx106.unimedfortaleza.com.br:4204/login'),
          consultorioOnline: ConsultorioOnline(
              url:
                  'https://consultorioonlinehmg.unimedfortaleza.com.br/prestador/login'),
          profilePermissions: DefaultCredencial(
            url: 'https://perfilappshmg.unimedfortaleza.com.br/api/',
            user: '<EMAIL>',
            password: 'ZfZULfm72dbhE95ssbbfnWPMtdeJ7By6dd2TVtusMd4eKZ9abh',
          ),
          graphql: DefaultCredencial(
            url: 'https://perfilappshmg.unimedfortaleza.com.br/graphql',
            user: '<EMAIL>',
            password: 'ZfZULfm72dbhE95ssbbfnWPMtdeJ7By6dd2TVtusMd4eKZ9abh',
          ),
          servicos: Servicos(
              url:
                  'https://apiservicoshmg.unimedfortaleza.com.br/servicos-unimed/api/',
              user: 'APP_COOPERADO',
              password: 'APP5muuz8xq'),
          relatorioReport: PortalRelatorioReport(
              url:
                  'http://portalhmg.unimedfortaleza.com.br/portal_homologacao/'),
          personalAssistant: PersonalAssistantApi(
              url: "https://apipersonaldigitalhmg.unimedfortaleza.com.br/"),
          evaluationEnv: EvaluationEnv.TEST,
          lostPasswordPortal: LostPasswordPortal(
              user: '40bs242a0ti9nkcmy9y3iiyxcvfux4vc', password: '123456'),
          validadeBioIdEnv: BiometryUnimedEnv.test,
        );
}

class FlavorDEV extends FlavorValues {
  FlavorDEV()
      : super(
          remoteLogEnv: RemoteLogEnv.DEV,
          oneSignalId: "************************************",
          configuracoes: ConfiguracoesData(
            url: "https://apiconfig.unimedfortaleza.com.br",
            token:
                "3256dde2ae32021afa7b9f96fdea200bd405ca5b3ba69a237e3dc81e208a80897acc59ca7f3a557d39b5013e928670f8",
          ),
          portal: Portal(
            url:
                'https://portalhmg.unimedfortaleza.com.br/portal_homologacao/api/v1/cooperado/',
          ),
          portalV2: Portal(
              url: 'https://portalhmg.unimedfortaleza.com.br/portal/api/'),
          guia: Guia(
            url:
                'https://remuneracaovariavelhmg.unimedfortaleza.com.br/resultado-iqa',
          ),
          consultometro: Consultometro(
            url:
                'https://consultometrohmg.unimedfortaleza.com.br/prestador/login',
          ),
          consultorioOnline: ConsultorioOnline(
            url:
                'https://consultorioonlinehmg.unimedfortaleza.com.br/prestador/login',
          ),
          servicos: Servicos(
            url:
                'https://apiservicoshmg.unimedfortaleza.com.br/servicos-unimed/api/',
            user: 'APP_COOPERADO',
            password: 'APP5muuz8xq',
          ),
          profilePermissions: DefaultCredencial(
            url: 'https://perfilappshmg.unimedfortaleza.com.br/api/',
            user: '<EMAIL>',
            password: 'ZfZULfm72dbhE95ssbbfnWPMtdeJ7By6dd2TVtusMd4eKZ9abh',
          ),
          graphql: DefaultCredencial(
            url: 'https://perfilappshmg.unimedfortaleza.com.br/graphql',
            user: '<EMAIL>',
            password: 'ZfZULfm72dbhE95ssbbfnWPMtdeJ7By6dd2TVtusMd4eKZ9abh',
          ),
          relatorioReport: PortalRelatorioReport(
            url: 'http://portalhmg.unimedfortaleza.com.br/portal_homologacao/',
          ),
          personalAssistant: PersonalAssistantApi(
              url: "https://apipersonaldigitaldev.unimedfortaleza.com.br/"),
          evaluationEnv: EvaluationEnv.DEV,
          lostPasswordPortal: LostPasswordPortal(
              user: '40bs242a0ti9nkcmy9y3iiyxcvfux4vc', password: '123456'),
          validadeBioIdEnv: BiometryUnimedEnv.dev,
        );
}

class FlavorPROD extends FlavorValues {
  FlavorPROD()
      : super(
          remoteLogEnv: RemoteLogEnv.PROD,
          oneSignalId: "************************************",
          configuracoes: ConfiguracoesData(
            url: "https://apiconfig.unimedfortaleza.com.br",
            token:
                "3256dde2ae32021afa7b9f96fdea200bd405ca5b3ba69a237e3dc81e208a80897acc59ca7f3a557d39b5013e928670f8",
          ),
          portal: Portal(
            url: 'https://www.unimedfortaleza.com.br/api/v1/cooperado/',
          ),
          portalV2: Portal(url: 'https://www.unimedfortaleza.com.br/api/'),
          guia: Guia(
            url: 'https://renumeracao-variavel.unimedfortaleza.com.br/login',
          ),
          consultometro: Consultometro(
            url: 'https://consultometro.unimedfortaleza.com.br/login',
          ),
          consultorioOnline: ConsultorioOnline(
              url:
                  'https://consultorio-online.unimedfortaleza.com.br/prestador/login'),
          profilePermissions: DefaultCredencial(
            url: 'https://perfilapps.unimedfortaleza.com.br/api/',
            user: '<EMAIL>',
            password: '23Dyb33YwYDNPPUN5WeBTSKq88Dr66D5qkrkmb7YqWaCkEjg5S',
          ),
          graphql: DefaultCredencial(
            url: 'https://perfilapps.unimedfortaleza.com.br/graphql',
            user: '<EMAIL>',
            password: '23Dyb33YwYDNPPUN5WeBTSKq88Dr66D5qkrkmb7YqWaCkEjg5S',
          ),
          servicos: Servicos(
            url:
                'https://apiservicos.unimedfortaleza.com.br/servicos-unimed/api/',
            user: 'APP_COOPERADO',
            password: 'APP5muuz8xq',
          ),
          relatorioReport: PortalRelatorioReport(
            url: 'https://www.unimedfortaleza.com.br/',
          ),
          personalAssistant: PersonalAssistantApi(
              url: "https://apipersonaldigital.unimedfortaleza.com.br/"),
          evaluationEnv: EvaluationEnv.PROD,
          lostPasswordPortal: LostPasswordPortal(
            user: 'k39es8kz9t86acqbwwxnhz4mw4akw07u',
            password: '4JUNwv0dxc3nYYCN5papaFTbkrtDsnsW',
          ),
          validadeBioIdEnv: BiometryUnimedEnv.prod,
        );
}

// import 'dart:io';

// import 'package:flutter/material.dart';
// import 'package:flutter_pdfview/flutter_pdfview.dart';
// import 'package:flutter_spinkit/flutter_spinkit.dart';

// class PreviewFileScreen extends StatefulWidget {
//   final File file;
//   final String fileName;
//   final String fileType;
//   final String? title;
//   PreviewFileScreen({
//     required this.file,
//     required this.fileName,
//     required this.fileType,
//     this.title,
//   });

//   @override
//   _PreviewFileScreenState createState() => _PreviewFileScreenState();
// }

// class _PreviewFileScreenState extends State<PreviewFileScreen> {
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//         appBar: AppBarUnimed(
//           title: _title(),
//         ),
//         body: Stack(children: [
//           Align(
//             alignment: Alignment.topCenter,
//             child: SpinKitThreeBounce(
//               color: Coo.green,
//               size: 20,
//             ),
//           ),
//           widget.fileType == 'pdf'
//               ? PDFView(
//                   filePath: widget.file.path,
//                   enableSwipe: true,
//                   swipeHorizontal: true,
//                   autoSpacing: true,
//                   pageFling: true,
//                 )
//               : Center(
//                   child: Image.file(
//                     widget.file,
//                     errorBuilder: (context, error, stacktrace) {
//                       return _error();
//                     },
//                     fit: BoxFit.contain,
//                     // filterQuality: FilterQuality.high,
//                   ),
//                 ),
//         ]));
//   }

//   Widget _title() {
//     return widget.title == null || widget.title!.isEmpty ? Text(widget.fileName) : Column(mainAxisAlignment: MainAxisAlignment.center, children: [Text(widget.title as String), Text(widget.fileName, style: TextStyle(fontSize: 10))]);
//   }

//   Widget _error() {
//     return Center(
//       child: EvaTriste(message: Text('Não foi possível carregar essa imagem')),
//     );
//   }
// }

import 'dart:convert';
import 'dart:io';

void main(List<String> args) {
  // print('Hi args $args');
  print('Build iOS PROD...');

  Process.start('flutter', [
    'build',
    'ios',
    '-t',
    'lib/main_sobral_prod.dart',
    '--flavor',
    'sobral',
    '--dart-define=CLIENT_ID=UNIMED_SOBRAL',
    '--release',
    '--verbose',
    ...args
  ]).then((Process process) {
    process.exitCode.then((exitCode) {
      print('exit code: $exitCode');
    });
    process.stdout.transform(utf8.decoder).listen((data) {
      print(data);
    });

    process.stderr.transform(utf8.decoder).listen((data) {
      print(data);
    });
  }).catchError((onError) {
    print('onError $onError');
  });
}

import 'package:cooperado_minha_unimed/models/notificacao/notification.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/messages.exceptions.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:graphql/client.dart';
import 'package:http_client/http_client.dart';

class GraphQlApiNotificacao {
  final UnimedHttpClient httpClient;

  GraphQlApiNotificacao(this.httpClient);

  final logger = UnimedLogger(className: 'GraphQlApiNotificacao');

  Future<GraphQLClient> getGithubGraphQLClient() async {
    final tokenPerfilApps = await Locator.instance!.get<AuthApi>().tokenPerfilApps();

    final Link link = HttpLink(
      FlavorConfig.instance!.values.graphql.url,
      defaultHeaders: {
        'Authorization': 'Bearer $tokenPerfilApps',
      },
    );

    return GraphQLClient(
      cache: GraphQLCache(),
      link: link,
      queryRequestTimeout: const Duration(seconds: 120),
    );
  }

  Future<NotificationResponse> getListNotificacoes({
    required String codPrestador,
    int? page,
    int? perPage,
    DateTime? startDateTime,
    DateTime? endDateTime,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
        query CooperadoGetFCMNotifications {
          cooperadoGetFCMNotifications(
              codPrestador: "$codPrestador"
              page: $page
              perPage: $perPage
              startDate: ${startDateTime != null ? '"${startDateTime.toIso8601String()}Z"' : null},
              endDate: ${endDateTime != null ? '"${endDateTime.toIso8601String()}Z"' : null}
          ) {
          totalPages
          notifications {
              notificationId
              title
              description
              createdAt
              readAt
              pinned
            }
          }
        }
      ''';
      logger.e('getListNotificacoes: $query');
      final QueryOptions options = QueryOptions(
        document: gql(query),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('Error getListNotificacoes: ${result.exception}');
        throw UnimedException(MessageException.general);
      } else {
        final data = result.data!['cooperadoGetFCMNotifications'];
        logger.e('getListNotificacoes sucess');

        return NotificationResponse.fromJson(data);
      }
    } on UnimedException catch (ex) {
      logger.e('getListNotificacoes ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('getListNotificacoes exception : $ex');
      throw UnimedException(MessageException.general);
    }
  }

  Future<void> markAsSeen({
    required String codPrestador,
    required String notificationId,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
        query ReadFCMNotifications {
          cooperadoReadFCMNotification(
            codPrestador: "$codPrestador"
            notificationId: "$notificationId"
          ) {
            message
          }
        }
      ''';
      logger.e('markAsSeen: $query');
      final QueryOptions options = QueryOptions(
        document: gql(query),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('Error markAsSeen: ${result.exception}');
        throw UnimedException(MessageException.general);
      } else {
        logger.e('markAsSeen sucess with notificationId: $notificationId');
      }
    } on UnimedException catch (ex) {
      logger.e('markAsSeen ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('markAsSeen exception : $ex');
      throw UnimedException(MessageException.general);
    }
  }

  Future<void> togglePinNotificationStatus({
    required String codPrestador,
    required String notificationId,
    required bool pinNotification,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String? query;

      if (pinNotification) {
        query = '''
        query PinFCMNotifications {
          cooperadoPinFCMNotification(
            codPrestador: "$codPrestador"
            notificationId: "$notificationId"
          ) {
            message
          }
        }
      ''';
      } else {
        query = '''
        query UnpinFCMNotifications {
          cooperadoUnpinFCMNotification(
            codPrestador: "$codPrestador"
            notificationId: "$notificationId"
          ) {
            message
          }
        }
      ''';
      }

      logger.e('togglePinNotificationStatus: $query');
      final QueryOptions options = QueryOptions(
        document: gql(query),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('Error togglePinNotificationStatus: ${result.exception}');
        throw UnimedException(MessageException.general);
      } else {
        logger.e('togglePinNotificationStatus sucess with notificationId: $notificationId');
      }
    } on UnimedException catch (ex) {
      logger.e('togglePinNotificationStatus ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('togglePinNotificationStatus exception : $ex');
      throw UnimedException(MessageException.general);
    }
  }

  Future<void> deleteNotification({
    required String codPrestador,
    required String notificationId,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
        query DeleteFCMNotifications {
          cooperadoDeleteFCMNotification(
            codPrestador: "$codPrestador"
            notificationId: "$notificationId"
          ) {
            message
          }
        }
      ''';
      logger.e('deleteNotification: $query');
      final QueryOptions options = QueryOptions(
        document: gql(query),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('Error deleteNotification: ${result.exception}');
        throw UnimedException(MessageException.general);
      } else {
        logger.e('deleteNotification sucess with notificationId: $notificationId');
      }
    } on UnimedException catch (ex) {
      logger.e('deleteNotification ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('deleteNotification exception : $ex');
      throw UnimedException(MessageException.general);
    }
  }
}

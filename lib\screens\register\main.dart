import 'package:cooperado_minha_unimed/bloc/auth/register/register_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/utils/validators.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:cooperado_minha_unimed/shared/widgets/item_form.dart';
import 'package:cooperado_minha_unimed/shared/widgets/snack.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:header_login/header_login.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:url_launcher/url_launcher.dart';

const String clientId = String.fromEnvironment('CLIENT_ID');

class RegisterScreen extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const RegisterScreen(
      {super.key, required this.analytics, required this.observer});

  @override
  RegisterScreenState createState() => RegisterScreenState();
}

class RegisterScreenState extends State<RegisterScreen> {
  final dateFormatter = MaskTextInputFormatter(
      mask: '##/##/####', filter: {"#": RegExp(r'[0-9]')});
  final cpfFormatter = MaskTextInputFormatter(
      mask: '###.###.###-##', filter: {"#": RegExp(r'[0-9]')});

  TextEditingController nameController = TextEditingController();
  TextEditingController crmController = TextEditingController();
  TextEditingController cpfController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController birthController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();

  bool acceptTerm = false;
  final bool _isLocalAuth = false;
  final _formKey = GlobalKey<FormState>();

  late FocusNode _nameFocus;
  late FocusNode _crmFocus;
  late FocusNode _cpfFocus;
  late FocusNode _birthFocus;
  late FocusNode _emailFocus;
  late FocusNode _passwordFocus;
  late FocusNode _confirmPasswordFocus;

  @override
  void initState() {
    super.initState();

    _nameFocus = FocusNode();
    _crmFocus = FocusNode();
    _cpfFocus = FocusNode();
    _birthFocus = FocusNode();
    _emailFocus = FocusNode();
    _passwordFocus = FocusNode();
    _confirmPasswordFocus = FocusNode();

    widget.analytics.logScreenView(
      screenName: 'Cadastro (médico)',
      screenClass: 'RegisterScreen',
    );
  }

  @override
  void dispose() {
    _nameFocus.dispose();
    _crmFocus.dispose();
    _cpfFocus.dispose();
    _birthFocus.dispose();
    _emailFocus.dispose();
    _passwordFocus.dispose();
    confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CooperadoColors.grayLight,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0.0,
      ),
      body: SafeArea(
        top: false,
        child: Column(
          children: [
            HeaderLogin(
              clientId: clientId,
              innerCircleColor: CooperadoColors.tealGreen,
              outerCircleColor: CooperadoColors.tealGreenSecondary,
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20.0).copyWith(top: 0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[_form()],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _form() {
    return Form(
      key: _formKey,
      child: ListView(
        padding: EdgeInsets.zero,
        physics: const PageScrollPhysics(),
        shrinkWrap: true,
        children: [
          ItemForm(
            title: "Nome",
            validator: (value) => TextFieldValidators.requiredField(value!),
            controller: nameController,
            type: TextInputType.name,
            focusNode: _nameFocus,
            next: true,
          ),
          ItemForm(
            title: "CRM",
            validator: (value) => TextFieldValidators.crm(value!),
            controller: crmController,
            type: TextInputType.number,
            focusNode: _crmFocus,
            next: true,
          ),
          ItemForm(
            title: "CPF",
            validator: (value) => TextFieldValidators.cpf(value!),
            controller: cpfController,
            formatters: [cpfFormatter],
            focusNode: _cpfFocus,
            type: TextInputType.number,
            next: true,
          ),
          ItemForm(
            title: "Data de nascimento",
            formatters: [dateFormatter],
            type: TextInputType.number,
            validator: (value) => TextFieldValidators.validateDate(value!),
            controller: birthController,
            focusNode: _birthFocus,
            next: true,
          ),
          ItemForm(
            title: "E-mail",
            validator: (value) => TextFieldValidators.email(value!),
            controller: emailController,
            focusNode: _emailFocus,
            next: true,
          ),
          ItemForm(
            title: "Senha",
            isPassword: true,
            validator: (value) {
              if (!_isLocalAuth && (value == null || value.isEmpty)) {
                return '';
              }
              return null;
            },
            controller: passwordController,
            focusNode: _passwordFocus,
            next: true,
            maxLength: 100,
          ),
          ItemForm(
            title: "Confirmar senha",
            isPassword: true,
            validator: (value) {
              if (!_isLocalAuth && (value == null || value.isEmpty)) {
                return '';
              }
              return null;
            },
            controller: confirmPasswordController,
            focusNode: _confirmPasswordFocus,
            next: false,
            submit: _submit,
            maxLength: 100,
          ),
          Row(
            children: [
              Checkbox(
                value: acceptTerm,
                onChanged: (bool? value) {
                  setState(() {
                    acceptTerm = value!;
                  });
                },
              ),
              Row(
                children: [
                  const Text(
                    'Li e concordo com o ',
                    style: TextStyle(fontSize: 12),
                  ),
                  InkWell(
                    onTap: _launchURL,
                    child: const Text(
                      "termo de aceitação",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          if (!acceptTerm)
            const Padding(
              padding: EdgeInsets.only(left: 8.0, bottom: 8.0),
              child: Text(
                'Campo obrigatório',
                style: TextStyle(color: Colors.red, fontSize: 12),
              ),
            ),
          _buttonConfirm()
        ],
      ),
    );
  }

  _launchURL() async {
    final url = Uri.parse(
        "https://www.unimedfortaleza.com.br/resources/arquivo/termo-de-aceitacao-cadastro-portal.pdf");
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      throw 'Unable to open url : $url';
    }
  }

  Widget _buttonConfirm() {
    return BlocConsumer<RegisterCubit, RegisterState>(
      listener: (context, state) {
        if (state is ErrorRegisterState) {
          showDialog(
            context: context,
            builder: (context) => CooperadoAlertDialog(
              textWidget: Text(
                state.message,
                textAlign: TextAlign.center,
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          );
        } else if (state is DoneRegisterState) {
          showDialog(
            context: context,
            builder: (context) => CooperadoAlertDialog(
              textWidget: const Text(
                "Cadastro realizado com sucesso!",
                textAlign: TextAlign.center,
              ),
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop();
              },
            ),
          );
        }
      },
      builder: (context, state) {
        if (state is LoadingRegisterState) {
          return const SpinKitThreeBounce(
            color: CooperadoColors.tealGreen,
          );
        }
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: CooperadoColors.tealGreen,
              padding: const EdgeInsets.symmetric(vertical: 16.0),
            ),
            onPressed: () => _submit(),
            child: const Text("CADASTRAR"),
          ),
        );
      },
    );
  }

  void _submit() {
    FocusScope.of(context).unfocus();
    if (_formKey.currentState != null) {
      if (_formKey.currentState!.validate()) {
        if (!acceptTerm) {
          ScaffoldMessenger.of(context).showSnackBar(
            Snack.warning(
              'Aceite os termos para concluir o cadastro',
              duration: const Duration(seconds: 1),
            ),
          );
        } else {
          context.read<RegisterCubit>().sendRegister(
                name: nameController.text,
                birthDate: birthController.text.replaceAll('/', '-'),
                crm: crmController.text,
                cpf: cpfController.text.replaceAll(" ", ""),
                confirmPassword: confirmPasswordController.text,
                password: passwordController.text,
                email: emailController.text,
                terms: acceptTerm,
              );
        }
      }
    }
  }
}

// ignore_for_file: sized_box_for_whitespace

import 'package:cooperado_minha_unimed/models/line_chart_value.model.dart';
import 'package:cooperado_minha_unimed/models/res-internal/res_indicator_data.model.dart';
import 'package:cooperado_minha_unimed/screens/res/indicators/widgets/res_line_chart.dart';
import 'package:flutter/material.dart';

class CardIndicatorData extends StatefulWidget {
  final IndicatorDataModel indicatorData;
  final String description;

  const CardIndicatorData(
      {super.key, required this.indicatorData, required this.description});

  @override
  CardIndicatorDataState createState() => CardIndicatorDataState();
}

class CardIndicatorDataState extends State<CardIndicatorData> {
  List<LineChartValues> data = [];

  @override
  void initState() {
    super.initState();

    _createChartValues();
  }

  @override
  Widget build(BuildContext context) {
    return widget.indicatorData.indicatorsData.isEmpty
        ? Container()
        : Card(
            child: SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              scrollDirection: Axis.horizontal,
              child: Container(
                width: widget.indicatorData.indicatorsData.length * 120.0,
                height: 250,
                child: ResLineGraphHistory(
                  data: data,
                  unity: widget.indicatorData.unity,
                  description:
                      '${widget.description} (${widget.indicatorData.unity})',
                ),
              ),
            ),
          );
  }

  _createChartValues() {
    int index = 0;
    data = [];
    for (IndicatorsData indicatorData
        in widget.indicatorData.indicatorsDataSorted) {
      data.add(LineChartValues(
          indicatorData.dateFormatted, double.parse(indicatorData.y), index));
      index++;
    }
  }
}

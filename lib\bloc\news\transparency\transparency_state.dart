part of 'transparency_cubit.dart';

abstract class TransparencyState extends Equatable {
  const TransparencyState();

  @override
  List<Object?> get props => [];
}

class TransparencyInitial extends TransparencyState {}

class LoadingTransparencyNewsStatePagination extends TransparencyState {
  final List<Noticia> list;
  @override
  List<Object> get props => [list];
  const LoadingTransparencyNewsStatePagination({required this.list});
}

class LoadingTransparencyNewsState extends TransparencyState {
  @override
  List<Object> get props => [];
}

class ErrorTransparencyNewsState extends TransparencyState {
  final String message;
  @override
  List<Object> get props => [message];
  const ErrorTransparencyNewsState(this.message);
}

class DoneTransparencyNewsState extends TransparencyState {
  final List<Noticia> list;
  @override
  List<Object> get props => [list];

  const DoneTransparencyNewsState({required this.list});
}

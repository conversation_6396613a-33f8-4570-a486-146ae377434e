class GeneralConfigModel {
  late Home home;
  List<Messages>? messages;
  Links? links;
  Permissions? permissions;

  GeneralConfigModel(
      {required this.home, this.messages, this.links, this.permissions});

  GeneralConfigModel.fromJson(Map<String, dynamic> json) {
    home = Home.fromJson(json['home']);
    if (json['messages'] != null) {
      messages = <Messages>[];
      json['messages'].forEach((v) {
        messages!.add(Messages.fromJson(v));
      });
    }
    links = json['links'] != null ? Links.fromJson(json['links']) : null;
    permissions = json['permissions'] != null
        ? Permissions.fromJson(json['permissions'])
        : Permissions();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['home'] = home.toJson();
    if (messages != null) {
      data['messages'] = messages!.map((v) => v.toJson()).toList();
    }
    if (links != null) {
      data['links'] = links!.toJson();
    }
    if (permissions != null) {
      data['permissions'] = permissions!.toJson();
    }
    return data;
  }
}

class Home {
  late Buttons buttons;
  late Boxes boxes;
  late Profile profile;

  Home({required this.buttons, required this.boxes, required this.profile});

  Home.fromJson(Map<String, dynamic> json) {
    buttons = Buttons.fromJson(json['buttons']);
    boxes = Boxes.fromJson(json['boxes']);
    profile = Profile.fromJson(json['profile']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['buttons'] = buttons.toJson();
    data['boxes'] = boxes.toJson();
    data['profile'] = profile.toJson();
    return data;
  }
}

class Buttons {
  late bool onlineConsultation;
  late bool myIndicators;
  late bool medicalProduction;
  late bool services;
  late bool transparency;
  late bool terms;
  late bool guide;
  late bool direction;
  late bool fiscalCouncil;
  late bool news;
  late bool consultometer;
  late bool personalAssistant;
  late bool economicIndicators;
  late bool financial;
  late bool benefits;
  late bool clinicalJourney;
  late bool officeNotices;
  late bool insurance;
  late bool supportCooperative;
  late bool clubMaisVantagens;
  late bool financialServiceUniverse;
  late Res res;
  late bool ecard;
  Buttons({
    required this.onlineConsultation,
    required this.myIndicators,
    required this.medicalProduction,
    required this.services,
    required this.transparency,
    required this.terms,
    required this.guide,
    required this.direction,
    required this.fiscalCouncil,
    required this.news,
    required this.consultometer,
    required this.personalAssistant,
    required this.economicIndicators,
    required this.financial,
    required this.benefits,
    required this.clinicalJourney,
    required this.officeNotices,
    required this.insurance,
    required this.supportCooperative,
    required this.clubMaisVantagens,
    required this.financialServiceUniverse,
    required this.res,
    required this.ecard,
  });

  Buttons.fromJson(Map<String, dynamic> json) {
    onlineConsultation = json['onlineConsultation'] ?? false;
    myIndicators = json['myIndicators'] ?? false;
    medicalProduction = json['medicalProduction'] ?? false;
    services = json['services'] ?? false;
    transparency = json['transparency'] ?? false;
    terms = json['terms'] ?? false;
    guide = json['guide'] ?? false;
    direction = json['direction'] ?? false;
    fiscalCouncil = json['fiscalCouncil'] ?? false;
    news = json['news'] ?? false;
    consultometer = json['consultometer'] ?? false;
    personalAssistant = json['personalAssistant'] ?? false;
    economicIndicators = json['economicIndicators'] ?? false;
    financial = json['financial'] ?? false;
    benefits = json['benefits'] ?? false;
    clinicalJourney = json['clinicalJourney'] ?? false;
    officeNotices = json['officeNotices'] ?? false;
    insurance = json['insurance'] ?? false;
    supportCooperative = json['supportCooperative'] ?? false;
    clubMaisVantagens = json['clubMaisVantagens'] ?? false;
    financialServiceUniverse = json['financialServiceUniverse'] ?? false;
    res = json['res'] != null
        ? Res.fromJson(json['res'])
        : Res(resExternal: ResExternal(), resInternal: false);
    ecard = json['ecard'] ?? false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['onlineConsultation'] = onlineConsultation;
    data['myIndicators'] = myIndicators;
    data['medicalProduction'] = medicalProduction;
    data['services'] = services;
    data['transparency'] = transparency;
    data['terms'] = terms;
    data['guide'] = guide;
    data['direction'] = direction;
    data['fiscalCouncil'] = fiscalCouncil;
    data['news'] = news;
    data['consultometer'] = consultometer;
    data['personalAssistant'] = personalAssistant;
    data['economicIndicators'] = economicIndicators;
    data['financial'] = financial;
    data['clinicalJourney'] = clinicalJourney;
    data['officeNotices'] = officeNotices;
    data['insurance'] = insurance;
    data['supportCooperative'] = supportCooperative;
    data['clubMaisVantagens'] = clubMaisVantagens;
    data['financialServiceUniverse'] = financialServiceUniverse;
    data['res'] = res.toJson();
    data['ecard'] = ecard;
    return data;
  }

  bool thereIsAnInvisibleButton() => (onlineConsultation ||
      myIndicators ||
      medicalProduction ||
      services ||
      transparency ||
      terms ||
      guide ||
      direction ||
      fiscalCouncil ||
      news ||
      consultometer ||
      personalAssistant);

  bool buttonListExpandedButtonVisibility() {
    int contador = 0;
    if (onlineConsultation) contador++;
    if (myIndicators) contador++;
    if (medicalProduction) contador++;
    if (services) contador++;
    if (transparency) contador++;
    if (terms) contador++;
    if (guide) contador++;
    if (direction) contador++;
    if (fiscalCouncil) contador++;
    if (news) contador++;
    if (consultometer) contador++;
    if (personalAssistant) contador++;

    return contador > 3;
  }
}

class Res {
  late bool resInternal;
  late ResExternal resExternal;

  Res({
    required this.resInternal,
    required this.resExternal,
  });

  Res.fromJson(Map<String, dynamic> json) {
    resInternal = json['resInternal'] ?? false;
    resExternal = ResExternal.fromJson(json['resExternal']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    data['resInternal'] = resInternal;
    data['resExternal'] = resExternal.toJson();

    return data;
  }
}

class ResExternal {
  late bool resExternal;
  late bool resExternalService;
  late bool resExternalAllergy;
  late bool resExternalProcedure;
  late bool resExternalDocument;
  late bool resExternalAlerts;
  late bool resExternalDiagnostic;
  late bool resExternalResultExam;

  ResExternal({
    this.resExternal = false,
    this.resExternalService = false,
    this.resExternalAllergy = false,
    this.resExternalProcedure = false,
    this.resExternalDocument = false,
    this.resExternalAlerts = false,
    this.resExternalDiagnostic = false,
    this.resExternalResultExam = false,
  });

  ResExternal.fromJson(Map<String, dynamic> json) {
    resExternal = json['resExternal'] ?? false;
    resExternalService = json['resExternalService'] ?? false;
    resExternalAllergy = json['resExternalAllergy'] ?? false;
    resExternalProcedure = json['resExternalProcedure'] ?? false;
    resExternalDocument = json['resExternalDocument'] ?? false;
    resExternalAlerts = json['resExternalAlerts'] ?? false;
    resExternalDiagnostic = json['resExternalDiagnostic'] ?? false;
    resExternalResultExam = json['resExternalResultExam'] ?? false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    data['resExternal'] = resExternal;
    data['resExternalService'] = resExternalService;
    data['resExternalAllergy'] = resExternalAllergy;
    data['resExternalProcedure'] = resExternalProcedure;
    data['resExternalDocument'] = resExternalDocument;
    data['resExternalAlerts'] = resExternalAlerts;
    data['resExternalDiagnostic'] = resExternalDiagnostic;
    data['resExternalResultExam'] = resExternalResultExam;

    return data;
  }
}

class Boxes {
  late bool medicalProductionReport;
  late bool productionComparison;
  late bool cooperative;
  late bool news;
  late bool unimedIndicators;
  late bool feeMonitoring;

  Boxes(
      {required this.medicalProductionReport,
      required this.productionComparison,
      required this.cooperative,
      required this.news,
      required this.unimedIndicators,
      required this.feeMonitoring});

  Boxes.fromJson(Map<String, dynamic> json) {
    medicalProductionReport = json['medicalProductionReport'];
    productionComparison = json['productionComparison'];
    cooperative = json['cooperative'];
    news = json['news'];
    unimedIndicators = json['unimedIndicators'];
    feeMonitoring = json['feeMonitoring'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['medicalProductionReport'] = medicalProductionReport;
    data['productionComparison'] = productionComparison;
    data['cooperative'] = cooperative;
    data['news'] = news;
    data['unimedIndicators'] = unimedIndicators;
    data['feeMonitoring'] = feeMonitoring;
    return data;
  }
}

class Profile {
  late bool myData;

  Profile({required this.myData});

  Profile.fromJson(Map<String, dynamic> json) {
    myData = json['myData'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['myData'] = myData;
    return data;
  }
}

class Messages {
  String? id;
  String? message;

  Messages({this.id, this.message});

  Messages.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['message'] = message;
    return data;
  }
}

class Links {
  String? plataformaMaisUnimed;
  String? coopMais;
  String? privacyPolitics;
  String? channelEthics;
  String? clubeMaisVantagens;
  String? plataformaSeguros;
  String? apoioCooperado;
  String? financialServiceLink;
  String? regulamentoClubeMaisVantagens;
  String? antecipa;

  Links(
      {this.plataformaMaisUnimed,
      this.coopMais,
      this.clubeMaisVantagens,
      this.apoioCooperado});

  Links.fromJson(Map<String, dynamic> json) {
    plataformaMaisUnimed = json['plataformaMaisUnimed'];
    coopMais = json['coopMais'];
    privacyPolitics = json['privacyPolitics'];
    channelEthics = json['channelEthics'];
    clubeMaisVantagens = json['clubeMaisVantagens'];
    plataformaSeguros = json['plataformaSeguros'];
    apoioCooperado = json['apoioCooperado'];
    financialServiceLink = json['financialServiceLink'];
    regulamentoClubeMaisVantagens = json['regulamentoClubeMaisVantagens'];
    antecipa = json['antecipa'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['plataformaMaisUnimed'] = plataformaMaisUnimed;
    data['coopMais'] = coopMais;
    data['privacyPolitics'] = privacyPolitics;
    data['channelEthics'] = channelEthics;
    data['clubeMaisVantagens'] = clubeMaisVantagens;
    data['plataformaSeguros'] = plataformaSeguros;
    data['apoioCooperado'] = apoioCooperado;
    data['regulamentoClubeMaisVantagens'] = regulamentoClubeMaisVantagens;
    data['financialServiceLink'] = financialServiceLink;
    data['antecipa'] = antecipa;
    return data;
  }
}

class Permissions {
  late bool useUrlLauncher;

  Permissions({this.useUrlLauncher = false});

  Permissions.fromJson(Map<String, dynamic> json) {
    useUrlLauncher = json['useUrlLauncher'] ?? false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['useUrlLauncher'] = useUrlLauncher;
    return data;
  }
}

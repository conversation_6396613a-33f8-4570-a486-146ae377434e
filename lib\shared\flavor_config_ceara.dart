import 'package:biometria_perfilapps/main.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:evaluation/evaluation.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';

const codUnimedCeara = 979;

class FlavorCearaTEST extends FlavorValues {
  FlavorCearaTEST()
      : super(
          remoteLogEnv: RemoteLogEnv.TEST,
          oneSignalId: "************************************",
          configuracoes: ConfiguracoesData(
            url: "https://apiconfig.unimedfortaleza.com.br",
            token:
                "3256dde2ae32021afa7b9f96fdea200bd405ca5b3ba69a237e3dc81e208a80897acc59ca7f3a557d39b5013e928670f8",
          ),
          portal: Portal(
              url:
                  'https://portalhmg.unimedfortaleza.com.br/portal_homologacao/api/v1/cooperado/'),
          portalV2: Portal(
              url: 'https://portalhmg.unimedfortaleza.com.br/portal/api/'),
          guia: Guia(url: 'http://s01lnx106.unimedfortaleza.com.br:4202/login'),
          consultometro: Consultometro(
              url: 'http://s01lnx106.unimedfortaleza.com.br:4204/login'),
          consultorioOnline: ConsultorioOnline(
              url:
                  'https://consultorioonlinehmg.unimedfortaleza.com.br/prestador/login'),
          profilePermissions: DefaultCredencial(
            url: 'https://perfilappshmg.unimedfortaleza.com.br/api/',
            user: '<EMAIL>',
            password: 'ZfZULfm72dbhE95ssbbfnWPMtdeJ7By6dd2TVtusMd4eKZ9abh',
          ),
          graphql: DefaultCredencial(
            url: 'https://perfilappshmg.unimedfortaleza.com.br/graphql',
            user: '<EMAIL>',
            password: 'ZfZULfm72dbhE95ssbbfnWPMtdeJ7By6dd2TVtusMd4eKZ9abh',
          ),
          servicos: Servicos(
              url:
                  'https://apiservicoshmg.unimedfortaleza.com.br/servicos-unimed/api/',
              user: 'APP_COOPERADO',
              password: 'APP5muuz8xq'),
          relatorioReport: PortalRelatorioReport(
              url:
                  'http://portalhmg.unimedfortaleza.com.br/portal_homologacao/'),
          personalAssistant: PersonalAssistantApi(
              url: "https://apipersonaldigitalhmg.unimedfortaleza.com.br/"),
          evaluationEnv: EvaluationEnv.TEST,
          codUnimed: codUnimedCeara,
          lostPasswordPortal: LostPasswordPortal(
              user: '40bs242a0ti9nkcmy9y3iiyxcvfux4vc', password: '123456'),
          validadeBioIdEnv: BiometryUnimedEnv.test,
        );
}

class FlavorCearaDEV extends FlavorValues {
  FlavorCearaDEV()
      : super(
          remoteLogEnv: RemoteLogEnv.DEV,
          oneSignalId: "************************************",
          configuracoes: ConfiguracoesData(
            url: "https://apiconfig.unimedfortaleza.com.br",
            token:
                "3256dde2ae32021afa7b9f96fdea200bd405ca5b3ba69a237e3dc81e208a80897acc59ca7f3a557d39b5013e928670f8",
          ),
          portal: Portal(
            url:
                'https://portalhmg.unimedfortaleza.com.br/portal_homologacao/api/v1/cooperado/',
          ),
          portalV2: Portal(
              url: 'https://portalhmg.unimedfortaleza.com.br/portal/api/'),
          guia: Guia(
            url: 'http://s01lnx106.unimedfortaleza.com.br:4202/login',
          ),
          consultometro: Consultometro(
            url:
                'https://consultometrohmg.unimedfortaleza.com.br/prestador/login',
          ),
          consultorioOnline: ConsultorioOnline(
            url:
                'https://consultorioonlinehmg.unimedfortaleza.com.br/prestador/login',
          ),
          servicos: Servicos(
            url:
                'https://apiservicoshmg.unimedfortaleza.com.br/servicos-unimed/api/',
            user: 'APP_COOPERADO',
            password: 'APP5muuz8xq',
          ),
          profilePermissions: DefaultCredencial(
            url: 'https://perfilappshmg.unimedfortaleza.com.br/api/',
            user: '<EMAIL>',
            password: 'ZfZULfm72dbhE95ssbbfnWPMtdeJ7By6dd2TVtusMd4eKZ9abh',
          ),
          graphql: DefaultCredencial(
            url: 'https://perfilappshmg.unimedfortaleza.com.br/graphql',
            user: '<EMAIL>',
            password: 'ZfZULfm72dbhE95ssbbfnWPMtdeJ7By6dd2TVtusMd4eKZ9abh',
          ),
          relatorioReport: PortalRelatorioReport(
            url: 'http://portalhmg.unimedfortaleza.com.br/portal_homologacao/',
          ),
          personalAssistant: PersonalAssistantApi(
              url: "https://apipersonaldigitaldev.unimedfortaleza.com.br/"),
          evaluationEnv: EvaluationEnv.DEV,
          codUnimed: codUnimedCeara,
          lostPasswordPortal: LostPasswordPortal(
              user: '40bs242a0ti9nkcmy9y3iiyxcvfux4vc', password: '123456'),
          validadeBioIdEnv: BiometryUnimedEnv.dev,
        );
}

class FlavorCearaPROD extends FlavorValues {
  FlavorCearaPROD()
      : super(
          remoteLogEnv: RemoteLogEnv.PROD,
          oneSignalId: "************************************",
          configuracoes: ConfiguracoesData(
            url: "https://apiconfig.unimedfortaleza.com.br",
            token:
                "3256dde2ae32021afa7b9f96fdea200bd405ca5b3ba69a237e3dc81e208a80897acc59ca7f3a557d39b5013e928670f8",
          ),
          portal: Portal(
            url: 'https://www.unimedfortaleza.com.br/api/v1/cooperado/',
          ),
          portalV2: Portal(url: 'https://www.unimedfortaleza.com.br/api/'),
          guia: Guia(
            url: 'https://renumeracao-variavel.unimedfortaleza.com.br/login',
          ),
          consultometro: Consultometro(
            url: 'https://consultometro.unimedfortaleza.com.br/login',
          ),
          consultorioOnline: ConsultorioOnline(
              url:
                  'https://consultorio-online.unimedfortaleza.com.br/prestador/login'),
          profilePermissions: DefaultCredencial(
            url: 'https://perfilapps.unimedfortaleza.com.br/api/',
            user: '<EMAIL>',
            password: 'NGyElE5m6N7wZhDXP7ZmW216LhZtwxpkUXlQ5TJ4r1A3hBGCLA',
          ),
          graphql: DefaultCredencial(
            url: 'https://perfilapps.unimedfortaleza.com.br/graphql',
            user: '<EMAIL>',
            password: 'NGyElE5m6N7wZhDXP7ZmW216LhZtwxpkUXlQ5TJ4r1A3hBGCLA',
          ),
          servicos: Servicos(
            url:
                'https://apiservicos.unimedfortaleza.com.br/servicos-unimed/api/',
            user: 'APP_COOPERADO',
            password: 'APP5muuz8xq',
          ),
          relatorioReport: PortalRelatorioReport(
            url: 'https://www.unimedfortaleza.com.br/',
          ),
          personalAssistant: PersonalAssistantApi(
              url: "https://apipersonaldigital.unimedfortaleza.com.br/"),
          evaluationEnv: EvaluationEnv.PROD,
          codUnimed: codUnimedCeara,
          lostPasswordPortal: LostPasswordPortal(
            user: 'k39es8kz9t86acqbwwxnhz4mw4akw07u',
            password: '4JUNwv0dxc3nYYCN5papaFTbkrtDsnsW',
          ),
          validadeBioIdEnv: BiometryUnimedEnv.prod,
        );
}

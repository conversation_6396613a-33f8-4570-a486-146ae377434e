import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';

enum InfoRowOrientation { vertical, horizontal }

class InfoRow extends StatelessWidget {
  final String label;
  final String value;
  final InfoRowOrientation orientation;
  final bool selectable;

  const InfoRow({
    super.key,
    required this.label,
    required this.value,
    this.orientation = InfoRowOrientation.horizontal,
    this.selectable = false,
  });

  @override
  Widget build(BuildContext context) {
    return orientation == InfoRowOrientation.horizontal
        ? Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '$label :',
                style: const TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: selectable
                    ? SelectableText(
                        value.isNotEmpty ? value : '-',
                        style: const TextStyle(
                          color: CooperadoColors.blackText,
                        ),
                      )
                    : Text(
                        value.isNotEmpty ? value : '-',
                        style: const TextStyle(
                          color: CooperadoColors.blackText,
                        ),
                      ),
              ),
            ],
          )
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.bold,
                ),
              ),
              selectable
                  ? SelectableText(
                      value,
                    )
                  : Text(
                      value,
                    ),
            ],
          );
  }
}

import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/news/read_news.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/demostrativo_resultado.dart';
import 'package:flutter/material.dart';

class CardOtherNews extends StatefulWidget {
  final Noticia noticiasPortal;
  const CardOtherNews({super.key, required this.noticiasPortal});
  @override
  CardOtherNewsState createState() => CardOtherNewsState();
}

class CardOtherNewsState extends State<CardOtherNews> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          FadeRoute(
            page: ReadNews(
              noticiasPortal: widget.noticiasPortal,
            ),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              widget.noticiasPortal.dataFormatted,
              style: const TextStyle(color: CooperadoColors.grayLight2),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              widget.noticiasPortal.titulo ?? "Sem titulo",
              style: const TextStyle(
                  color: CooperadoColors.tealGreen, fontSize: 16),
            ),
            const Align(
                alignment: Alignment.bottomRight,
                child: Text(
                  "Leia mais",
                  style: TextStyle(color: CooperadoColors.tealGreen),
                )),
            const Divider()
          ],
        ),
      ),
    );
  }
}

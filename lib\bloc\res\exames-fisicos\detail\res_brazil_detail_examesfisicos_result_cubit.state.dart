import 'package:cooperado_minha_unimed/bloc/res/exames-fisicos/detail/res_brazil_detail_examesfisicos_result_cubit.dart';
import 'package:cooperado_minha_unimed/models/res/res_brasil_detail_exam_phisical_model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResBrazilDetailExamesfisicosResultCubit
    extends Cubit<ResBrazilDetailExamesFisicosResultState> {
  ResBrazilDetailExamesfisicosResultCubit()
      : super(InitialResBrazilDetailExamesFisicosResultState());

  List<ResDetailExameFisicoModel>? _resExameFisicoDetailModel;
  List<ResDetailExameFisicoModel>?
      get resBrazilDetailExamesFisicosResultModel => _resExameFisicoDetailModel;

  void getExamesFisicosDetail({
    required String crm,
    required String card,
    required String code,
  }) async {
    try {
      emit(LoadingResBrazilDetailExamesFisicosResultState());

      _resExameFisicoDetailModel = await Locator.instance!<ResGraphQlApi>()
          .resExamesFisicosDetail(crm: crm, card: card, code: code);

      if (_resExameFisicoDetailModel!.isEmpty) {
        emit(NoDataResBrazilDetailExamesFisicosResultState());
      } else {
        emit(LoadedResBrazilDetailExamesFisicosResultState(
          detailExamesFisicos: _resExameFisicoDetailModel!,
        ));
      }
    } catch (e) {
      emit(ErrorResBrazilDetailExamesFisicosResultState(message: e.toString()));
    }
  }
}

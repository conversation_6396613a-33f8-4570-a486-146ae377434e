part of 'office_notices_cubit.dart';

abstract class OfficeNoticesState extends Equatable {
  const OfficeNoticesState();
}

class OfficeNoticesInitial extends OfficeNoticesState {
  @override
  List<Object> get props => [];
}

class LoadingOfficeNoticesState extends OfficeNoticesState {
  @override
  List<Object?> get props => [];
}

class LoadedListOfficeNoticesState extends OfficeNoticesState {
  final List<OfficeNoticeModel> officeNotices;

  @override
  List<Object?> get props => [];

  const LoadedListOfficeNoticesState({required this.officeNotices});
}

class EmptyOfficeNoticesState extends OfficeNoticesState {
  @override
  List<Object?> get props => [];
}

class DoneReadNotificationState extends OfficeNoticesState {
  @override
  List<Object?> get props => [];
}

class DoneHideNotificationState extends OfficeNoticesState {
  @override
  List<Object?> get props => [];
}

class ErrorOfficeNoticesState extends OfficeNoticesState {
  final String message;

  @override
  List<Object> get props => [];

  const ErrorOfficeNoticesState(this.message);
}

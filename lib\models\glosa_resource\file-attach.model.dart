import 'dart:io';
import 'package:path/path.dart';

class FileAttach {
  String name;
  File file;
  File? thumbnail;
  bool? sended;

  void setFile(File file, File? thumbnail) {
    this.file = file;
    this.thumbnail = thumbnail;
    name = basename(this.file.path);
  }

  FileAttach({
    required this.name,
    required this.file,
    required this.thumbnail,
    this.sended,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data["name"] = name;
    data["file"] = file;
    data["thumbnail"] = thumbnail;

    return data;
  }
}

import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/indicators/last_production/last_production_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/glose_resource/add_glosa_resource_bloc.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/glose_resource/add_glosa_resource_state.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/glose_resource/glosa_resource_bloc.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/glosa_resource/glosa_resource.model.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/info_row.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/svg.dart';

class ButtonRequestResource extends StatefulWidget {
  final GlosaResourceData glosaResourceData;

  const ButtonRequestResource({super.key, required this.glosaResourceData});

  @override
  ButtonRequestResourceState createState() => ButtonRequestResourceState();
}

class ButtonRequestResourceState extends State<ButtonRequestResource> {
  bool showExtract = true;
  DateTime? selectedDateTime;
  bool filtersPanelOpened = false;
  FocusNode? focusTextFieldSearch;
  TextEditingController textEditingController = TextEditingController();
  bool loading = false;

  @override
  void initState() {
    selectedDateTime = context.read<LastProductionCubit>().lastDate;

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return TextButton(
      style: TextButton.styleFrom(
          backgroundColor: widget.glosaResourceData.resourced == true
              ? CooperadoColors.grayLight8
              : CooperadoColors.greenWhite,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5),
            side: BorderSide(
                color: widget.glosaResourceData.resourced == true
                    ? CooperadoColors.grayLight3
                    : CooperadoColors.greenDark5),
          )),
      onPressed: widget.glosaResourceData.resourced == true
          ? null
          : () {
              context.read<AddGlosaResourceCubit>().setToInitiaState();
              showJustificationModal(context);
            },
      child: Text(
          widget.glosaResourceData.resourced == true
              ? "Recurso solicitado"
              : "Solicitar recurso",
          style: TextStyle(
              color: widget.glosaResourceData.resourced == true
                  ? CooperadoColors.grayDark
                  : CooperadoColors.greenDark2,
              fontWeight: FontWeight.bold)),
    );
  }

  void showJustificationModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.0)),
      ),
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: StatefulBuilder(
              builder: (BuildContext context, StateSetter setState) {
                return Container(
                  height: MediaQuery.of(context).size.height * 0.7,
                  padding: const EdgeInsets.only(
                      right: 20.0, bottom: 28.0, top: 12.0, left: 20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Center(
                        child: Container(
                          height: 4,
                          width: 100,
                          margin: const EdgeInsets.symmetric(
                            vertical: 10,
                          ),
                          decoration: BoxDecoration(
                            color: CooperadoColors.grayLight3,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),
                      BlocConsumer<AddGlosaResourceCubit,
                          AddGlosaResourceState>(listener: (context, state) {
                        if (state is ErrorAddGlosaResourceState) {
                          Alert.open(
                            context,
                            title: 'Erro ao enviar recurso',
                            text: state.message,
                          );
                        } else if (state is DoneAddGlosaResourceState) {
                          context
                              .read<GlosaResourceCubit>()
                              .getGlosaResourceEvent(
                                  crm:
                                      context.read<AuthCubit>().credentials.crm,
                                  guide: widget.glosaResourceData.guiaReferencia
                                      .toString());
                        }
                        if (state is LoadingAddGlosaResourceState) {
                          setState(() {
                            loading = true;
                          });
                        } else {
                          setState(() {
                            loading = false;
                          });
                        }
                      }, builder: (context, state) {
                        if (state is DoneAddGlosaResourceState) {
                          return Padding(
                            padding: EdgeInsets.only(
                              top: MediaQuery.of(context).size.height * 0.1,
                            ),
                            child: Column(
                              children: [
                                Center(
                                  child: SvgPicture.asset(
                                    'assets/svg/icon_success.svg',
                                    width: 150,
                                  ),
                                ),
                                const SizedBox(
                                  height: 20,
                                ),
                                const Text(
                                  'Recurso enviado',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                const SizedBox(
                                  width: 150,
                                  child: Text(
                                      'Seu recurso foi enviado com sucesso',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: CooperadoColors.grayLight7,
                                      )),
                                ),
                              ],
                            ),
                          );
                        }
                        return Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                child: SingleChildScrollView(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Padding(
                                        padding: EdgeInsets.symmetric(
                                            vertical: 16.0),
                                        child: Text(
                                          'Solicitação de recursos',
                                          style: TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      InfoRow(
                                          label: 'Código',
                                          value: widget
                                              .glosaResourceData.servicoDv,
                                          orientation:
                                              InfoRowOrientation.vertical),
                                      const SizedBox(height: 8),
                                      if (widget.glosaResourceData
                                              .descricaoServico !=
                                          null)
                                        InfoRow(
                                          label: 'Descrição',
                                          value: widget.glosaResourceData
                                              .descricaoServico!,
                                          orientation:
                                              InfoRowOrientation.vertical,
                                        ),
                                      const SizedBox(height: 16),
                                      RichText(
                                        text: const TextSpan(
                                          children: [
                                            TextSpan(
                                              text:
                                                  'Justificativa da solicitação ',
                                              style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.black),
                                            ),
                                            TextSpan(
                                                text: '*',
                                                style: TextStyle(
                                                    color: CooperadoColors
                                                        .redCancel)),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(height: 5),
                                      TextFormField(
                                        maxLines: 5,
                                        maxLength: 500,
                                        validator: (value) => value!.isEmpty
                                            ? 'Campo obrigatório'
                                            : value.length < 10
                                                ? 'Justificativa deve ter no mínimo 10 caracteres'
                                                : null,
                                        autovalidateMode:
                                            AutovalidateMode.always,
                                        controller: textEditingController,
                                        decoration: InputDecoration(
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(8.0),
                                            borderSide: const BorderSide(
                                                color:
                                                    CooperadoColors.grayLight3),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(8.0),
                                            borderSide: const BorderSide(
                                                color:
                                                    CooperadoColors.greenDark2),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              const SizedBox(
                                height: 20,
                              ),
                              SizedBox(
                                width: double.infinity,
                                child: ElevatedButton(
                                  onPressed: loading
                                      ? null
                                      : () {
                                          if (textEditingController
                                                  .text.isNotEmpty &&
                                              textEditingController
                                                      .text.length >=
                                                  10) {
                                            context
                                                .read<AddGlosaResourceCubit>()
                                                .addGlosaResourceEvent(
                                                    crm: context
                                                        .read<AuthCubit>()
                                                        .credentials
                                                        .crm,
                                                    guide: widget
                                                        .glosaResourceData
                                                        .guiaReferencia
                                                        .toString(),
                                                    codService: widget
                                                        .glosaResourceData
                                                        .codServico,
                                                    justification:
                                                        textEditingController
                                                            .text,
                                                    numSohoev: (widget
                                                                .glosaResourceData
                                                                .numSohoev ??
                                                            "")
                                                        .toString());
                                          }
                                        },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: CooperadoColors.greenDark2,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0),
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 16.0),
                                  ),
                                  child: loading
                                      ? const SpinKitThreeBounce(
                                          color: Colors.white,
                                          size: 20,
                                        )
                                      : const Text('Enviar'),
                                ),
                              ),
                              const SizedBox(height: 10),
                              SizedBox(
                                width: double.infinity,
                                child: ElevatedButton(
                                  onPressed: () {
                                    Navigator.pop(context);
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0),
                                      side:
                                          const BorderSide(color: Colors.grey),
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 16.0),
                                  ),
                                  child: const Text(
                                    'Cancelar',
                                    style: TextStyle(color: Colors.black),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                    ],
                  ),
                );
              },
            ));
      },
    );
  }
}

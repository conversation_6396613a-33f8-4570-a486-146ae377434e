import 'package:cooperado_minha_unimed/models/diretoria.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late RetornoDiretoria? retornoDiretoria;
  late Map? json;

  setUpAll(
    () {
      retornoDiretoria = RetornoDiretoria(conselhos: [
        Conselhos(
          nome: "nomeConselho",
          membros: [
            Membros(
              nome: "nomeMenbro",
            ),
            Membros(
              nome: "nomeMenbro2",
            ),
          ],
        )
      ], diretores: [
        Diretores(
          nome: "nomeDiretor",
          cargo: "cargoDiretor",
          crm: "crmDiretor",
          especialidade: "especialidadeDiretor",
          imagem: "https://linkdaimagem.com",
          thumb: "thumbDiretor",
        )
      ]);

      json = {
        "diretores": [
          {
            "nome": "Marcos Aragão",
            "cargo": "Presidente",
            "crm": "",
            "especialidade": "",
            "imagem":
                "https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/02/marcos-aragao.png",
            "thumb":
                "https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/02/marcos-aragao-small.png"
          },
          {
            "nome": "Flávio Ibiapina",
            "cargo": "Diretor Administrativo Financeiro",
            "crm": "",
            "especialidade": "",
            "imagem":
                "https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/02/flavio.png",
            "thumb":
                "https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/02/flavio-small.png"
          },
          {
            "nome": "Fabrício Martins",
            "cargo": "Diretor Comercial",
            "crm": "",
            "especialidade": "",
            "imagem":
                "https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/02/fabricio.png",
            "thumb":
                "https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/02/fabricio-small.png"
          },
          {
            "nome": "Francisco Assis",
            "cargo": "Diretor de Provimentos em Saúde",
            "crm": "",
            "especialidade": "",
            "imagem":
                "https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/02/francisco-assis.png",
            "thumb":
                "https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/02/francisco-assis-small.png"
          },
          {
            "nome": "Fernanda Colares",
            "cargo": "Diretora de Recursos Próprios",
            "crm": "",
            "especialidade": "",
            "imagem":
                "https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/02/Fernanda.png",
            "thumb":
                "https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/02/Fernanda-small.png"
          }
        ],
        "conselhos": [
          {
            "nome": "Conselho Administrativo",
            "membros": [
              {"nome": "Adriano Accioly"},
              {"nome": "Gustavo Rego"},
              {"nome": "Hissa Tavares"},
              {"nome": "Vanda Belmino"}
            ]
          },
          {
            "nome": "Conselho Fiscal",
            "membros": [
              {"nome": "Anderson Costa (Coordenador)"},
              {"nome": "Rogean Nunes (Secretário)"},
              {"nome": "Daniel Mota Moura Fé (1º titular)"},
              {"nome": "Péricles de Lucena Feitosa"},
              {"nome": "Igor de Pinho Pessoa Xavier"},
              {"nome": "Rodrigo Vicentini"}
            ]
          },
          {
            "nome": "Conselho Técnico",
            "membros": [
              {"nome": "Aluilson Lima"},
              {"nome": "Eduardo Demes"},
              {"nome": "Fernando Medeiros"},
              {"nome": "Marcos Girão"},
              {"nome": "Marcus Rattacaso"},
              {"nome": "Patrícia Mesquita"}
            ]
          }
        ]
      };
    },
  );

  group(
    "isInstanceOf retornoDiretoria model tests",
    () {
      test("Should be return instance of retornoDiretoria", () {
        expect(retornoDiretoria, isInstanceOf<RetornoDiretoria>());
      });
      test("Should be return instance of Conselhos", () {
        expect(retornoDiretoria!.conselhos![0], isInstanceOf<Conselhos>());
      });

      test("Should be return instance of Membros", () {
        expect(retornoDiretoria!.conselhos![0].membros![0],
            isInstanceOf<Membros>());
      });

      test("Should be return instance of diretores", () {
        expect(retornoDiretoria!.diretores![0], isInstanceOf<Diretores>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of retornoDiretoria to json", () {
      expect(retornoDiretoria!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of retornoDiretoria from json", () {
      final json = retornoDiretoria!.toJson();
      expect(RetornoDiretoria.fromJson(json), isInstanceOf<RetornoDiretoria>());
    });

    test("Should be return instance of conselhos to json", () {
      expect(retornoDiretoria!.conselhos![0].toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of conselhos from json", () {
      final json = retornoDiretoria!.conselhos![0].toJson();
      expect(Conselhos.fromJson(json), isInstanceOf<Conselhos>());
    });
    test("Should be return instance of membros to json", () {
      expect(retornoDiretoria!.conselhos![0].membros![0].toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of membros from json", () {
      final json = retornoDiretoria!.conselhos![0].membros![0].toJson();
      expect(Membros.fromJson(json), isInstanceOf<Membros>());
    });
    test("Should be return instance of diretores to json", () {
      expect(retornoDiretoria!.diretores![0].toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of diretores from json", () {
      final json = retornoDiretoria!.diretores![0].toJson();
      expect(Diretores.fromJson(json), isInstanceOf<Diretores>());
    });
  });

  group("Json type", () {
    test("type", () {
      expect(json!["diretores"], isInstanceOf<List>());
      expect(json!["conselhos"], isInstanceOf<List>());
      expect(json!["diretores"][0]["nome"], isInstanceOf<String>());
      expect(json!["diretores"][0]["cargo"], isInstanceOf<String>());
      expect(json!["diretores"][0]["crm"], isInstanceOf<String>());
      expect(json!["diretores"][0]["especialidade"], isInstanceOf<String>());
      expect(json!["diretores"][0]["imagem"], isInstanceOf<String>());
      expect(json!["diretores"][0]["thumb"], isInstanceOf<String>());
      expect(json!["conselhos"][0]["nome"], isInstanceOf<String>());
      expect(json!["conselhos"][0]["membros"], isInstanceOf<List>());
      expect(
          json!["conselhos"][0]["membros"][0]["nome"], isInstanceOf<String>());
    });
  });

  group(
    "Other tests",
    () {
      test("Should be return true", () {
        expect(
            retornoDiretoria!.diretores![0].imagem
                .toString()
                .contains("https://"),
            true);
      });
    },
  );
}

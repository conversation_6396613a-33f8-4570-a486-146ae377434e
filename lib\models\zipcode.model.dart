import 'package:json_annotation/json_annotation.dart';

part 'zipcode.model.g.dart';

@JsonSerializable(anyMap: true)
class ZipCodeModel {
  @JsonKey(name: 'retorno')
  List<AddressZipCodeModel>? adressZipCodeModel;

  ZipCodeModel({this.adressZipCodeModel});

  factory ZipCodeModel.fromJson(Map json) => _$ZipCodeModelFromJson(json);
  Map<String, dynamic> toJson() => _$ZipCodeModelToJson(this);
}

@JsonSerializable(anyMap: true)
class AddressZipCodeModel {
  @JsonKey(name: 'cep')
  int? cep;
  @J<PERSON><PERSON><PERSON>(name: 'codUf')
  String? codUf;
  @JsonKey(name: 'codCidade')
  int? codCidade;
  @JsonKey(name: 'nomeCidade')
  String? nomeCidade;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'codLogradouro')
  int? codLogradouro;
  @Json<PERSON>ey(name: 'codTipoLogradouro')
  String? codTipoLogradouro;
  @Json<PERSON><PERSON>(name: 'nomeTipoLogradouro')
  String? nomeTipoLogradouro;
  @JsonKey(name: 'nomeLogradouro')
  String? nomeLogradouro;
  @JsonKey(name: 'codBairro')
  int? codBairro;
  @JsonKey(name: 'nomeBairro')
  String? nomeBairro;
  @JsonKey(name: 'nomeUf')
  String? nomeUf;
  @JsonKey(name: 'complementoLogradouro')
  String? complementoLogradouro;

  AddressZipCodeModel({
    required this.cep,
    required this.codUf,
    required this.codCidade,
    required this.nomeCidade,
    required this.codLogradouro,
    required this.codTipoLogradouro,
    required this.nomeTipoLogradouro,
    required this.nomeLogradouro,
    required this.codBairro,
    required this.nomeBairro,
    required this.nomeUf,
    required this.complementoLogradouro,
  });

  factory AddressZipCodeModel.fromJson(Map json) =>
      _$AddressZipCodeModelFromJson(json);
  Map<String, dynamic> toJson() => _$AddressZipCodeModelToJson(this);
}

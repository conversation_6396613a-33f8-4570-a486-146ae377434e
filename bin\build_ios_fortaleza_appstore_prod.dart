import 'dart:convert';
import 'dart:io';

void main(List<String> args) {
  // print('Hi args $args');
  print('Build iOS Fortaleza AppStore PROD...');

  Process.start('flutter', [
    'build',
    'ios',
    '-t',
    'lib/main_prod.dart',
    '--flavor',
    'fortaleza-appstore',
    '--dart-define=CLIENT_ID=UNIMED_FORTALEZA',
    '--release',
    '--verbose',
    ...args
  ]).then((Process process) {
    process.exitCode.then((exitCode) {
      print('exit code: $exitCode');
    });
    process.stdout.transform(utf8.decoder).listen((data) {
      print(data);
    });

    process.stderr.transform(utf8.decoder).listen((data) {
      print(data);
    });
  }).catchError((onError) {
    print('onError $onError');
  });
}

import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:flutter/material.dart';

import 'cid_list_screen.dart';

class CardConsultarCid extends StatelessWidget {
  const CardConsultarCid({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Container(
        padding: const EdgeInsets.only(top: 8.0, left: 8.0, right: 8.0),
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              _headerCard(context),
              const SizedBox(height: 10),
              const Text("Doença ou código", style: TextStyle(fontSize: 16)),
              const SizedBox(height: 10),
              _buttonDetails(context)
              //SizedBox(height: 1,)
            ]),
      ),
    );
  }

  Widget _headerCard(context) {
    return const Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Padding(
          padding: EdgeInsets.symmetric(vertical: 8.0),
          child: Text(
            "Consulta CID",
            style: TextStyle(
                color: CooperadoColors.blackText,
                fontSize: 16,
                fontWeight: FontWeight.bold),
          ),
        ),
        //SizedBox(height: 300, child: ProducaoChart())
      ],
    );
  }

  Widget _buttonDetails(context) {
    return Align(
      child: InkWell(
        onTap: () {
          Navigator.push(context, FadeRoute(page: const CidListScreen()));
        },
        child: Container(
          padding: const EdgeInsets.only(
              top: 12.0, bottom: 12.0, left: 40, right: 40),
          decoration: const BoxDecoration(
              color: CooperadoColors.tealGreen,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20), topRight: Radius.circular(20))),
          child: const Text(
            "BUSCAR",
            style: TextStyle(
              //fontSize: 18,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}

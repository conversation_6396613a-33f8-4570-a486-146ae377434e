import 'package:cooperado_minha_unimed/models/res/res_allergie_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ResAllergieModel', () {
    test('fromJson - sucesso', () {
      final json = {
        'alergeno': 'P<PERSON>len',
        'local': 'Parque',
        'data': '2023-10-01T12:34:56Z',
        'categoria': 'Alergia a Plantas',
      };

      final model = ResAllergieModel.fromJson(json);

      expect(model.alergeno, 'Pólen');
      expect(model.local, 'Parque');
      expect(model.dataTime, '2023-10-01T12:34:56Z');
      expect(model.categoria, 'Alergia a Plantas');
    });

    test('fromJson - erro', () {
      final json = {
        'alergeno': 'Pólen',
        'local': 'Parque',
        // 'data' está faltando
        'categoria': 'Alergia a Plantas',
      };

      expect(() => ResAllergieModel.from<PERSON>son(json), throwsA(isA<TypeError>()));
    });

    test('toJson - sucesso', () {
      final model = ResAllergieModel(
        alergeno: 'Pólen',
        local: 'Parque',
        dataTime: '2023-10-01T12:34:56Z',
        categoria: 'Alergia a Plantas',
      );

      final json = model.toJson();

      expect(json['alergeno'], 'Pólen');
      expect(json['local'], 'Parque');
      expect(json['data'], '2023-10-01T12:34:56Z');
      expect(json['categoria'], 'Alergia a Plantas');
    });

    test('dataTimeFormatted - sucesso', () {
      final model = ResAllergieModel(
        alergeno: 'Pólen',
        local: 'Parque',
        dataTime: '2023-10-01T12:34:56Z',
        categoria: 'Alergia a Plantas',
      );

      final formattedDate = model.dataTimeFormatted;

      expect(formattedDate, '01/10/2023 09:34:56');
    });

    test('dataTimeFormatted - erro', () {
      final model = ResAllergieModel(
        alergeno: 'Pólen',
        local: 'Parque',
        dataTime: 'data inválida',
        categoria: 'Alergia a Plantas',
      );

      expect(() => model.dataTimeFormatted, throwsA(isA<FormatException>()));
    });
  });
}

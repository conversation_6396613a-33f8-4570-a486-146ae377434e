import 'dart:convert';

import 'package:cooperado_minha_unimed/models/general_config_model.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:http_client/exceptions/exceptions.dart';
import 'package:http_client/http_client.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/messages.exceptions.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:http/http.dart';

class GeneralConfigsApi {
  final logger = UnimedLogger(className: 'ConselhoAPI');
  final UnimedHttpClient httpClient;
  GeneralConfigsApi(this.httpClient);

  Future<GeneralConfigModel> getGeneralMessage() async {
    try {
      final UserCredentials? credentials =
          await Locator.instance!.get<AuthApi>().getCredentials();
      final String token =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();

      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}config/cooperado/general/${credentials!.crm}';
      final response = await get(Uri.parse(url), headers: {
        "Authorization": "Bearer $token",
        "Content-Type": "application/json",
      });

      final json = (jsonDecode(response.body));

      if (response.statusCode == 200) {
        GeneralConfigModel returnModel = GeneralConfigModel.fromJson(json);
        logger.d('GeneralConfigMessage, success. length: ${json.length}');
        return returnModel;
      } else {
        logger.e(
            'GeneralConfigMessage: ${response.statusCode} body: ${response.body}');
        throw UnimedException(MessageException.general);
      }
    } on ServiceTimeoutException catch (e) {
      logger.e('getGeneralMessage ServiceTimeoutException ${e.message}');
      rethrow;
    } on UnimedException catch (e) {
      logger.e('getGeneralMessage UnimedException ${e.message}');
      rethrow;
    } catch (ex) {
      logger.e('GeneralConfigMessage: $ex');
      throw UnimedException(MessageException.general);
    }
  }
}

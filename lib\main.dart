import 'dart:async';

//import 'dart:ui';

import 'package:cooperado_minha_unimed/screens/main.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
//import 'package:firebase_crashlytics/firebase_crashlytics.dart';
//import 'package:flutter/foundation.dart' show kDebugMode;
import 'package:flutter/material.dart';

Future<void> main() async {
  //runZonedGuarded<Future<void>>(() async {
  WidgetsFlutterBinding.ensureInitialized();
  FlavorConfig(flavor: Flavor.dev, values: FlavorDEV());

  await Firebase.initializeApp();
  await FirebaseMessaging.instance.setAutoInitEnabled(true);
  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
      alert: true, badge: true, sound: true);

  // FirebaseCrashlytics.instance
  //     .setCustomKey('environment', FlavorConfig.instance?.name ?? "");

  // FlutterError.onError = (FlutterErrorDetails errorDetails) {
  //   if (kDebugMode) return;

  //   FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  // };

  // PlatformDispatcher.instance.onError = (error, stack) {
  //   if (kDebugMode) return false;
  //   FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
  //   return true;
  // };

  runApp(CooperadoUnimed());
  // }, (error, stackTrace) {
  //   if (!kDebugMode) {
  //     FirebaseCrashlytics.instance.recordError(error, stackTrace);
  //   }
  // });
}

class GlosaResourceData {
  int? numSohoev;
  int? guiaReferencia;
  String? dataAtendimento;
  int? codPrestador;
  String? nomePrestador;
  int? unimedCarteira;
  int? codCarteira;
  String? dvCarteira;
  String? nomeBeneficiario;
  int? codParticipacao;
  String? descricaoParticipacao;
  late String codServico;
  late String servicoDv;
  String? descricaoServico;
  String? recursado;
  int? unimedProducao;
  int? qtdeCobrPrest;
  String? motivoRecurso;
  List<StatusAuditoriaModel>? statusAuditoria;
  String? statusAtual;
  int? quantSolicitada;
  int? quantAuditada;
  String? dataSolicitacaoRecurso;
  int? sequencial;
  int? numNota;
  late bool showAddFileButton;
  String? justifAuditor;
  int? maxFilesNumber;
  int? maxFileSizeinMb;

  bool? get resourced {
    if (recursado == 'S' || recursado == 'Sim') {
      return true;
    } else if (recursado == 'N' || recursado == 'Não') {
      return false;
    }
    return null;
  }

  GlosaResourceData(
      {this.numSohoev,
      this.guiaReferencia,
      this.dataAtendimento,
      this.codPrestador,
      this.nomePrestador,
      this.unimedCarteira,
      this.codCarteira,
      this.dvCarteira,
      this.nomeBeneficiario,
      this.codParticipacao,
      this.descricaoParticipacao,
      required this.codServico,
      required this.servicoDv,
      this.descricaoServico,
      this.recursado,
      this.unimedProducao,
      this.qtdeCobrPrest,
      this.motivoRecurso,
      this.statusAuditoria,
      this.statusAtual,
      this.quantSolicitada,
      this.quantAuditada,
      this.dataSolicitacaoRecurso,
      this.sequencial,
      this.numNota,
      this.showAddFileButton = false,
      this.justifAuditor,
      this.maxFilesNumber,
      this.maxFileSizeinMb});

  GlosaResourceData.fromJson(Map<String, dynamic> json) {
    numSohoev = json['numSohoev'];
    guiaReferencia = json['guiaReferencia'];
    dataAtendimento = json['dataAtendimento'];
    codPrestador = json['codPrestador'];
    nomePrestador = json['nomePrestador'];
    unimedCarteira = json['unimedCarteira'];
    codCarteira = json['codCarteira'];
    dvCarteira = json['dvCarteira'];
    nomeBeneficiario = json['nomeBeneficiario'];
    codParticipacao = json['codParticipacao'];
    descricaoParticipacao = json['descricaoParticipacao'];
    codServico = json['codServico'];
    servicoDv = json['servicoDv'];
    descricaoServico = json['descricaoServico'];
    recursado = json['recursado'];
    unimedProducao = json['unimedProducao'];
    qtdeCobrPrest = json['qtdeCobrPrest'];
    motivoRecurso = json['motivoRecurso'];
    if (json['statusAuditoria'] != null) {
      statusAuditoria = [];
      json['statusAuditoria'].forEach((v) {
        statusAuditoria!.add(StatusAuditoriaModel.fromJson(v));
      });
    }
    statusAtual = json['statusAtual'];
    quantSolicitada = json['quantSolicitada'];
    quantAuditada = json['quantAuditada'];
    dataSolicitacaoRecurso = json['dataSolicitacaoRecurso'];
    sequencial = json['sequencial'];
    numNota = json['numNota'];
    showAddFileButton = json['showAddFileButton'] ?? false;
    justifAuditor = json['justifAuditor'];
    maxFilesNumber = json['maxFilesNumber'];
    maxFileSizeinMb = json['maxFileSizeinMb'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['numSohoev'] = numSohoev;
    data['guiaReferencia'] = guiaReferencia;
    data['dataAtendimento'] = dataAtendimento;
    data['codPrestador'] = codPrestador;
    data['nomePrestador'] = nomePrestador;
    data['unimedCarteira'] = unimedCarteira;
    data['codCarteira'] = codCarteira;
    data['dvCarteira'] = dvCarteira;
    data['nomeBeneficiario'] = nomeBeneficiario;
    data['codParticipacao'] = codParticipacao;
    data['descricaoParticipacao'] = descricaoParticipacao;
    data['codServico'] = codServico;
    data['servicoDv'] = servicoDv;
    data['descricaoServico'] = descricaoServico;
    data['recursado'] = recursado;
    data['unimedProducao'] = unimedProducao;
    data['qtdeCobrPrest'] = qtdeCobrPrest;
    data['motivoRecurso'] = motivoRecurso;
    if (statusAuditoria != null) {
      data['statusAuditoria'] =
          statusAuditoria!.map((v) => v.toJson()).toList();
    }
    data['statusAtual'] = statusAtual;
    data['dataSolicitacaoRecurso'] = dataSolicitacaoRecurso;
    data['sequencial'] = sequencial;
    data['numNota'] = numNota;
    data['showAddFileButton'] = showAddFileButton;
    data['justifAuditor'] = justifAuditor;
    data['maxFilesNumber'] = maxFilesNumber;
    data['maxFileSizeinMb'] = maxFileSizeinMb;
    return data;
  }
}

class StatusAuditoriaModel {
  SpgStatusAuditoria? spgStatusAuditoria;
  String? dataAuditoria;
  String? usuarioAuditoria;

  StatusAuditoriaModel(
      {this.spgStatusAuditoria, this.dataAuditoria, this.usuarioAuditoria});

  StatusAuditoriaModel.fromJson(Map<String, dynamic> json) {
    spgStatusAuditoria = json['spgStatusAuditoria'] != null
        ? SpgStatusAuditoria.fromJson(json['spgStatusAuditoria'])
        : null;
    dataAuditoria = json['dataAuditoria'];
    usuarioAuditoria = json['usuarioAuditoria'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (spgStatusAuditoria != null) {
      data['spgStatusAuditoria'] = spgStatusAuditoria!.toJson();
    }
    data['dataAuditoria'] = dataAuditoria;
    data['usuarioAuditoria'] = usuarioAuditoria;
    return data;
  }
}

class SpgStatusAuditoria {
  int? codStatus;
  String? descricaoStatus;

  SpgStatusAuditoria({this.codStatus, this.descricaoStatus});

  SpgStatusAuditoria.fromJson(Map<String, dynamic> json) {
    codStatus = json['codStatus'];
    descricaoStatus = json['descricaoStatus'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codStatus'] = codStatus;
    data['descricaoStatus'] = descricaoStatus;
    return data;
  }
}

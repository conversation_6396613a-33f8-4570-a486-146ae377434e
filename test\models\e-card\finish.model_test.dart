import 'package:cooperado_minha_unimed/models/e-card/finish.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EcardFinishCardModel', () {
    test('fromJson - sucesso', () {
      final json = {
        'message': 'Test message',
      };

      final model = EcardFinishCardModel.fromJson(json);

      expect(model.message, 'Test message');
    });

    test('toJson - sucesso', () {
      final model = EcardFinishCardModel(message: 'Test message');

      final json = model.toJson();

      expect(json['data']['eCardDisableCard']['message'], 'Test message');
    });
  });
}

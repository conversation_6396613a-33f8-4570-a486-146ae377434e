import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/shared/api/pdf.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/utils/file.dart';
import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:equatable/equatable.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdfx/pdfx.dart';

part 'pdf_factory_state.dart';

class PdfFactoryCubit extends Cubit<PdfFactoryState> {
  PdfFactoryCubit() : super(PdfFactoryInitial());

  Future generatePdfDocument(
      {required String url,
      required String filename,
      bool isFirebase = false}) async {
    try {
      emit(LoadingPdf());

      if (isFirebase) {
        final file =
            await Locator.instance!<PdfApi>().getFirebaseFile(url, filename);

        final pdfDocument = PdfController(
          document: PdfDocument.openFile(file.path),
        );

        emit(DonePdf(pdfDocument: pdfDocument, file: file));
      } else {
        final filenameVerified = StringUtils.formatFilenamePDF(filename);
        final file = await Locator.instance!<PdfApi>()
            .createPDFFileFromUrl(url, filenameVerified);
        final pdfDocument = PdfController(
          document: PdfDocument.openFile(file.path),
        );

        emit(DonePdf(pdfDocument: pdfDocument, file: file));
      }
    } catch (ex) {
      emit(ErrorPdf('$ex'));
    }
  }

  Future generatePdfDocumentFromPath(
      {required String url, required String filename, String? oa}) async {
    try {
      emit(LoadingPdf());

      final name = FileUtils.formatFilenamePDF(filename);
      final documentDirectory = (await getApplicationDocumentsDirectory()).path;
      File file = File('$documentDirectory'
          '/${DateTime.now().millisecondsSinceEpoch.toString()}_$name');

      final fileBytes = await File(url).readAsBytes();
      await file.writeAsBytes(fileBytes);

      final pdfDocument = PdfController(document: PdfDocument.openFile(url));

      emit(DonePdf(pdfDocument: pdfDocument, file: file));
    } catch (ex) {
      emit(ErrorPdf('$ex'));
    }
  }
}

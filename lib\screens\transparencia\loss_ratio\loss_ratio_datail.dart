// ignore_for_file: library_private_types_in_public_api

import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/bloc/transparency/loss_ratio/loss_ratio.cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/graph-transparency/transparency_barchart.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/loss_ratio/widgets/loss_ratio_table.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/widgets/header_chart.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/indices.dart';
import 'package:cooperado_minha_unimed/shared/widgets/graphics/indicator.dart';
import 'package:cooperado_minha_unimed/shared/widgets/transparency_collapsible_head.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class LossRatioDetail extends StatefulWidget {
  final VOIndicatorModel? lossRatio;
  const LossRatioDetail({super.key, this.lossRatio});
  @override
  _LossRatioDetailState createState() => _LossRatioDetailState();
}

class _LossRatioDetailState extends State<LossRatioDetail> {
  bool isError = false;
  @override
  void initState() {
    super.initState();
  }

  void _getLossRatio() {
    context.read<LossRatioCubit>().getLossRatio();
  }

  int dataLimit = 6;

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
      builder: (context, orientation) {
        dataLimit =
            MediaQuery.of(context).orientation == Orientation.portrait ? 6 : 12;
        return PopScope(
          onPopInvokedWithResult: (value, result) async {
            _getLossRatio();
          },
          child: Scaffold(
            appBar: AppBar(
              centerTitle: true,
              title: const Text("Sinistralidade"),
              backgroundColor: CooperadoColors.tealGreenDark,
              actions: [IconButton(
                       icon: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
              builder: (context, state) {
                return Icon(
                  state.isSensitiveDataVisible ? Icons.visibility : Icons.visibility_off,
                );
              },
                       ),
                       onPressed: () {
              context.read<SensitiveDataCubit>().toggleSensitiveDataVisibility();
                       },
                     ) , _iconChangeOrientations(orientation)],
            ),
            body: SafeArea(
              child: Container(
                padding: const EdgeInsets.all(10.0),
                child: SingleChildScrollView(
                  child: Column(
                    children: <Widget>[
                      const TransparencyCollapsibleHead(
                        title: 'O que é sinistralidade',
                        dividend: 'Custo assistencial',
                        divider: 'Receita assistencial',
                        quotient: 'Sinistralidade em %',
                        quotientSub: '(Resultado x 100)',
                      ),
                      const SizedBox(height: 20),
                      const Center(
                        child: TransparencyHeaderChart(
                            title: 'Índice de sinistralidade'),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                            left: 8.0, bottom: 20, top: 20),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Indicator(
                              color: CooperadoColors.chartColors[9],
                              text: 'Mensal',
                              isSquare: true,
                            ),
                            const SizedBox(width: 20),
                            Indicator(
                              color: CooperadoColors.chartColors[7],
                              text: 'Acumulado',
                              isSquare: true,
                            ),
                            const SizedBox(width: 20),
                            Indicator(
                              color: CooperadoColors.chartColors[8],
                              text: 'Projetado',
                              isSquare: true,
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 200,
                        child: TransparencyBarChart(
                          animate: true,
                          data: widget.lossRatio!.data,
                          colors: [
                            CooperadoColors.chartColors[9],
                            CooperadoColors.chartColors[7],
                            CooperadoColors.chartColors[8]
                          ],
                          orientation: MediaQuery.of(context).orientation,
                          dataLimit: dataLimit,
                        ),
                      ),
                      const SizedBox(height: 25),
                      LossRatioTable(
                        lossRatio: widget.lossRatio,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _iconChangeOrientations(orientation) {
    return IconButton(
      icon: const Icon(Icons.screen_rotation_rounded, color: Colors.white),
      onPressed: () {
        orientation.index == DeviceOrientation.portraitUp.index
            ? SystemChrome.setPreferredOrientations(
                [DeviceOrientation.landscapeLeft])
            : SystemChrome.setPreferredOrientations(
                [DeviceOrientation.portraitUp]);
      },
    );
  }
}

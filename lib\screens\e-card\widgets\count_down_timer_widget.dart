import 'dart:async';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/timer-card/timer_card.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CountdownTimerWidget extends StatefulWidget {
  final String initialTime;
  final String finalTime;

  const CountdownTimerWidget({
    super.key,
    required this.initialTime,
    required this.finalTime,
  });

  @override
  CountdownTimerWidgetState createState() => CountdownTimerWidgetState();
}

class CountdownTimerWidgetState extends State<CountdownTimerWidget> {
  late Duration _remainingTime = Duration.zero;
  Timer _timer = Timer(const Duration(seconds: 0), () {});
  late DateTime initialDateTime;
  late DateTime finalDateTime;

  @override
  void initState() {
    super.initState();

    _initializeTimer();
  }

  void _initializeTimer() {
    initialDateTime = DateTime.parse(widget.initialTime).toLocal();
    finalDateTime = DateTime.parse(widget.finalTime).toLocal();

    final now = DateTime.now();
    if (now.isAfter(finalDateTime)) {
      setState(() {
        _remainingTime = Duration.zero;
      });

      context.read<TimerEcardCubit>().setStateTimerEcard(
            isTimerRunner: true,
          );
      return;
    }

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        final now = DateTime.now();
        if (now.isBefore(finalDateTime)) {
          _remainingTime = finalDateTime.difference(now);
        } else {
          _remainingTime = Duration.zero;
          context.read<TimerEcardCubit>().setStateTimerEcard(
                isTimerRunner: true,
              );
          _timer.cancel();
        }
      });
    });
  }

  @override
  void dispose() {
    if (_timer.isActive) {
      _timer.cancel();
    }
    super.dispose();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return "${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds";
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const AutoSizeText(
          'Tempo Restante: ',
          minFontSize: 8,
          maxFontSize: 14,
          style: TextStyle(
            color: CooperadoColors.grayLight,
          ),
        ),
        AutoSizeText(
          _formatDuration(_remainingTime),
          minFontSize: 8,
          maxFontSize: 14,
          style: const TextStyle(
            color: CooperadoColors.grayLight,
          ),
        ),
      ],
    );
  }
}

import 'package:cooperado_minha_unimed/models/res/res_allert_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ResAllertModel Tests', () {
    late Map<String, dynamic> mockJson;

    setUp(() {
      mockJson = {
        'alert': 'Test Alert',
        'origin': 'Test Origin',
        'date': '2024-11-22T10:30:00Z',
        'type': 'Test Type',
        'typeOrigin': 'Test Type Origin',
      };
    });

    test('Should parse JSON correctly in fromJson constructor', () {
      final model = ResAllertModel.fromJson(mockJson);

      expect(model.alert, mockJson['alert']);
      expect(model.origin, mockJson['origin']);
      expect(model.date, mockJson['date']);
      expect(model.type, mockJson['type']);
      expect(model.typeOrigin, mockJson['typeOrigin']);
    });

    test('Should serialize to JSON correctly using toJson method', () {
      final model = ResAllertModel(
        alert: 'Serialized Alert',
        origin: 'Serialized Origin',
        date: '2024-11-22T10:30:00Z',
        type: 'Serialized Type',
        typeOrigin: 'Serialized Type Origin',
      );

      final json = model.toJson();

      expect(json['alert'], model.alert);
      expect(json['origin'], model.origin);
      expect(json['date'], model.date);
      expect(json['type'], model.type);
      expect(json['typeOrigin'], model.typeOrigin);
    });

    test('Should format date correctly in dateFormatted getter', () {
      final model = ResAllertModel.fromJson(mockJson);

      expect(model.dateFormatted, '22/11/2024');
    });

    test('Should throw FormatException for invalid date in dateFormatted', () {
      final invalidJson = {...mockJson, 'date': 'invalid-date'};
      final model = ResAllertModel.fromJson(invalidJson);

      expect(() => model.dateFormatted, throwsFormatException);
    });

    test('Equality test for different instances with same data', () {
      final model1 = ResAllertModel.fromJson(mockJson);
      final model2 = ResAllertModel(
        alert: mockJson['alert']!,
        origin: mockJson['origin']!,
        date: mockJson['date']!,
        type: mockJson['type']!,
        typeOrigin: mockJson['typeOrigin']!,
      );

      expect(model1.toJson(), equals(model2.toJson()));
    });
  });
}

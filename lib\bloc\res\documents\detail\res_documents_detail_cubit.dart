import 'package:cooperado_minha_unimed/models/res/res_document_detail_model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'res_documents_detail_state.dart';

class ResDocumentDetailCubit extends Cubit<ResDocumentDetailState> {
  ResDocumentDetailCubit() : super(InitialResDocumentDetailState());

  List<ResDocumentDetailModel>? _resDocumentDetailModel;
  List<ResDocumentDetailModel>? get resDocumentDetailModel =>
      _resDocumentDetailModel;

  void getDocumentDetail(
      {required String crm,
      required String card,
      required String code,
      int index = 0}) async {
    try {
      emit(LoadingResDocumentDetailState());

      _resDocumentDetailModel = await Locator.instance!<ResGraphQlApi>()
          .resDocumentDetail(crm: crm, card: card, code: code);

      _resDocumentDetailModel!.isEmpty
          ? emit(const NoDataResDocumentDetailState())
          : emit(LoadedResDocumentDetailState(
              resDocumentDetailModel: _resDocumentDetailModel!, index: index));
    } catch (e) {
      emit(ErrorResDocumentDetailState(message: e.toString()));
    }
  }
}

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:cooperado_minha_unimed/models/password_rules.model.dart';
import 'package:cooperado_minha_unimed/models/redefine_password.model.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/models/version_validate.vo.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/messages.exceptions.dart';
import 'package:cooperado_minha_unimed/shared/services/version.service.dart';
import 'package:cooperado_minha_unimed/shared/utils/http.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:encrypt/encrypt.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
//import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart' show debugPrint;
import 'package:http_client/exceptions/exceptions.dart';
import 'package:http_client/http_client.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';
import 'package:shared_preferences/shared_preferences.dart';

const String clientId = String.fromEnvironment('CLIENT_ID');

class AuthApi {
  final UnimedHttpClient httpClient;
  final attributeCredentials = 'cookie-session';
  final firstTime = "firstTime";
  final passwordCredentials = '357538782F413F4428472B4B62506553';
  UserCredentials? _credentials;
  String? _tokenPerfilApps;
  String? fcmToken;
  User? user;

  final String _perfilAppsBaseUrl =
      FlavorConfig.instance!.values.profilePermissions.url;

  final logger = UnimedLogger(className: 'AuthApi');

  AuthApi(this.httpClient);

  Future<User> login(
      {required UserCredentials credentials, required String deviceId}) async {
    String token = "";

    if (FlavorConfig.instance?.values.codUnimed == codUnimedFortaleza) {
      token = await User.createToken(credentials: credentials);
    }

    try {
      logger.d('login crm: ${credentials.crm}');

      final versionInfo =
          await (Locator.instance!.get<VersionService>().getInfo());
      final int platform = Platform.isIOS ? 1 : 3;
      try {
        fcmToken = await FirebaseMessaging.instance.getToken();
      } catch (e) {
        logger.e('login FCM Token Error');
      }
      logger.d('login FCM Token: $fcmToken');

      final body = {
        if (FlavorConfig.instance?.values.codUnimed != codUnimedFortaleza)
          "crm": credentials.crm,
        if (FlavorConfig.instance?.values.codUnimed != codUnimedFortaleza)
          "senha": credentials.password,
        "deviceId": deviceId,
        "deviceType": "$platform",
        "versao": versionInfo.version,
        "codUnimed": "${FlavorConfig.instance!.values.codUnimed}",
        "fcmToken": fcmToken,
      };
      final headers = {
        "Content-Type": "application/json",
      };

      String params = "?${token.isNotEmpty ? "tokenPortal=$token" : ""}";

      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/prestador/login$params';
      final response = await httpClient.post(Uri.parse(url),
          body: jsonEncode(body), headers: headers, encodeBody: true);
      //.timeout(const Duration(milliseconds: 100));
      //caso precise de timeout, descomentar a linha abaixo

      if (response.statusCode == 200) {
        User userLogged = User.fromJson(jsonDecode(response.body)['retorno']);
        user = userLogged;

        _setCredentials(credentials);

        Locator.instance!.get<RemoteLog>().setUserId(credentials.crm);

        //FirebaseCrashlytics.instance.setUserIdentifier(credentials.crm);

        logger.d('login User ${credentials.crm} - response: ${response.body}');

        return userLogged;
      } else if (response.statusCode == 404) {
        throw NotFoundException();
      } else {
        var message = jsonDecode(response.body);
        if (message.containsKey('message')) {
          if (message["message"] is List<dynamic>) {
            message["message"] = message["message"][0];
          }
        } else if (message.containsKey('mensagem')) {
          if (message["mensagem"] is List<dynamic>) {
            message["mensagem"] = message["mensagem"][0];
          }
        }

        logger.e(
            'login crm: ${credentials.crm} statusCode : ${response.statusCode} message : ${response.reasonPhrase} => ${response.body}');
        throw AuthException(message["message"] ??
            message["mensagem"] ??
            'Ocorreu um erro ao tentar entrar no sistema. Tente novamente mais tarde.');
      }
    } on TimeoutException {
      logger.e('login TimeoutException: Rede instável ou sem conexão.');
      throw AuthException(
          'Não foi possível realizar o login devido à instabilidade ou falta de conexão com a rede. Verifique sua conexão e tente novamente.');
    } on ServiceTimeoutException catch (e) {
      logger.e('login ServiceTimeoutException ${e.message}');
      throw AuthException(e.message);
    } on AuthException catch (e) {
      logger.e('login Exception => $e');
      rethrow;
    } on Exception catch (e) {
      logger.e('login Exception => $e');
      throw AuthException(
          'Ocorreu um erro ao tentar entrar no sistema. Tente novamente mais tarde.');
    }
  }

  Future<String?> logout(UserCredentials credentials) async {
    final token = await User.createToken(credentials: credentials);
    SharedPreferences prefs = await SharedPreferences.getInstance();

    final deviceId = user?.FCMUserId ?? "";
    final versionInfo =
        await (Locator.instance!.get<VersionService>().getInfo());

    prefs.remove(attributeCredentials);
    prefs.remove(firstTime);
    _credentials = null;

    FirebaseMessaging.instance.deleteToken();

    try {
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}auth/logout-cooperado?t=$token&i=$deviceId&v=${versionInfo.version}';

      final response = await httpClient
          .get(Uri.parse(url), headers: {"Content-Type": "application/json"});

      if (response.statusCode == 200) {
        logger.d("Logout app success... | response: ${response.body}");

        return jsonDecode(response.body)['mensagem'];
      } else {
        final message = jsonDecode(response.body);

        logger.e(
            'logout statusCode : ${response.statusCode} message : ${response.reasonPhrase} => ${message["mensagem"]}');
        throw AuthException(message["mensagem"]);
      }
    } on NoInternetException catch (ex) {
      logger.i('logout NoInternetException: $ex');
      throw AuthException(ex.message);
    } on TimeoutException catch (ex) {
      logger.e('logout TimeoutException: $ex');
      throw AuthException(ex.message);
    } on AuthException catch (ex) {
      throw AuthException(ex.message);
    } catch (ex) {
      logger.e('logout catch exception: $ex');
      throw AuthException('$ex');
    }
  }

  Future<UserCredentials?> getCredentials() async {
    if (FlavorConfig.isTest()) {
      if (_credentials != null) {
        return _credentials!;
      } else {
        return UserCredentials(crm: '5023', password: '123456');
      }
    } else {
      SharedPreferences prefs = await SharedPreferences.getInstance();

      final dataCrypted = prefs.getString(attributeCredentials);

      if (dataCrypted != null && dataCrypted != "" && dataCrypted.isNotEmpty) {
        final key = Key.fromUtf8(passwordCredentials);
        final iv = IV.fromLength(16);
        final encrypter = Encrypter(AES(key));

        final encrypted = Encrypted.fromBase64(dataCrypted);
        final data = encrypter.decrypt(encrypted, iv: iv);

        final jsonData = json.decode(data);

        return UserCredentials(
          crm: jsonData['crm'],
          password: jsonData['password'],
        );
      } else {
        return null;
      }
    }
  }

  Future<void> _setCredentials(UserCredentials credentials) async {
    if (!FlavorConfig.isTest()) {
      SharedPreferences prefs = await SharedPreferences.getInstance();

      final key = Key.fromUtf8(passwordCredentials);
      final iv = IV.fromLength(16);
      final encrypter = Encrypter(AES(key));

      Map<String, dynamic> jsonToSave = {
        'crm': credentials.crm,
        'password': credentials.password,
      };

      final jsonString = json.encode(jsonToSave);
      final encrypted = encrypter.encrypt(jsonString, iv: iv);

      await prefs.setString(attributeCredentials, encrypted.base64);
    } else {
      _credentials = credentials;
    }
  }

  Future<String?> sendRegister({
    required String name,
    required String birthDate,
    required String email,
    required String crm,
    required String cpf,
    required String password,
    required String confirmPassword,
    required bool terms,
  }) async {
    final url =
        '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/prestador';

    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final passwordConverted = password;
      final confirmPasswordConverted = confirmPassword;
      final postData = jsonEncode({
        "senha": passwordConverted,
        "confirmaSenha": confirmPasswordConverted,
        "confirmaTermo": terms.toString(),
        "cpf": cpf,
        "crm": crm,
        "dataNascimento": birthDate,
        "email": email,
        "nome": name,
      });
      final headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer $tokenPerfilApps",
      };
      final response = await httpClient.post(
        Uri.parse(url),
        body: postData,
        headers: headers,
      );

      final responseBody = jsonDecode(utf8.decode(response.bodyBytes));
      final mensagem = responseBody["message"];

      if (response.statusCode == 200 || response.statusCode == 201) {
        logger.d('sendRegister success ${response.body}');

        return responseBody["message"];
      } else {
        logger.e(
            'sendRegister() statusCode : ${response.statusCode} message : ${response.body}');
        throw AuthException(mensagem);
      }
    } on AuthException catch (ex) {
      throw AuthException(ex.message);
    } catch (ex) {
      logger.e('sendRegister() exception: $ex');
      throw AuthException(
          'Ocorreu um erro ao tentar entrar no sistema. Tente novamente mais tarde.');
    }
  }

  Future<String> recoverPassword({
    required String crm,
  }) async {
    final token = await Locator.instance!.get<AuthApi>().tokenPerfilApps();
    const String urlEndpoint = clientId == 'UNIMED_FORTALEZA'
        ? 'auth/cooperado/esqueceu-senha'
        : 'app/cooperado/forgot-password/generate/access-token';
    final url = _perfilAppsBaseUrl + urlEndpoint;
    debugPrint(url);
    final postData = jsonEncode({"crm": crm});
    try {
      final response =
          await httpClient.post(Uri.parse(url), body: postData, headers: {
        "Authorization": "Bearer $token",
        "Content-Type": "application/json",
      });

      final responseBody = jsonDecode(response.body);
      if (response.statusCode == 200 || response.statusCode == 201) {
        String message;
        if (responseBody["mensagem"] == "" ||
            responseBody["mensagem"] == null) {
          message = responseBody["retorno"];
        } else {
          message = responseBody["mensagem"];
        }
        return message;
      } else {
        logger.e(
            'recoverPassword() statusCode : ${response.statusCode} body : ${response.body}');
        throw AuthException(responseBody["mensagem"] ??
            'Ocorreu um erro ao tentar redefinir sua senha. Tente novamente mais tarde.');
      }
    } on AuthException catch (ex) {
      logger.e('recoverPassword() AuthException : $ex');
      throw AuthException(ex.message ??
          'Ocorreu um erro ao tentar redefinir sua senha. Tente novamente mais tarde.');
    } on NoInternetException catch (ex) {
      logger.e('recoverPassword() NoInternetException : $ex');
      throw NoInternetException();
    } catch (ex) {
      logger.e('recoverPassword() exception: $ex');
      throw AuthException(
          'Ocorreu um erro ao tentar redefinir sua senha. Tente novamente mais tarde.');
    }
  }

  Future<String> redefinePassword(
      {required RedefinePasswordModel redefinePasswordModel}) async {
    final tokenPerfilApps =
        await Locator.instance!.get<AuthApi>().tokenPerfilApps();
    redefinePasswordModel.crm ??=
        await loginIdentification(token: redefinePasswordModel.token);
    const String urlEndpoint = clientId == 'UNIMED_FORTALEZA'
        ? 'auth/cooperado/esqueceu-senha/redefinir'
        : 'app/cooperado/forgot-password/reset';
    final url = _perfilAppsBaseUrl + urlEndpoint;

    final postData = jsonEncode(redefinePasswordModel);
    try {
      final response =
          await httpClient.post(Uri.parse(url), body: postData, headers: {
        "Authorization": "Bearer $tokenPerfilApps",
        "Content-Type": "application/json",
      });

      final responseBody = jsonDecode(utf8.decode(response.bodyBytes));

      if (redefinePasswordModel.senha != redefinePasswordModel.confirmarSenha) {
        logger.d(
            'redefinePassword() exception: Nova senha e confirmação não conferem...');
        throw AuthException('Nova senha e confirmação não conferem...');
      }

      if (response.statusCode == 200 || response.statusCode == 201) {
        String message = responseBody["mensagem"] ?? "";
        if (message == "") {
          return "Senha alterada com sucesso!";
        } else {
          return message;
        }
      } else {
        logger.e(
            'redefinePassword() statusCode : ${response.statusCode} body : ${response.body}');
        throw AuthException(responseBody["mensagem"] ??
            'Ocorreu um erro ao tentar redefinir sua senha. Tente novamente mais tarde.');
      }
    } on AuthException catch (ex) {
      logger.e('redefinePassword() AuthException : $ex');
      throw AuthException(ex.message ??
          'Ocorreu um erro ao tentar redefinir sua senha. Tente novamente mais tarde.');
    } on NoInternetException catch (ex) {
      logger.e('redefinePassword() NoInternetException : $ex');
      throw NoInternetException();
    } catch (ex) {
      logger.e('redefinePassword() exception: $ex');
      throw AuthException(
          'Ocorreu um erro ao tentar redefinir sua senha. Tente novamente mais tarde.');
    }
  }

  Future<String> loginIdentification({
    required String token,
  }) async {
    final tokenPerfilApps =
        await Locator.instance!.get<AuthApi>().tokenPerfilApps();
    final url =
        '${FlavorConfig.instance!.values.profilePermissions.url}auth/cooperado/esqueceu-senha/token/$token';
    try {
      final response = await httpClient.get(Uri.parse(url), headers: {
        "Authorization": "Bearer $tokenPerfilApps",
        "Content-Type": "application/json",
      });

      final responseBody = jsonDecode(utf8.decode(response.bodyBytes));
      final mensagem = responseBody["mensagem"];

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (responseBody['sucesso'] == true) {
          logger.d('loginIdentification success ${response.body}');
          final loginCrm = responseBody["retorno"]["login"];
          return loginCrm;
        } else {
          logger.d('loginIdentification exception ${response.body}');
          throw AuthException(responseBody["mensagem"] ??
              'Ocorreu um erro ao tentar redefinir sua senha. Tente novamente mais tarde.');
        }
      } else {
        logger.e(
            'loginIdentification() statusCode : ${response.statusCode} message : ${response.body}');
        throw AuthException(mensagem);
      }
    } on AuthException catch (ex) {
      throw AuthException(ex.message ??
          'Ocorreu um erro ao tentar redefinir sua senha. Tente novamente mais tarde.');
    } on NoInternetException catch (ex) {
      logger.e('redefinePassword NoInternetException : $ex');
      throw NoInternetException();
    } catch (ex) {
      logger.e('loginIdentification() exception: $ex');
      throw AuthException(
          'Ocorreu um erro ao tentar redefinir sua senha. Tente novamente mais tarde.');
    }
  }

  Future<String> tokenPerfilApps({String? user, String? password}) async {
    if (!_isValidTokenPerfilApps(_tokenPerfilApps)) {
      user ??= FlavorConfig.instance!.values.profilePermissions.user;
      password ??= FlavorConfig.instance!.values.profilePermissions.password;

      try {
        final String url =
            '${FlavorConfig.instance!.values.profilePermissions.url}auth/login';
        final body = jsonEncode({"user": user, "password": password});

        final headers = {"Content-Type": "application/json"};

        final response =
            await httpClient.post(Uri.parse(url), body: body, headers: headers);

        logger.d(
            'AuthApi tokenPerfilApps user ${FlavorConfig.instance!.values.profilePermissions.user}');
        logger.d(
            'AuthApi tokenPerfilApps pass ${FlavorConfig.instance!.values.profilePermissions.password.length}');

        if (response.statusCode == 200 || response.statusCode == 201) {
          logger.d('AuthApi tokenPerfilApps success logged');
          _tokenPerfilApps = (jsonDecode(response.body))['token'];
          logger.d('AuthApi tokenPerfilApps token $_tokenPerfilApps');

          return _tokenPerfilApps!;
        } else {
          logger.e('AuthApi error status != 200 ${response.body}');
          throw AuthException(
              'AuthApi tokenPerfilApps != 200 => ${response.statusCode}');
        }
      } on NoInternetException catch (ex) {
        logger.e('tokenPerfilApps NoInternetException : $ex');
        throw NoInternetException();
      } catch (e) {
        logger.e('AuthApi tokenPerfilApps Exception $e');

        throw AuthException(MessageException.general);
      }
    } else {
      return _tokenPerfilApps!;
    }
  }

  bool _isValidTokenPerfilApps(String? token) {
    if (_tokenPerfilApps == null || _tokenPerfilApps!.isEmpty) {
      return false;
    } else {
      final parts = token!.split('.');
      if (parts.length != 3) {
        throw Exception('invalid token');
      }

      final payload = _decodeBase64(parts[1]);
      final payloadMap = json.decode(payload);
      if (payloadMap is! Map<String, dynamic>) {
        throw Exception('invalid payload');
      }

      final DateTime exp =
          DateTime.fromMillisecondsSinceEpoch(payloadMap['exp']);
      return exp.isAfter(DateTime.now());
    }
  }

  String _decodeBase64(String str) {
    String output = str.replaceAll('-', '+').replaceAll('_', '/');

    switch (output.length % 4) {
      case 0:
        break;
      case 2:
        output += '==';
        break;
      case 3:
        output += '=';
        break;
      default:
        throw Exception('Illegal base64url string!"');
    }

    return utf8.decode(base64Url.decode(output));
  }

  Future<VersionValidateResponse> checkVersionValidade({
    required String version,
    required String buildNumber,
  }) async {
    try {
      final token = await tokenPerfilApps(
          user: FlavorConfig.instance!.values.profilePermissions.user,
          password: FlavorConfig.instance!.values.profilePermissions.password);

      final versionFormmated = version.trim();
      final buildNumberFormmated = buildNumber.trim();

      final String url =
          '${FlavorConfig.instance!.values.profilePermissions.url}version/cooperado/$versionFormmated/$buildNumberFormmated';

      final headers = {
        "Content-Type": "application/json",
        "Authorization": 'Bearer $token'
      };

      final response = await httpClient.get(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        logger.d('AuthApi checkVersionValidade response => $data');

        final versionValidadeResponse = VersionValidateResponse.fromJson(data);
        versionValidadeResponse.isOutOfDate = false;

        return versionValidadeResponse;
      } else if (response.statusCode == 500) {
        final data = jsonDecode(response.body);
        logger.d('AuthApi checkVersionValidade response => $data');

        if (data['lastVersion'] != null) {
          final versionValidadeResponse =
              VersionValidateResponse.fromJson(data);
          versionValidadeResponse.isOutOfDate = true;

          return versionValidadeResponse;
        } else {
          throw UnimedException(
              'AuthApi checkVersionValidade != 200 => ${response.statusCode}');
        }
      } else {
        logger.e(
            'AuthApi checkVersionValidade error status != 200 ${response.body}');
        throw UnimedException(
            'AuthApi checkVersionValidade != 200 => ${response.statusCode}');
      }
    } on UnimedException {
      rethrow;
    } catch (e) {
      logger.e('AuthApi checkVersionValidade Exception $e');
      throw UnimedException('Erro ao verificar versão ${e.toString()}');
    }
  }

  Future<List<PasswordRulesModel>?> getPasswordRules(
      {required bool isAnonymous}) async {
    try {
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/esqueceu-senha/generate/access-token';

      // TODO - quando for com o usuario logado (ainda não implementado no app), o header deverá ser alterado
      final headers = {
        "Authorization": HttpUtils.getLostPasswordAuthorizationBasicPortal(),
      };

      final responseToken = await httpClient.get(
        Uri.parse(url),
        headers: headers,
      );

      if (responseToken.statusCode == 200 || responseToken.statusCode == 201) {
        final data = jsonDecode(responseToken.body);
        logger.d('RedefinirSenhaApi getAccessTokenPortal response => $data');

        final accessToken = data['access_token'];

        final url =
            '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/esqueceu-senha/password-rules';

        final headersRules = {
          "Authorization": "Bearer $accessToken",
        };

        final responseRules = await httpClient.get(
          Uri.parse(url),
          headers: headersRules,
        );

        if (responseRules.statusCode == 200 ||
            responseRules.statusCode == 201) {
          final dataRules = jsonDecode(responseRules.body);
          logger.d('RedefinirSenhaApi getPasswordRules response => $dataRules');

          final List<PasswordRulesModel> list = [];

          for (final item in dataRules as List<dynamic>) {
            list.add(PasswordRulesModel.fromJson(item));
          }

          return list;
        } else if (responseRules.statusCode == 500) {
          final dataRules = jsonDecode(responseRules.body);
          logger.d(
              'RedefinirSenhaApi getAccessTokenPortal response => $dataRules');

          throw UnimedException(data['message'] ?? MessageException.general);
        } else {
          logger.e(
              'RedefinirSenhaApi getPasswordRules error status != 200 ${responseRules.body}');
          throw UnimedException(MessageException.general);
        }
      } else if (responseToken.statusCode == 500) {
        final data = jsonDecode(responseToken.body);
        logger.d('RedefinirSenhaApi getAccessTokenPortal response => $data');

        throw UnimedException(data['message'] ?? MessageException.general);
      } else {
        logger.e(
            'RedefinirSenhaApi getPasswordRules error status != 200 ${responseToken.body}');
        throw UnimedException(MessageException.general);
      }
    } on UnimedException {
      rethrow;
    } catch (e) {
      logger.e('RedefinirSenhaApi getPasswordRules Exception $e');
      throw UnimedException(MessageException.general);
    }
  }
}

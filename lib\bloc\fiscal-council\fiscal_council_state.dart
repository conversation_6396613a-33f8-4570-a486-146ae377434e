part of 'fiscal_council_cubit.dart';

abstract class FiscalCouncilState extends Equatable {
  const FiscalCouncilState();

  @override
  List<Object?> get props => [];
}

class FiscalCouncilInitial extends FiscalCouncilState {}

class LoadingGetCouncil extends FiscalCouncilState {
  @override
  List<Object> get props => [];
}

class DoneGetCouncil extends FiscalCouncilState {
  final List<FiscalCouncil>? list;
  @override
  List<Object?> get props => [list];
  const DoneGetCouncil({this.list});
}

class ErrorGetCouncil extends FiscalCouncilState {
  final String message;
  @override
  List<Object> get props => [message];

  const ErrorGetCouncil(this.message);
}

class LoadingGetReports extends FiscalCouncilState {
  @override
  List<Object> get props => [];
}

class DoneGetReports extends FiscalCouncilState {
  final List<Noticia>? list;
  @override
  List<Object?> get props => [list];
  const DoneGetReports({this.list});
}

class ErrorGetReports extends FiscalCouncilState {
  final String message;
  @override
  List<Object> get props => [message];

  const ErrorGetReports(this.message);
}

class LoadingGetInternalCommunic extends FiscalCouncilState {
  @override
  List<Object> get props => [];
}

class DoneGetInternalCommunic extends FiscalCouncilState {
  final List<Noticia>? list;
  @override
  List<Object?> get props => [list];
  const DoneGetInternalCommunic({this.list});
}

class ErrorGetInternalCommunic extends FiscalCouncilState {
  final String message;
  @override
  List<Object> get props => [message];

  const ErrorGetInternalCommunic(this.message);
}

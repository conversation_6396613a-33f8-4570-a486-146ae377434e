import 'package:cooperado_minha_unimed/screens/transparencia/contingency-index/widgets/contingency_table_row.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/indices.dart';
import 'package:flutter/material.dart';

class ContingencyTable extends StatefulWidget {
  final VOIndicatorModel? contingencyIndex;
  final Color color;

  const ContingencyTable(
      {super.key, this.contingencyIndex, required this.color});
  @override
  ContingencyTableState createState() => ContingencyTableState();
}

class ContingencyTableState extends State<ContingencyTable> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(children: <Widget>[
      _rowHeader(),
      ..._rowsBody(),
    ]);
  }

  Widget _rowHeader() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 4,
            child: Container(),
          ),
          const Expanded(
            flex: 4,
            child: Text(
              'Desconto mensal (%)',
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _rowsBody() {
    return widget.contingencyIndex!.data!
        .map(
          (item) => ContingencyTableRow(
            monthValue: item.monthValue,
            totalValue: item.totalValue,
            refYear: item.referenceYear,
            refMonth: item.referenceMonth,
            color: widget.color,
          ),
        )
        .toList();
  }
}

import 'package:cooperado_minha_unimed/bloc/notificao/notifications-count/notifications_count_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/notificao/notifications-count/notifications_count_state.dart';
import 'package:cooperado_minha_unimed/bloc/profile/profile_cubit.dart';
import 'package:cooperado_minha_unimed/screens/notificacao/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class NotificationsCountWidget extends StatefulWidget {
  const NotificationsCountWidget({super.key});

  @override
  State<NotificationsCountWidget> createState() =>
      _NotificationsCountWidgetState();
}

class _NotificationsCountWidgetState extends State<NotificationsCountWidget> {
  @override
  void initState() {
    super.initState();
    context.read<NotificationsCountCubit>().getNotificationsCount(
          codPrestador:
              context.read<ProfileCubit>().user.codPrestador.toString(),
        );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NotificationsCountCubit, NotificationsCountState>(
      builder: (context, state) {
        if (state is NotificationsCountStateLoading) {
          return const IconButton(
            onPressed: null,
            icon: Center(
              child: SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(),
              ),
            ),
          );
        } else if (state is NotificationsCountStateLoaded) {
          if (state.notificationsCount == 0) {
            return IconButton(
              icon: const Icon(Icons.notifications_none_rounded),
              onPressed: () => _navigateToNotificationsScreen(),
            );
          }
          return Badge.count(
            count: state.notificationsCount,
            alignment: Alignment.topLeft,
            offset: const Offset(6, 3),
            child: IconButton(
              icon: const Icon(Icons.notifications_none_rounded),
              onPressed: () => _navigateToNotificationsScreen(),
            ),
          );
        }
        return IconButton(
          icon: const Icon(Icons.notifications_none_rounded),
          onPressed: () => _navigateToNotificationsScreen(),
        );
      },
    );
  }

  void _navigateToNotificationsScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const NotificacaoScreen(),
      ),
    );
  }
}

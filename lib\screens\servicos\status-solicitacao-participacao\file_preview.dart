import 'dart:io';

import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:cooperado_minha_unimed/shared/widgets/pdf_view/pdf_view_platform.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class PreviewFileScreen extends StatefulWidget {
  final File file;
  final String fileName;
  final String fileType;
  final String? title;
  const PreviewFileScreen({
    super.key,
    required this.file,
    required this.fileName,
    required this.fileType,
    this.title,
  });

  @override
  PreviewFileScreenState createState() => PreviewFileScreenState();
}

class PreviewFileScreenState extends State<PreviewFileScreen> {
  @override
  Widget build(BuildContext context) {
    return widget.fileType == 'pdf'
        ? PDFViewPlatform(
            widget.file.path,
            share: true,
            isPath: true,
            filename: '',
            title: widget.fileName,
          )
        : Scaffold(
            appBar: AppBar(
              title: _title(),
            ),
            body: Stack(children: [
              const Align(
                alignment: Alignment.topCenter,
                child: SpinKitThreeBounce(
                  color: CooperadoColors.tealGreen,
                  size: 20,
                ),
              ),
              Center(
                child: Image.file(
                  widget.file,
                  errorBuilder: (context, error, stacktrace) {
                    return _error();
                  },
                  fit: BoxFit.contain,
                  // filterQuality: FilterQuality.high,
                ),
              ),
            ]));
  }

  Widget _title() {
    return widget.title == null || widget.title!.isEmpty
        ? Text(widget.fileName)
        : Column(mainAxisAlignment: MainAxisAlignment.center, children: [
            Text(widget.title as String),
            Text(widget.fileName, style: const TextStyle(fontSize: 10))
          ]);
  }

  Widget _error() {
    return const Center(
      child: ErrorBanner(message: 'Não foi possível carregar essa imagem'),
    );
  }
}

import 'package:cooperado_minha_unimed/models/res-internal/solicitation.model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res-internal.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'res_solicitation_state.dart';

class ResSolicitationCubit extends Cubit<ResSolicitationState> {
  ResSolicitationCubit() : super(InitialResSolicitationState());

  List<ResSolicitationModel> _listSolicitations = List.empty(growable: true);
  List<ResSolicitationModel> get listSolicitations => _listSolicitations;

  void listResSolicitations({required String crm}) async {
    try {
      emit(LoadingResSolicitationState());

      _listSolicitations = await Locator.instance!<GraphQlApiInternal>()
          .resInternalSolicitationsByCRM(crm: crm);

      emit(LoadedResSolicitationState(listSolicitations: _listSolicitations));
    } catch (e) {
      emit(ErrorResSolicitationState(message: e.toString()));
    }
  }

  void searchListSolicitations({required String searchKey}) async {
    try {
      emit(LoadingResSolicitationState());

      final filtredList = _listSolicitations
          .where((element) => element
              .toJson()
              .toString()
              .toLowerCase()
              .contains(searchKey.toLowerCase()))
          .toList();

      emit(LoadedResSolicitationState(listSolicitations: filtredList));
    } catch (e) {
      emit(ErrorResSolicitationState(message: e.toString()));
    }
  }
}

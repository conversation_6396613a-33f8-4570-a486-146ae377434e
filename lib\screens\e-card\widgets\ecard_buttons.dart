// ignore_for_file: use_build_context_synchronously, unused_local_variable

import 'package:auto_size_text/auto_size_text.dart';
import 'package:biometria_perfilapps/main.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/activation-check/activation_check_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/activation-check/activation_check_state.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/activation/activation_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/activation/activation_state.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/finish/finish_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/finish/finish_state.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/timer-card/timer_card.dart';
import 'package:cooperado_minha_unimed/bloc/location/location_permission_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/profile/profile_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/utils/biometry_utils.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:permission_handler/permission_handler.dart';

class EcardButtons extends StatefulWidget {
  final bool isActivated;
  final bool isReactivated;
  final bool isButtonEnabled;
  const EcardButtons(
      {super.key,
      required this.isActivated,
      required this.isReactivated,
      required this.isButtonEnabled});

  @override
  State<EcardButtons> createState() => _EcardButtonsState();
}

class _EcardButtonsState extends State<EcardButtons> {
  String errorMessage = '';
  int? selectedPeriod;

  late String codPrestador;

  @override
  void initState() {
    codPrestador = context.read<ProfileCubit>().user.codPrestador.toString();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<EcardActivationCheckCubit, EcardActivationCheckState>(
      listener: (context, state) {},
      builder: (context, state) {
        if (state is LoadingEcardActivationCheckState) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is ErrorEcardActivationCheckState) {
          errorMessage = state.message;
        }
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 8.0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.0),
          ),
          width: double.infinity,
          height: 80.0,
          child: Padding(
            padding: const EdgeInsets.all(3.0),
            child: Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: widget.isButtonEnabled
                        ? () async {
                            if (state is LoadedEcardActivationCheckState ||
                                widget.isReactivated) {
                              PermissionStatus status =
                                  await Permission.location.status;
                              if (status.isGranted) {
                                _showTransactionModalTimePeriod(context);
                              } else {
                                _showTransactionModalPermissionLocation(
                                    context);
                              }
                            }
                          }
                        : null,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          widget.isReactivated
                              ? Icons.alarm
                              : Icons.check_circle_outline_outlined,
                          color: widget.isButtonEnabled == true
                              ? CooperadoColors.tealGreen
                              : Colors.grey,
                        ),
                        const SizedBox(height: 4.0),
                        Text(
                          widget.isReactivated ? 'Reativar' : 'Ativar',
                          style: TextStyle(
                            color: widget.isButtonEnabled == true
                                ? CooperadoColors.tealGreen
                                : Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 5.0),
                  child: VerticalDivider(
                    color: CooperadoColors.grayLight5,
                    width: 1.0,
                    thickness: 1.0,
                    indent: 25,
                    endIndent: 25,
                  ),
                ),
                Expanded(
                  child: InkWell(
                    onTap: () {
                      _showTransactionModalRecuseOrFinish(
                        context: context,
                        isToFinish: widget.isActivated,
                      );
                    },
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.remove_circle_outline_outlined,
                            color: CooperadoColors.redCancel),
                        const SizedBox(height: 4.0),
                        Text(
                          widget.isActivated ? 'Finalizar' : 'Recusar',
                          style: const TextStyle(
                            color: CooperadoColors.redCancel,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showTransactionModalPermissionLocation(BuildContext context) async {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Permite que o modal se ajuste melhor
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(25.0)),
      ),
      builder: (BuildContext context) {
        return BlocListener<LocationPermissionCubit, LocationPermissionState>(
          listener: (context, state) {
            if (state is LocationPermissionGranted) {
              Navigator.pop(context);
            } else if (state is LocationPermissionDenied) {
              // Permissão negada, solicitar novamente
              context
                  .read<LocationPermissionCubit>()
                  .requestLocationPermission();
            } else if (state is LocationPermissionPermanentlyDenied) {
              openAppSettings();
            }
          },
          child: Wrap(
            alignment: WrapAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min, // Ajusta ao conteúdo
                    children: [
                      const Padding(
                        padding: EdgeInsets.symmetric(vertical: 16.0),
                        child: Text(
                          'Confirmar localização.',
                          style: TextStyle(
                              fontSize: 24, fontWeight: FontWeight.bold),
                        ),
                      ),
                      SvgPicture.asset(
                        'assets/svg/icon_location.svg',
                        width: 72,
                        height: 72,
                      ),
                      const SizedBox(height: 20),
                      RichText(
                        textAlign: TextAlign.center,
                        text: const TextSpan(
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.black,
                            height: 1.5,
                          ),
                          children: [
                            TextSpan(
                                text: 'Para continuar com a ativação do\n'),
                            TextSpan(
                              text: 'Uni Card',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            TextSpan(
                              text:
                                  ', permita que o sistema valide\nsua localização.',
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 10),
                      ElevatedButton(
                        onPressed: () async {
                          PermissionStatus status =
                              await Permission.location.request();
                          if (status.isGranted) {
                            Navigator.pop(context);
                            _showTransactionModalTimePeriod(context);
                          } else if (status.isDenied) {
                            status = await Permission.location.request();
                            if (status.isGranted) {
                              Navigator.pop(context);
                              _showTransactionModalTimePeriod(context);
                            }
                          } else if (status.isPermanentlyDenied) {
                            openAppSettings();
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: CooperadoColors.tealGreen,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          padding: const EdgeInsets.symmetric(
                              vertical: 15, horizontal: 30),
                        ),
                        child: const Text(
                          'PERMITIR',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(height: 10),
                      TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        style: TextButton.styleFrom(
                          foregroundColor: CooperadoColors.tealGreen,
                          padding: const EdgeInsets.symmetric(
                              vertical: 15, horizontal: 30),
                        ),
                        child: const Text(
                          'CANCELAR',
                          style: TextStyle(
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showTransactionModalRecuseOrFinish(
      {required BuildContext context, bool isToFinish = false}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Permite que o modal se ajuste melhor
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(25.0)),
      ),
      builder: (BuildContext context) {
        return Wrap(
          alignment: WrapAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min, // Ajusta a altura ao conteúdo
                  children: [
                    const SizedBox(height: 8),
                    AutoSizeText(
                      '${isToFinish ? 'Finalizar' : 'Recusar'} Uni Card.',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                      maxFontSize: 24,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    SvgPicture.asset(
                      'assets/svg/icon_ecard_recuse.svg',
                      height: 85,
                    ),
                    const SizedBox(height: 20),
                    RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.black,
                          height: 1.5,
                        ),
                        children: [
                          TextSpan(
                              text:
                                  'Deseja confirmar a ${isToFinish ? 'finalização' : 'recusa'} do \n'),
                          const TextSpan(
                            text: 'Uni Card?',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        if (isToFinish) {
                          _showTransactionModalBiometria(
                            context: context,
                            isToFinish: isToFinish,
                          );
                        } else {
                          Navigator.pop(context);
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: CooperadoColors.tealGreen,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: const EdgeInsets.symmetric(
                            vertical: 15, horizontal: 30),
                      ),
                      child: const Text(
                        'Confirmar',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(height: 10),
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: CooperadoColors.tealGreen,
                        padding: const EdgeInsets.symmetric(
                            vertical: 15, horizontal: 30),
                      ),
                      child: const Text(
                        'Cancelar',
                        style: TextStyle(
                          fontSize: 16,
                        ),
                      ),
                    ),
                    const SizedBox(height: 10),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showTransactionModalBiometria({
    required BuildContext context,
    bool isToFinish = false,
    bool isReactivated = false,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Permite melhor ajuste do modal
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(25.0)),
      ),
      builder: (BuildContext context) {
        return Wrap(
          alignment: WrapAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.all(20.0),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      child: Text(
                        '${isToFinish ? 'Finalizar' : 'Ativar'} Uni Card',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    SvgPicture.asset(
                      'assets/svg/icon_biometria.svg',
                      height: 85,
                    ),
                    const SizedBox(height: 20),
                    RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.black,
                          height: 1.5,
                        ),
                        children: [
                          TextSpan(
                            text:
                                'Para continuar com a ${isToFinish ? 'finalização' : 'ativação'} do\n',
                          ),
                          const TextSpan(
                            text: 'Uni Card',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const TextSpan(
                            text: ', confirme sua biometria facial.',
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Botão Confirmar (desabilitado enquanto a biometria não for processada)
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        final User user = context.read<ProfileCubit>().user;
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => BiometryUnimed(
                              perfilAppsCredentials:
                                  BiometriaUtils.convertDefaultCredentials(
                                FlavorConfig
                                    .instance!.values.profilePermissions,
                              ),
                              codPrestador: user.codPrestador,
                              name: user.nome ?? '',
                              environment: FlavorConfig
                                  .instance!.values.validadeBioIdEnv,
                              maxHeight: 720,
                              maxQuality: 60,
                              cameraPreview: true,
                              theme: ThemeData(
                                useMaterial3: false,
                                primaryColor: cooperadoTealGreen,
                                colorScheme: ColorScheme.fromSwatch().copyWith(
                                  secondary: CooperadoColors.grayDark2,
                                ),
                                progressIndicatorTheme:
                                    const ProgressIndicatorThemeData(
                                  color: cooperadoTealGreen,
                                ),
                                elevatedButtonTheme: ElevatedButtonThemeData(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: cooperadoTealGreen,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                                fontFamily: 'UnimedSans',
                                primaryIconTheme: const IconThemeData(
                                    color: cooperadoTealGreen),
                              ),
                              onValid: () {
                                Navigator.pop(context);
                                if (isToFinish) {
                                  _showTransactionModalFinish(
                                    context: context,
                                    isToFinish: widget.isActivated,
                                  );
                                } else {
                                  _showTransactionModalActivateAndDeactivate(
                                    context: context,
                                    periodInHours: selectedPeriod!,
                                    isReactivated: isReactivated,
                                  );
                                }
                              },
                              onCancel: () {
                                Navigator.of(context).pop();
                              },
                              logger: BiometryLogger.biometryLogger,
                            ),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: CooperadoColors.tealGreen,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: const EdgeInsets.symmetric(
                            vertical: 15, horizontal: 30),
                      ),
                      child: const Text(
                        'Confirmar',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    const SizedBox(height: 10),

                    // Botão Cancelar
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: CooperadoColors.tealGreen,
                        padding: const EdgeInsets.symmetric(
                            vertical: 15, horizontal: 30),
                      ),
                      child: const Text(
                        'Cancelar',
                        style: TextStyle(
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showTransactionModalFinish(
      {required BuildContext context, bool isToFinish = false}) {
    final String codPrestador =
        context.read<ProfileCubit>().user.codPrestador.toString();
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(25.0)),
      ),
      builder: (BuildContext context) {
        return BlocProvider(
          create: (context) =>
              EcardFinishCubit()..finishEcard(codPrestador: codPrestador),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: BlocConsumer<EcardFinishCubit, EcardFinishState>(
              listener: (context, state) {
                if (state is LoadedEcardFinishState) {
                  Navigator.pop(context);
                  _showTransactionModalSuccess(
                    context: context,
                    isToFinish: true,
                  );
                }
              },
              builder: (context, state) {
                if (state is LoadingEcardFinishState) {
                  return const Center(
                    child: SpinKitThreeBounce(
                      color: CooperadoColors.tealGreen,
                      size: 30,
                    ),
                  );
                } else if (state is ErrorEcardFinishState) {
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 16.0),
                        child: Text(
                          '${isToFinish ? 'Finalizar' : 'Recusar'} Uni Card.',
                          style: const TextStyle(
                              fontSize: 24, fontWeight: FontWeight.bold),
                        ),
                      ),
                      SvgPicture.asset(
                        'assets/svg/icon_ecard_recuse.svg',
                        height: 85,
                      ),
                      const SizedBox(height: 20),
                      Text(
                        state.message,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: CooperadoColors.tealGreen,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          padding: const EdgeInsets.symmetric(
                              vertical: 15, horizontal: 30),
                        ),
                        child: Text(
                          'Ok'.toUpperCase(),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  );
                }

                return Container();
              },
            ),
          ),
        );
      },
    );
  }

  void _showTransactionModalActivateAndDeactivate({
    required BuildContext context,
    required int periodInHours,
    required bool isReactivated,
  }) {
    final String codPrestador =
        context.read<ProfileCubit>().user.codPrestador.toString();
    showModalBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(25.0)),
      ),
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: BlocProvider(
            create: (context) => EcardActivationCubit()
              ..activatedAndReactivatedEcard(
                codPrestador: codPrestador,
                periodInHours: periodInHours,
                isReactivated: isReactivated,
              ),
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: BlocConsumer<EcardActivationCubit, EcardActivationState>(
                listener: (context, state) {
                  if (state is LoadedEcardActivationState) {
                    context
                        .read<TimerEcardCubit>()
                        .setStateTimerEcard(isTimerRunner: false);
                    context
                        .read<EcardActivationCheckCubit>()
                        .ecardActivationCheck(
                          codPrestador: context
                              .read<ProfileCubit>()
                              .user
                              .codPrestador
                              .toString(),
                        );
                    Navigator.pop(context);
                    _showTransactionModalSuccess(context: context);
                  }
                },
                builder: (context, state) {
                  if (state is LoadingEcardActivationState) {
                    return const Center(
                      child: SpinKitThreeBounce(
                        color: CooperadoColors.tealGreen,
                        size: 30,
                      ),
                    );
                  } else if (state is ErrorEcardActivationState) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Padding(
                          padding: EdgeInsets.symmetric(vertical: 16.0),
                          child: Text(
                            'Ativação Uni Card.',
                            style: TextStyle(
                                fontSize: 24, fontWeight: FontWeight.bold),
                          ),
                        ),
                        SvgPicture.asset(
                          'assets/svg/icon_ecard_recuse.svg',
                          height: 85,
                        ),
                        const SizedBox(height: 20),
                        Text(
                          state.message,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 20),
                        ElevatedButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: CooperadoColors.tealGreen,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            padding: const EdgeInsets.symmetric(
                                vertical: 15, horizontal: 30),
                          ),
                          child: Text(
                            'Ok'.toUpperCase(),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    );
                  }

                  return Container();
                },
              ),
            ),
          ),
        );
      },
    );
  }

  void _showTransactionModalSuccess({
    required BuildContext context,
    bool isToFinish = false,
  }) {
    showModalBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(25.0)),
      ),
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: Text(
                    'Uni Card ${isToFinish ? 'Finalizado' : 'Ativado'}.',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SvgPicture.asset(
                  'assets/svg/icon_ecard_success.svg',
                  height: 85,
                ),
                const SizedBox(height: 20),
                RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.black,
                      height: 1.5,
                    ),
                    children: [
                      const TextSpan(text: 'Seu '),
                      const TextSpan(
                        text: 'Uni Card',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      TextSpan(
                        text:
                            ', foi ${isToFinish ? 'finalizado' : 'ativado'} com sucesso.',
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    if (isToFinish) {
                      Navigator.pop(context);
                      Navigator.pop(context);
                    } else {
                      Navigator.pop(context);
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: CooperadoColors.tealGreen,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    padding: const EdgeInsets.symmetric(
                      vertical: 15,
                      horizontal: 30,
                    ),
                  ),
                  child: const Text(
                    'Confirmar',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showTransactionModalTimePeriod(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Permite que o modal se ajuste melhor
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(25.0)),
      ),
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Wrap(
              alignment: WrapAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          'Selecione o período de ${widget.isReactivated ? 'reativação' : 'ativação'}:',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 20),

                        // Lista de opções de período
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: 4, // 4 períodos de 1 a 4 horas
                          itemBuilder: (context, index) {
                            int period = index + 1;
                            return GestureDetector(
                              onTap: () {
                                setState(() {
                                  selectedPeriod = period;
                                });
                              },
                              child: Container(
                                margin: const EdgeInsets.only(bottom: 10),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    color: selectedPeriod == period
                                        ? CooperadoColors.tealGreen
                                        : Colors.grey,
                                    width: 2,
                                  ),
                                ),
                                child: ListTile(
                                  leading: const Icon(Icons.access_time),
                                  title: Text(
                                    '$period hora${period > 1 ? 's' : ''}',
                                    textAlign: TextAlign.center,
                                  ),
                                  trailing: Radio<int>(
                                    value: period,
                                    groupValue: selectedPeriod,
                                    onChanged: (int? value) {
                                      setState(() {
                                        selectedPeriod = value;
                                      });
                                    },
                                  ),
                                ),
                              ),
                            );
                          },
                        ),

                        const SizedBox(height: 20),

                        // Botões de ação
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            ElevatedButton(
                              onPressed: selectedPeriod != null
                                  ? () {
                                      Navigator.pop(context);
                                      _showTransactionModalBiometria(
                                        context: context,
                                        isReactivated: widget.isReactivated,
                                      );
                                    }
                                  : null,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: CooperadoColors.tealGreen,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                padding: const EdgeInsets.symmetric(
                                    vertical: 15, horizontal: 30),
                              ),
                              child: const Text(
                                'CONFIRMAR',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            TextButton(
                              onPressed: () {
                                Navigator.pop(context);
                              },
                              style: TextButton.styleFrom(
                                foregroundColor: CooperadoColors.tealGreen,
                                padding: const EdgeInsets.symmetric(
                                    vertical: 15, horizontal: 30),
                              ),
                              child: const Text(
                                'CANCELAR',
                                style: TextStyle(
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}

import 'package:cooperado_minha_unimed/models/ecard-models/history_transaction_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('HistoryTransactionEcardModelViewAddress', () {
    test('fromJson e toJson', () {
      final json = {
        "addressCode": 817862,
        "streetTypeCode": "AV",
        "streetCode": 9041,
        "streetTypeName": "AVENIDA",
        "streetName": "SANTOS DUMONT",
        "addressNumber": 3131,
        "addressComplement": "sl 316",
        "neighborhoodCode": 3,
        "neighborhoodName": "ALDEOTA",
        "postalCode": 60150161,
        "cityCode": 9533,
        "cityName": "FORTALEZA",
        "stateCode": "CE",
        "stateName": "CEARA"
      };

      final address = HistoryTransactionEcardModelViewAddress.fromJson(json);
      expect(address.addressCode, 817862);
      expect(address.streetTypeCode, "AV");
      expect(address.streetCode, 9041);
      expect(address.streetTypeName, "AVENIDA");
      expect(address.streetName, "SANTOS DUMONT");
      expect(address.addressNumber, 3131);
      expect(address.addressComplement, "sl 316");
      expect(address.neighborhoodCode, 3);
      expect(address.neighborhoodName, "ALDEOTA");
      expect(address.postalCode, 60150161);
      expect(address.cityCode, 9533);
      expect(address.cityName, "FORTALEZA");
      expect(address.stateCode, "CE");
      expect(address.stateName, "CEARA");

      final jsonResult = address.toJson();
      expect(jsonResult, json);
    });
  });

  group('HistoryTransactionEcardModel', () {
    test('fromJson e toJson', () {
      final json = {
        "codConsultEcad": 11,
        "codPrestador": 6358,
        "fullName": "MAC GONTEI",
        "addressCode": 817862,
        "validityDate": "2025-01-09T16:08:05.000Z",
        "startDate": "2025-01-09T15:08:05.000Z",
        "exclusionDate": "2025-01-10T13:00:47.000Z",
        "insertionDate": "2025-01-09T15:08:05.000Z",
        "insertionUser": "AUDIT_CONS",
        "viewAddress": {
          "addressCode": 817862,
          "streetTypeCode": "AV",
          "streetCode": 9041,
          "streetTypeName": "AVENIDA",
          "streetName": "SANTOS DUMONT",
          "addressNumber": 3131,
          "addressComplement": "sl 316",
          "neighborhoodCode": 3,
          "neighborhoodName": "ALDEOTA",
          "postalCode": 60150161,
          "cityCode": 9533,
          "cityName": "FORTALEZA",
          "stateCode": "CE",
          "stateName": "CEARA"
        }
      };

      final transaction = HistoryTransactionEcardModel.fromJson(json);
      expect(transaction.codConsultEcad, 11);
      expect(transaction.codPrestador, 6358);
      expect(transaction.fullName, "MAC GONTEI");
      expect(transaction.addressCode, 817862);
      expect(transaction.validityDate, "2025-01-09T16:08:05.000Z");
      expect(transaction.startDate, "2025-01-09T15:08:05.000Z");
      expect(transaction.exclusionDate, "2025-01-10T13:00:47.000Z");
      expect(transaction.insertionDate, "2025-01-09T15:08:05.000Z");
      expect(transaction.insertionUser, "AUDIT_CONS");
      expect(transaction.viewAddress?.addressCode, 817862);
      expect(transaction.viewAddress?.streetTypeCode, "AV");
      expect(transaction.viewAddress?.streetCode, 9041);
      expect(transaction.viewAddress?.streetTypeName, "AVENIDA");
      expect(transaction.viewAddress?.streetName, "SANTOS DUMONT");
      expect(transaction.viewAddress?.addressNumber, 3131);
      expect(transaction.viewAddress?.addressComplement, "sl 316");
      expect(transaction.viewAddress?.neighborhoodCode, 3);
      expect(transaction.viewAddress?.neighborhoodName, "ALDEOTA");
      expect(transaction.viewAddress?.postalCode, 60150161);
      expect(transaction.viewAddress?.cityCode, 9533);
      expect(transaction.viewAddress?.cityName, "FORTALEZA");
      expect(transaction.viewAddress?.stateCode, "CE");
      expect(transaction.viewAddress?.stateName, "CEARA");

      final jsonResult = transaction.toJson();
      expect(jsonResult, json);
    });
  });
}
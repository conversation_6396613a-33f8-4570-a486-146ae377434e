import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/add_file/add_file_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/add_file/add_file_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/glosa_resource/glosa_resource.model.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/info_row.dart';
import 'package:cooperado_minha_unimed/screens/servicos/status-solicitacao-participacao/file_viewer.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:image_picker/image_picker.dart';

final logger = UnimedLogger(className: 'DocumentsPage');

class AddFileScreen extends StatefulWidget {
  final GlosaResourceData glosaResourceData;

  const AddFileScreen({super.key, required this.glosaResourceData});

  @override
  AddFileScreenState createState() => AddFileScreenState();
}

class AddFileScreenState extends State<AddFileScreen> {
  final picker = ImagePicker();

  bool enableAddFilesButtons = true;
  bool enableSendButtons = false;

  bool loading = false;
  String? loadingMessage;

  PageController controller = PageController(
    initialPage: 0,
    viewportFraction: 1 / 1.25,
  );

  @override
  void initState() {
    super.initState();

    BlocProvider.of<GlosaResourceAddFileCubit>(context)
        .loadFiles(sequencial: widget.glosaResourceData.sequencial.toString());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text("Anexar arquivos"),
        ),
        body:
            BlocListener<GlosaResourceAddFileCubit, GlosaResourceAddFileState>(
                listener: (context, state) {
                  if (state is LoadingDocumentState) {
                    setState(() {
                      loading = true;
                      loadingMessage = state.message;
                    });
                  } else if (state is FinishSendAllFileState) {
                    setState(() {
                      loading = false;
                    });

                    Alert.open(context,
                        title: 'Sucesso',
                        text: 'Arquivos enviados com sucesso',
                        textButtonClose: 'Fechar', callbackClose: () {
                      Navigator.pop(context);
                    });
                    //BlocProvider.of<GlosaResourceAddFileCubit>(context).add(NextPage());
                  } else if (state is GlosaResourceAttachmentErrorState) {
                    setState(() {
                      loading = false;
                    });
                    Alert.open(context,
                        title: 'Falha ao anexar arquivo',
                        text: state.message,
                        textButtonClose: 'Fechar', callbackClose: () {
                      BlocProvider.of<GlosaResourceAddFileCubit>(context)
                          .stopAttachFile();
                    });
                  } else if (state is DoneState) {
                    setState(() {
                      loading = false;
                    });
                  }
                },
                child: Container(
                  color: CooperadoColors.grayLight,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      header(),
                      //specialCharsPanel(context),
                      Expanded(child: _listFiles(context)),
                      _footer(),
                    ],
                  ),
                )));
  }

  Widget header() {
    return Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InfoRow(
                label: "Descrição",
                value: widget.glosaResourceData.descricaoServico.toString(),
                orientation: InfoRowOrientation.vertical,
              ),
              const SizedBox(height: 10),
              InfoRow(
                label: "Guia geradora",
                value: widget.glosaResourceData.guiaReferencia.toString(),
                orientation: InfoRowOrientation.vertical,
              ),
              const SizedBox(height: 10),
              InfoRow(
                label: "Codigo do serviço",
                value: widget.glosaResourceData.codServico.toString(),
                orientation: InfoRowOrientation.vertical,
              ),
            ],
          ),
        ));
  }

  Widget _footer() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: TextButton(
          style: TextButton.styleFrom(
              backgroundColor: enableSendButtons == false
                  ? CooperadoColors.grayLight8
                  : CooperadoColors.greenWhite,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5),
                side: BorderSide(
                    color: enableSendButtons == false
                        ? CooperadoColors.grayLight3
                        : CooperadoColors.greenDark5),
              )),
          onPressed: enableSendButtons && !loading
              ? () {
                  BlocProvider.of<GlosaResourceAddFileCubit>(context)
                      .sendAttachedFiles(
                          guide: widget.glosaResourceData.guiaReferencia
                              .toString(),
                          sequencial:
                              widget.glosaResourceData.sequencial.toString());
                }
              : null,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: loading
                ? Text(loadingMessage ?? "",
                    style: TextStyle(
                        fontSize: 16,
                        color: enableSendButtons == false
                            ? CooperadoColors.grayDark
                            : CooperadoColors.greenDark2,
                        fontWeight: FontWeight.bold))
                : Text("Enviar arquivos",
                    style: TextStyle(
                        fontSize: 16,
                        color: enableSendButtons == false
                            ? CooperadoColors.grayDark
                            : CooperadoColors.greenDark2,
                        fontWeight: FontWeight.bold)),
          ),
        ),
      ),
    );
  }

  Widget _listFiles(BuildContext context) {
    return BlocConsumer<GlosaResourceAddFileCubit, GlosaResourceAddFileState>(
      listener: (context, state) {
        if (state is DoneState) {
          _isValid();
        } else if (state is ErrorState) {
          Alert.open(context,
              title: state.message,
              text: state.message,
              textButtonClose: 'Fechar', callbackClose: () {
            BlocProvider.of<GlosaResourceAddFileCubit>(context)
                .stopAttachFile();
          });
        } else if (state is ErrorFileLoadingState) {
          loading = false;
        } else if (state is ErrorSendedState) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text(
                  "Erro ao enviar arquivo ${BlocProvider.of<GlosaResourceAddFileCubit>(context).attachments[state.index]?.name ?? ""}")));
        }
      },
      builder: (context, state) {
        if (state is LoadingState) {
          return const Center(
              child: Column(mainAxisSize: MainAxisSize.min, children: <Widget>[
            SpinKitThreeBounce(
              color: CooperadoColors.tealGreen,
              size: 24,
            ),
            Text('Carregando documentos na lista...'),
          ]));
        } else if (state is ErrorFileLoadingState) {
          return RefreshIndicator(
            onRefresh: () async {
              BlocProvider.of<GlosaResourceAddFileCubit>(context).loadFiles(
                  sequencial: widget.glosaResourceData.sequencial.toString());
            },
            child: ListView(
              padding: const EdgeInsets.only(top: 8.0),
              physics: const ClampingScrollPhysics(
                  parent: AlwaysScrollableScrollPhysics()),
              children: [ErrorBanner(message: state.message)],
            ),
          );
        } else {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: PageView.builder(
              itemCount: BlocProvider.of<GlosaResourceAddFileCubit>(context)
                  .attachments
                  .length,
              controller: controller,
              itemBuilder: (context, index) {
                return FileViewer(
                    fileAttach:
                        BlocProvider.of<GlosaResourceAddFileCubit>(context)
                            .attachments[index],
                    index: index);
              },
            ),
          );
        }
      },
    );
  }

  blockCameraButton() {
    setState(() {
      enableAddFilesButtons = true;
    });
  }

  _isValid() {
    final files =
        BlocProvider.of<GlosaResourceAddFileCubit>(context).attachments.where(
              (element) => element != null && element.sended != true,
            );

    setState(() {
      enableSendButtons = files.isNotEmpty;
    });
  }
}

import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';

part 'register_state.dart';

class RegisterCubit extends Cubit<RegisterState> {
  RegisterCubit() : super(RegisterInitial());

  Future<void> sendRegister(
      {required String name,
      required String birthDate,
      required String email,
      required String crm,
      required String cpf,
      required String password,
      required String confirmPassword,
      required bool terms}) async {
    try {
      emit(LoadingRegisterState());

      if (password == confirmPassword) {
        final response = await Locator.instance!<AuthApi>().sendRegister(
          name: name,
          birthDate: birthDate,
          crm: crm,
          cpf: cpf,
          email: email,
          password: password,
          terms: terms,
          confirmPassword: confirmPassword,
        );
        emit(DoneRegisterState(response));
      } else {
        emit(const ErrorRegisterState('Senhas não correspondem'));
      }
    } catch (e) {
      emit(ErrorRegisterState(e.toString()));
    }
  }
}

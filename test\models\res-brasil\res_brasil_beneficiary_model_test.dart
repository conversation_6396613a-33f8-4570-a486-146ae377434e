import 'package:cooperado_minha_unimed/models/res/res_brasil_beneficiary_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ResBrazilBeneficiaryModel', () {
    test('fromJson - sucesso', () {
      final json = {
        'name': '<PERSON>',
        'socialName': '<PERSON>',
        'cpf': '123.456.789-00',
        'card': '1234567890',
        'permissions': [
          {'name': 'resExternalService', 'value': true},
          {'name': 'resExternalAllergy', 'value': false},
        ],
      };

      final model = ResBrazilBeneficiaryModel.fromJson(json);

      expect(model.name, '<PERSON>');
      expect(model.socialName, 'Johnny');
      expect(model.cpf, '123.456.789-00');
      expect(model.card, '1234567890');
      expect(model.permissions.length, 2);
      expect(model.permissions[0].name, 'resExternalService');
      expect(model.permissions[0].value, true);
      expect(model.permissions[1].name, 'resExternalAllergy');
      expect(model.permissions[1].value, false);
    });

    test('fromJson - erro', () {
      final json = {
        'name': '<PERSON> Doe',
        'cpf': '123.456.789-00',
        'card': '1234567890',
        // 'permissions' está faltando
      };

      final model = ResBrazilBeneficiaryModel.fromJson(json);

      expect(model.name, 'John Doe');
      expect(model.socialName, isNull);
      expect(model.cpf, '123.456.789-00');
      expect(model.card, '1234567890');
      expect(model.permissions, isEmpty);
    });

    test('toJson - sucesso', () {
      final model = ResBrazilBeneficiaryModel(
        name: 'John Doe',
        socialName: 'Johnny',
        cpf: '123.456.789-00',
        card: '1234567890',
        permissions: [
          PermissionsBeneficiaryModel(name: 'resExternalService', value: true),
          PermissionsBeneficiaryModel(name: 'resExternalAllergy', value: false),
        ],
      );

      final json = model.toJson();

      expect(json['name'], 'John Doe');
      expect(json['socialName'], 'Johnny');
      expect(json['cpf'], '123.456.789-00');
      expect(json['card'], '1234567890');
      expect(json['permissions'].length, 2);
      expect(json['permissions'][0]['name'], 'resExternalService');
      expect(json['permissions'][0]['value'], true);
      expect(json['permissions'][1]['name'], 'resExternalAllergy');
      expect(json['permissions'][1]['value'], false);
    });

    test('Permissões - sucesso', () {
      final model = ResBrazilBeneficiaryModel(
        name: 'John Doe',
        socialName: 'Johnny',
        cpf: '123.456.789-00',
        card: '1234567890',
        permissions: [
          PermissionsBeneficiaryModel(name: 'resExternalService', value: true),
          PermissionsBeneficiaryModel(name: 'resExternalAllergy', value: false),
          PermissionsBeneficiaryModel(
              name: 'resExternalProcedure', value: true),
          PermissionsBeneficiaryModel(
              name: 'resExternalDocument', value: false),
          PermissionsBeneficiaryModel(
              name: 'resExternalDiagnostic', value: true),
        ],
      );

      expect(model.resExternalService, true);
      expect(model.resExternalAllergy, false);
      expect(model.resExternalProcedure, true);
      expect(model.resExternalDocument, false);
      expect(model.resExternalDiagnostic, true);
    });

    test('Permissões - erro', () {
      final model = ResBrazilBeneficiaryModel(
        name: 'John Doe',
        socialName: 'Johnny',
        cpf: '123.456.789-00',
        card: '1234567890',
        permissions: [
          PermissionsBeneficiaryModel(name: 'resExternalService', value: true),
        ],
      );

      expect(model.resExternalService, true);
      expect(model.resExternalAllergy,
          false); // Permissão não existe, deve retornar false
      expect(model.resExternalProcedure,
          false); // Permissão não existe, deve retornar false
      expect(model.resExternalDocument,
          false); // Permissão não existe, deve retornar false
      expect(model.resExternalDiagnostic,
          false); // Permissão não existe, deve retornar false
    });
  });
}

import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/allergies/res_allergies_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/configs/res_configs_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/res/allergies/widgets/show_modal_allergies.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/app_bar_res.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/empty_list.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/erro_service.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/expandable_card.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/title_result.dart';
import 'package:cooperado_minha_unimed/shared/utils/router_observer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class ResInternalAllergiesScreen extends StatefulWidget {
  final String nameBeneficiary;
  final String beneficiaryCard;
  const ResInternalAllergiesScreen(
      {super.key,
      required this.nameBeneficiary,
      required this.beneficiaryCard});

  @override
  State<ResInternalAllergiesScreen> createState() =>
      _ResInternalAllergiesScreenState();
}

class _ResInternalAllergiesScreenState extends State<ResInternalAllergiesScreen>
    with RouteAware {
  List<ExpansionTileController> controllers = [];
  int? previusSelectedIndex;

  @override
  void initState() {
    context.read<ResAllergieCubit>().listResAllergies(
          crm: context.read<AuthCubit>().credentials.crm,
          card: widget.beneficiaryCard,
        );
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarRes(
        title: 'Alergias',
        nameBeneficiary: widget.nameBeneficiary,
      ),
      backgroundColor: CooperadoColors.backgroundWhiteColor,
      body: Padding(
        padding: const EdgeInsets.only(left: 20, right: 20, top: 10),
        child: BlocBuilder<ResAllergieCubit, ResAllergiesState>(
          builder: (context, state) {
            return Column(
              mainAxisAlignment: state is LoadingResAllergiesState ||
                      state is ErrorResAllergiesState
                  ? MainAxisAlignment.center
                  : MainAxisAlignment.start,
              crossAxisAlignment: state is LoadingResAllergiesState ||
                      state is ErrorResAllergiesState
                  ? CrossAxisAlignment.center
                  : CrossAxisAlignment.start,
              children: [
                if (state is! LoadingResAllergiesState &&
                    state is! ErrorResAllergiesState)
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            'Categorias',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: CooperadoColors.blackText,
                            ),
                          ),
                          IconButton(
                            iconSize: 14,
                            onPressed: () {
                              _showOptionsModal();
                            },
                            icon: const Icon(
                              Icons.info_outline,
                              color: CooperadoColors.greenDark,
                            ),
                          ),
                        ],
                      ),
                      const AllergySelection(),
                    ],
                  ),
                if (state is LoadingResAllergiesState)
                  const Center(
                      child: SpinKitCircle(color: CooperadoColors.tealGreen)),
                if (state is LoadedResAllergiesState)
                  if (state.listResAllergies.isEmpty)
                    const Expanded(
                      child: EmptyList(
                        pathIcon: 'assets/svg/icon_file.svg',
                        message: 'Nenhum registro encontrado no momento',
                      ),
                    )
                  else
                    Expanded(
                      child: Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(bottom: 20, top: 24),
                            child: TitleResult(
                              title: state.listResAllergies.length > 1
                                  ? 'Resultados '
                                  : 'Resultado ',
                              quantity: state.listResAllergies.length
                                  .toString()
                                  .padLeft(2, '0'),
                            ),
                          ),
                          Expanded(
                            child: ListView.builder(
                                physics: const ClampingScrollPhysics(
                                    parent: AlwaysScrollableScrollPhysics()),
                                itemCount: state.listResAllergies.length,
                                itemBuilder: (context, index) {
                                  ExpansionTileController controller =
                                      ExpansionTileController();

                                  controllers.add(controller);

                                  return Padding(
                                    padding: const EdgeInsets.only(bottom: 12),
                                    child: ExpandableCard(
                                      pathIcon:
                                          'assets/svg/icon_card_allergy.svg',
                                      controller: controllers[index],
                                      title: state
                                          .listResAllergies[index].alergeno,
                                      onExpansionChanged: (value) {
                                        setState(() {
                                          if (value &&
                                              previusSelectedIndex != index) {
                                            if (previusSelectedIndex != null) {
                                              controllers[previusSelectedIndex!]
                                                  .collapse();
                                            }
                                            previusSelectedIndex = index;
                                          }
                                        });
                                      },
                                      subtitle: state.listResAllergies[index]
                                          .dataTimeFormatted,
                                      additionalInfo: [
                                        {
                                          'title': 'Local',
                                          'description': state
                                              .listResAllergies[index].local
                                        },
                                        {
                                          'title': 'Categoria',
                                          'description': _getSelectedAllergies(
                                                  context: context,
                                                  alerfiesCode: state
                                                      .listResAllergies[index]
                                                      .categoria) ??
                                              state.listResAllergies[index]
                                                  .categoria
                                        },
                                      ],
                                    ),
                                  );
                                }),
                          ),
                        ],
                      ),
                    ),
                if (state is ErrorResAllergiesState)
                  ErroService(
                    message: state.message,
                    onPressed: () {
                      context.read<ResAllergieCubit>().listResAllergies(
                            crm: context.read<AuthCubit>().credentials.crm,
                            card: widget.beneficiaryCard,
                          );
                    },
                  ),
                if (state is NoDataResAllergiesState)
                  const Padding(
                    padding: EdgeInsets.only(top: 32.0),
                    child: Center(
                      child: EmptyList(
                        pathIcon: 'assets/svg/icon_file.svg',
                        message: 'Sem dados de alergias.',
                      ),
                    ),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }

  void _showOptionsModal() {
    showModalBottomSheet<void>(
      context: context,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      builder: (BuildContext context) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  height: 4,
                  width: 100,
                  margin: const EdgeInsets.symmetric(
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                    color: CooperadoColors.grayLight3,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const Padding(
                padding: EdgeInsets.only(top: 27),
                child: Text(
                  'Categorias de alergias',
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                    color: CooperadoColors.blackText,
                  ),
                ),
              ),
              const SizedBox(height: 24),
              ...context
                  .read<ResConfigCubit>()
                  .resConfigModel
                  .allergies
                  .categories!
                  .map(
                    (category) => Padding(
                      padding: const EdgeInsets.only(bottom: 10),
                      child: Row(
                        children: [
                          Text(
                            '${category.code!} : ',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: CooperadoColors.blackText,
                            ),
                          ),
                          Text(
                            category.description!,
                            style: const TextStyle(
                              fontSize: 14,
                              color: CooperadoColors.blackText,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
            ],
          ),
        );
      },
    );
  }

  String? _getSelectedAllergies(
      {required BuildContext context, required String alerfiesCode}) {
    String? allergies = context
        .read<ResConfigCubit>()
        .resConfigModel
        .allergies
        .categories!
        .firstWhere((element) => element.code == alerfiesCode)
        .description;

    return allergies;
  }
}

import 'package:flutter/material.dart';

import '../../../colors.dart';

class RefreshErrorBanner extends StatelessWidget {
  final String? message;
  final IconData? icon;
  final String? title;
  final Color backgroundColor;
  final Color? iconColor;
  final double? elevation;
  final Function onRefresh;
  const RefreshErrorBanner({
    super.key,
    required this.message,
    required this.onRefresh,
    this.icon,
    this.title,
    this.backgroundColor = Colors.white,
    this.iconColor,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
        onRefresh: () async {
          onRefresh();
        },
        child: SingleChildScrollView(
            physics: const ClampingScrollPhysics(
                parent: AlwaysScrollableScrollPhysics()),
            child: Card(
              color: backgroundColor,
              elevation: elevation ?? 0,
              child: Container(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Icon(
                      icon ?? Icons.report_problem_rounded,
                      size: 48,
                      color: iconColor ?? unimedOrange,
                    ),
                    const SizedBox(height: 2.0),
                    Text(title ?? "",
                        style: const TextStyle(fontWeight: FontWeight.bold),
                        textAlign: TextAlign.center),
                    const SizedBox(height: 4.0),
                    Text(message!, textAlign: TextAlign.center),
                  ],
                ),
              ),
            )));
  }
}

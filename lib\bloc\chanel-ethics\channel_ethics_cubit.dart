import 'package:cooperado_minha_unimed/bloc/chanel-ethics/channel_ethics_state.dart';
import 'package:cooperado_minha_unimed/shared/api/channel_ethics.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ChannelEthicsCubit extends Cubit<ChannelEthicsState> {
  ChannelEthicsCubit() : super(ChannelEthicsInital());

  Future<void> getChannelEthics() async {
    try {
      final servicesApi = Locator.instance!<ChannelEthicsApi>();
      final response = await servicesApi.getChannelEthics();
      emit(ChannelEthicsStateDone(channelResposive: response));
    } catch (ex) {
      debugPrint(ex.toString());
      emit(ChannelEthicsError('$ex'));
    }
  }
}

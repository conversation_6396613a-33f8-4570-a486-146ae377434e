{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Fortaleza DEV",
            "request": "launch",
            "type": "dart",
            "program": "lib/main.dart",
            "args": [
                "--verbose",
                "--flavor",
                "fortaleza",
                "--dart-define=CLIENT_ID=UNIMED_FORTALEZA",
            ]
        },
        {
            "name": "Fortaleza AppStore DEV",
            "request": "launch",
            "type": "dart",
            "program": "lib/main.dart",
            "args": [
                "--verbose",
                "--flavor",
                "fortaleza-appstore",
                "--dart-define=CLIENT_ID=UNIMED_FORTALEZA",
            ]
        },
        {
            "name": "PROD",
            "request": "launch",
            "type": "dart",
            "program": "lib/main_prod.dart",
            "args": [
                // "--release",
                "--verbose",
                "--flavor",
                "fortaleza",
                "--dart-define=CLIENT_ID=UNIMED_FORTALEZA",
            ]
        },
        {
            "name": "PROD Token",
            "request": "launch",
            "type": "dart",
            "program": "lib/main_prod.dart",
            "args": [
                "--dart-define",
                "token_forced=321872370e7ef84bf969fd6380172ebc",
                "--flavor",
                "fortaleza",
                "--dart-define=CLIENT_ID=UNIMED_FORTALEZA",
            ]
        },
        {
            "name": "Sobral DEV",
            "request": "launch",
            "type": "dart",
            "program": "lib/main_sobral.dart",
            "args": [
                "--verbose",
                "--flavor",
                "sobral",
                "--dart-define=CLIENT_ID=UNIMED_SOBRAL",
            ]
        },
        {
            "name": "Sobral PROD",
            "request": "launch",
            "type": "dart",
            "program": "lib/main_sobral_prod.dart",
            "args": [
                "--verbose",
                "--flavor",
                "sobral",
                "--dart-define=CLIENT_ID=UNIMED_SOBRAL",
            ]
        },
        {
            "name": "Cariri DEV",
            "request": "launch",
            "type": "dart",
            "program": "lib/main_cariri.dart",
            "args": [
                "--verbose",
                "--flavor",
                "cariri",
                "--dart-define=CLIENT_ID=UNIMED_CARIRI",
            ]
        },
        {
            "name": "Cariri PROD",
            "request": "launch",
            "type": "dart",
            "program": "lib/main_cariri_prod.dart",
            "args": [
                "--verbose",
                "--flavor",
                "cariri",
                "--dart-define=CLIENT_ID=UNIMED_CARIRI",
            ]
        },
        {
            "name": "Ceara DEV",
            "request": "launch",
            "type": "dart",
            "program": "lib/main_ceara.dart",
            "args": [
                "--verbose",
                "--flavor",
                "ceara",
                "--dart-define=CLIENT_ID=UNIMED_CEARA",
            ]
        },
        {
            "name": "Ceara PROD",
            "request": "launch",
            "type": "dart",
            "program": "lib/main_ceara_prod.dart",
            "args": [
                "--verbose",
                "--flavor",
                "ceara",
                "--dart-define=CLIENT_ID=UNIMED_CEARA",
            ]
        },
    ]
}
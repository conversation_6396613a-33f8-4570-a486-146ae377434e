class CidList {
  int? totalPaginas;
  int? pagina;
  Cid? cid;
  List<Cid>? listaCid;

  CidList({this.totalPaginas, this.pagina, this.cid, this.listaCid});

  CidList.fromJson(Map<String, dynamic> json) {
    totalPaginas = json['totalPaginas'];
    pagina = json['pagina'];
    cid = json['cid'] != null ? Cid.fromJson(json['cid']) : null;
    if (json['listaCid'] != null) {
      listaCid = [];
      json['listaCid'].forEach((v) {
        listaCid!.add(Cid.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['totalPaginas'] = totalPaginas;
    data['pagina'] = pagina;
    if (cid != null) {
      data['cid'] = cid!.toJson();
    }
    if (listaCid != null) {
      data['listaCid'] = listaCid!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Cid {
  String? codigo;
  String? descricao;

  Cid({this.codigo, this.descricao});

  Cid.fromJson(Map<String, dynamic> json) {
    codigo = json['codigo'];
    descricao = json['descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codigo'] = codigo;
    data['descricao'] = descricao;
    return data;
  }
}

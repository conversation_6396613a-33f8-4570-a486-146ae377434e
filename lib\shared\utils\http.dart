import 'dart:convert';
import 'dart:io';

import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:http/io_client.dart';

class HttpUtils {
  /// bypass CERTIFICATE_VERIFY_FAILED in android
  static IOClient bypassInvalidCertificate() {
    final ioc = HttpClient();
    ioc.badCertificateCallback =
        (X509Certificate cert, String host, int port) => true;
    return IOClient(ioc);
  }

  static String getLostPasswordAuthorizationBasicPortal() {
    return 'Basic ${base64Encode(utf8.encode('${FlavorConfig.instance!.values.lostPasswordPortal.user}:${FlavorConfig.instance!.values.lostPasswordPortal.password}'))}';
  }
}

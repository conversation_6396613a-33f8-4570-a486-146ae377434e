import 'package:auto_size_text/auto_size_text.dart';
import 'package:cooperado_minha_unimed/bloc/res-internal/lab_exams/res_lab_exam_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res-internal/lab_exams_detail/res_lab_exam_detail_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/res-internal/lab_exam.model.dart';
import 'package:cooperado_minha_unimed/screens/res-internal/res_lab_exam_detail.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:url_launcher/url_launcher.dart';

class ResLabExamScreen extends StatefulWidget {
  final String card;
  const ResLabExamScreen({super.key, required this.card});

  @override
  State<ResLabExamScreen> createState() => _ResLabExamScreenState();
}

class _ResLabExamScreenState extends State<ResLabExamScreen> {
  final logger = UnimedLogger(className: '_ResLabExamScreenState');
  TextEditingController controllerSearch = TextEditingController();

  bool loadingDetail = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: CooperadoColors.grayLight3,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: BlocConsumer<ResLabExamCubit, ResLabExamState>(
          listener: (context, state) {},
          builder: (context, state) {
            if (state is LoadingResLabExamState) {
              return const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SpinKitCircle(color: CooperadoColors.tealGreen),
                  SizedBox(height: 10),
                  AutoSizeText(
                    'Buscando exames laboratoriais...',
                    style: TextStyle(
                      color: CooperadoColors.tealGreen,
                      fontSize: 12,
                    ),
                  ),
                ],
              );
            } else if (state is ErrorResLabExamState) {
              return Center(
                child: Text(
                  state.message,
                  textAlign: TextAlign.center,
                ),
              );
            } else if (state is LoadedResLabExamState) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.all(
                          Radius.circular(24),
                        ),
                      ),
                      child: _serchLabExam(),
                    ),
                  ),
                  Expanded(
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 20),
                          AutoSizeText(
                            '${state.listLabExams.length} ${state.listLabExams.length > 1 ? 'exames encontrados' : 'exame encontrado'}',
                            style: const TextStyle(
                              color: CooperadoColors.tealGreen,
                              fontSize: 18,
                            ),
                          ),
                          const SizedBox(height: 5),
                          Expanded(
                            child: ListView.builder(
                              physics: const ClampingScrollPhysics(
                                  parent: AlwaysScrollableScrollPhysics()),
                              itemCount: state.listLabExams.length,
                              itemBuilder: (context, index) {
                                final labExam = state.listLabExams[index];
                                return _buildLabExamItem(labExamModel: labExam);
                              },
                            ),
                          ),
                        ]),
                  ),
                ],
              );
            }
            return const Center();
          },
        ),
      ),
    );
  }

  Widget _serchLabExam() {
    return TextFormField(
      enableInteractiveSelection: false,
      autofocus: false,
      controller: controllerSearch,
      textInputAction: TextInputAction.search,
      style: const TextStyle(color: Colors.green),
      onChanged: (query) {
        debugPrint('query: $query');
        context.read<ResLabExamCubit>().searchListLabExams(
              searchKey: query,
            );
      },
      maxLines: 1,
      decoration: InputDecoration(
        hintText: 'Buscar exame',
        hintMaxLines: null,
        suffixIcon: IconButton(
          onPressed: () {
            controllerSearch.clear();
            context.read<ResLabExamCubit>().searchListLabExams(
                  searchKey: '',
                );
          },
          icon: const Icon(
            Icons.clear,
            size: 16,
          ),
        ),
        prefixIcon: const Icon(
          Icons.search,
          size: 24,
          color: CooperadoColors.green,
        ),
        contentPadding: const EdgeInsets.only(
          top: 12,
          left: 10,
          right: 10,
          bottom: 6,
        ),
        border: InputBorder.none,
      ),
    );
  }

  Widget _buildLabExamItem({required ResLabExamModel labExamModel}) {
    return Hero(
      tag: labExamModel.orderId,
      child: BlocProvider(
          create: (_) => ResLabExamDetailCubit(),
          child: Card(
            margin: const EdgeInsets.symmetric(vertical: 5),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildItemText(
                        title: 'Nome do paciente',
                        value: labExamModel.patientName,
                      ),
                      
                      _buildItemText(
                        title: 'Unidade',
                        value: labExamModel.unitName,
                      ),
                      _buildItemText(
                        title: 'Número do Pedido',
                        value: labExamModel.orderLabel,
                      ),
                      _buildItemText(
                        title: 'Data do pedido',
                        value: labExamModel.dateOrderFormatted,
                      ),
                      const SizedBox(height: 20),
                      _rowOptions(labExamModel),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ],
            ),
          )),
    );
  }

  Widget _buildItemText({required String title, required String? value}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AutoSizeText(
            '$title: ',
            style: const TextStyle(
              color: CooperadoColors.blackText,
              fontSize: 12,
            ),
          ),
          AutoSizeText(
            value ?? 'Não informado',
            style: const TextStyle(
              color: CooperadoColors.blackText,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _rowOptions(ResLabExamModel labExamModel) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        BlocConsumer<ResLabExamDetailCubit, ResLabExamDetailState>(
          listener: (context, state) {
            if (state is LoadingResLabExamDetailState) {
              setState(() {
                loadingDetail = true;
              });
            } else if (state is ErrorResLabExamDetailState) {
              setState(() {
                loadingDetail = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            } else if (state is LoadedResLabExamDetailState) {
              setState(() {
                loadingDetail = false;
              });
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ResLabExamDetailScreen(
                    card: widget.card,
                    orderId: labExamModel.orderId,
                    listLabExamDetails: state.listLabExamDetails,
                  ),
                ),
              );
            }
          },
          builder: (context, state) {
            if (loadingDetail) {
              return const SpinKitThreeBounce(
                color: CooperadoColors.tealGreen,
                size: 20,
              );
            }

            return InkWell(
              onTap: () => {
                context.read<ResLabExamDetailCubit>().listResLabExamDetails(
                      card: widget.card,
                      orderId: labExamModel.orderId,
                    ),
              },
              child: const Center(
                child: AutoSizeText(
                  'Ver detalhes',
                  style: TextStyle(
                    color: CooperadoColors.tealGreen,
                    fontSize: 14,
                    decoration: TextDecoration.underline,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            );
          },
        ),
        if (labExamModel.resultPdfUrl != null)
          InkWell(
            onTap: () => {
              _launchURL(labExamModel.resultPdfUrl!),
            },
            child: const Center(
              child: AutoSizeText(
                'Ver resultado',
                style: TextStyle(
                  color: CooperadoColors.tealGreen,
                  fontSize: 14,
                  decoration: TextDecoration.underline,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }

  Future _launchURL(String url) async {
    logger.i('launchURL - abrindo url $url');
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      throw 'Unable to open url : $url';
    }
  }
}

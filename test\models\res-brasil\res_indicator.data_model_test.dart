import 'package:cooperado_minha_unimed/models/res-internal/res_indicator_data.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('IndicatorDataModel', () {
    test('fromJson - sucesso', () {
      final json = {
        'id': 1,
        'unity': 'Unidade 1',
        'indicatorsData': [
          {'date': '2023-01-01T12:00:00', 'x': '10', 'y': '20'},
          {'date': '2023-01-02T12:00:00', 'x': '15', 'y': '25'},
        ],
      };

      final model = IndicatorDataModel.fromJson(json);

      expect(model.id, 1);
      expect(model.unity, 'Unidade 1');
      expect(model.indicatorsData.length, 2);
      expect(model.indicatorsData[0].date, '2023-01-01T12:00:00');
      expect(model.indicatorsData[0].x, '10');
      expect(model.indicatorsData[0].y, '20');
      expect(model.indicatorsData[1].date, '2023-01-02T12:00:00');
      expect(model.indicatorsData[1].x, '15');
      expect(model.indicatorsData[1].y, '25');
    });

    test('toJson - sucesso', () {
      final indicatorsData = [
        IndicatorsData(date: '2023-01-01T12:00:00', x: '10', y: '20'),
        IndicatorsData(date: '2023-01-02T12:00:00', x: '15', y: '25'),
      ];

      final model = IndicatorDataModel(
        id: 1,
        unity: 'Unidade 1',
        indicatorsData: indicatorsData,
      );

      final json = model.toJson();

      expect(json['id'], 1);
      expect(json['unity'], 'Unidade 1');
      expect(json['indicatorsData'].length, 2);
      expect(json['indicatorsData'][0]['date'], '2023-01-01T12:00:00');
      expect(json['indicatorsData'][0]['x'], '10');
      expect(json['indicatorsData'][0]['y'], '20');
      expect(json['indicatorsData'][1]['date'], '2023-01-02T12:00:00');
      expect(json['indicatorsData'][1]['x'], '15');
      expect(json['indicatorsData'][1]['y'], '25');
    });

    test('indicatorsDataSorted - sucesso', () {
      final indicatorsData = [
        IndicatorsData(date: '2023-01-02T12:00:00', x: '15', y: '25'),
        IndicatorsData(date: '2023-01-01T12:00:00', x: '10', y: '20'),
      ];

      final model = IndicatorDataModel(
        id: 1,
        unity: 'Unidade 1',
        indicatorsData: indicatorsData,
      );

      final sortedData = model.indicatorsDataSorted;

      expect(sortedData.length, 2);
      expect(sortedData[0].date, '2023-01-01T12:00:00');
      expect(sortedData[1].date, '2023-01-02T12:00:00');
    });
  });

  group('IndicatorsData', () {
    test('fromJson - sucesso', () {
      final json = {
        'date': '2023-01-01T12:00:00',
        'x': '10',
        'y': '20',
      };

      final data = IndicatorsData.fromJson(json);

      expect(data.date, '2023-01-01T12:00:00');
      expect(data.x, '10');
      expect(data.y, '20');
    });

    test('toJson - sucesso', () {
      final data = IndicatorsData(
        date: '2023-01-01T12:00:00',
        x: '10',
        y: '20',
      );

      final json = data.toJson();

      expect(json['date'], '2023-01-01T12:00:00');
      expect(json['x'], '10');
      expect(json['y'], '20');
    });

    test('dateFormatted - sucesso', () {
      final data = IndicatorsData(
        date: '2023-01-01T12:00:00',
        x: '10',
        y: '20',
      );

      final formattedDate = data.dateFormatted;

      expect(formattedDate, '01/01/2023 12:00');
    });

    test('dateTime - sucesso', () {
      final data = IndicatorsData(
        date: '2023-01-01T12:00:00',
        x: '10',
        y: '20',
      );

      final dateTime = data.dateTime;

      expect(dateTime, DateTime.parse('2023-01-01T12:00:00'));
    });
  });
}

import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/shared/api/notice.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/demostrativo_resultado.dart';
import 'package:equatable/equatable.dart';

part 'council_state.dart';

class CouncilCubit extends Cubit<CouncilState> {
  CouncilCubit() : super(CouncilInitial());
  List<Noticia> _listCouncilNews = [];
  List<Noticia> _listCouncilNewsSearch = [];
  int _pageCurrent = 0;

  getFiscalCouncil(
      {required List<String> categories,
      required int page,
      String search = ''}) async {
    emit(LoadingFiscalCouncilNewsState());
    try {
      final List<Noticia> listPortal = await Locator.instance!<NoticeApi>()
          .getNoticeByType(categories: categories, page: page);

      emit(DoneFiscalCouncilNewsState(list: listPortal));
    } catch (ex) {
      emit(ErrorFiscalCouncilNewsState('$ex'));
    }
  }

  getListNewsEventPagination(
      {required List<String> categories, required int page}) async {
    try {
      if (page == 1) {
        _clearNews();
        emit(LoadingFiscalCouncilNewsState());
      } else {
        emit(LoadingFiscalCouncilNewsStatePagination(list: _listCouncilNews));
      }
      if (page > _pageCurrent) {
        final List<Noticia> listPortal = await Locator.instance!<NoticeApi>()
            .getNoticeByType(categories: categories, page: page);
        _addItensPagination(newsResponse: listPortal, page: page);
        _pageCurrent = page;
      }

      emit(DoneFiscalCouncilNewsState(list: _listCouncilNews));
    } catch (ex) {
      emit(ErrorFiscalCouncilNewsState('$ex'));
    }
  }

  getListNewsSearch({String search = ''}) async {
    try {
      emit(LoadingFiscalCouncilNewsStatePagination(
          list: _listCouncilNewsSearch));

      _searchNewsEvent(search: search);
      await Future.delayed(const Duration(milliseconds: 100));
      emit(DoneFiscalCouncilNewsState(list: _listCouncilNewsSearch));
    } catch (ex) {
      emit(ErrorFiscalCouncilNewsState('$ex'));
    }
  }

  _clearNews() {
    _listCouncilNewsSearch = [];
    _listCouncilNews = [];
    _pageCurrent = 0;
  }

  _addItensPagination(
      {required List<Noticia> newsResponse, required int page}) {
    if (page > 1) {
      _listCouncilNews.addAll(newsResponse);
    } else {
      _listCouncilNews.clear();
      _listCouncilNews.addAll(newsResponse);
    }
  }

  _searchNewsEvent({required String search}) {
    _listCouncilNewsSearch.clear();
    if (search.isEmpty) {
      _listCouncilNewsSearch.addAll(_listCouncilNews);
    } else {
      for (Noticia element in _listCouncilNews) {
        if (element.titulo!.toUpperCase().contains(search.toUpperCase())) {
          _listCouncilNewsSearch.add(element);
        }
      }
    }
  }
}

import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/shared/api/notice.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/demostrativo_resultado.dart';
import 'package:equatable/equatable.dart';

part 'other_state.dart';

class OtherCubit extends Cubit<OtherState> {
  OtherCubit() : super(OtherInitial());
  List<Noticia> _listOtherNews = [];
  List<Noticia> _listOtherNewsSearch = [];
  int _pageCurrent = 0;
  getOtherNews(
      {required List<String> categories,
      required int page,
      String search = ''}) async {
    emit(LoadingOthersNewsState());
    try {
      final List<Noticia> listPortal = await Locator.instance!<NoticeApi>()
          .getNoticeByType(categories: categories, page: page);

      emit(DoneOthersNewsState(list: listPortal));
    } catch (ex) {
      emit(ErrorOthersNewsState('$ex'));
    }
  }

  getListNewsEventPagination(
      {required List<String> categories, required int page}) async {
    try {
      if (page == 1) {
        _clearNews();
        emit(LoadingOthersNewsState());
      } else {
        emit(LoadingOthersNewsStatePagination(list: _listOtherNews));
      }
      if (page > _pageCurrent) {
        final List<Noticia> listPortal = await Locator.instance!<NoticeApi>()
            .getNoticeByType(categories: categories, page: page);
        _addItensPagination(newsResponse: listPortal, page: page);
        _pageCurrent = page;
      }

      emit(DoneOthersNewsState(list: _listOtherNews));
    } catch (ex) {
      emit(ErrorOthersNewsState('$ex'));
    }
  }

  getListNewsSearch({String search = ''}) async {
    try {
      emit(LoadingOthersNewsStatePagination(list: _listOtherNewsSearch));

      _searchNewsEvent(search: search);
      await Future.delayed(const Duration(milliseconds: 100));
      emit(DoneOthersNewsState(list: _listOtherNewsSearch));
    } catch (ex) {
      emit(ErrorOthersNewsState('$ex'));
    }
  }

  _clearNews() {
    _listOtherNewsSearch = [];
    _listOtherNews = [];
    _pageCurrent = 0;
  }

  _addItensPagination(
      {required List<Noticia> newsResponse, required int page}) {
    if (page > 1) {
      _listOtherNews.addAll(newsResponse);
    } else {
      _listOtherNews.clear();
      _listOtherNews.addAll(newsResponse);
    }
  }

  _searchNewsEvent({required String search}) {
    _listOtherNewsSearch.clear();
    if (search.isEmpty) {
      _listOtherNewsSearch.addAll(_listOtherNews);
    } else {
      for (Noticia element in _listOtherNews) {
        if (element.titulo!.toUpperCase().contains(search.toUpperCase())) {
          _listOtherNewsSearch.add(element);
        }
      }
    }
  }
}

import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/fiscal-council/documents/internal-communications/main.dart';
import 'package:cooperado_minha_unimed/screens/fiscal-council/documents/report.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:flutter/material.dart';

class CardDocuments extends StatefulWidget {
  const CardDocuments({super.key});

  @override
  CardDocumentsState createState() => CardDocumentsState();
}

class CardDocumentsState extends State<CardDocuments> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Card(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const Padding(
              padding: EdgeInsets.all(8.0),
              child: Text(
                "Documentos fiscais",
                style:
                    TextStyle(color: CooperadoColors.tealGreen, fontSize: 16),
              ),
            ),
            ListTile(
              onTap: () {
                Navigator.push(context, FadeRoute(page: const ReportScreen()));
              },
              leading: Container(
                padding: const EdgeInsets.all(8.0),
                decoration: const BoxDecoration(
                  color: CooperadoColors.tealGreen,
                  borderRadius: BorderRadius.all(Radius.elliptical(50, 50)),
                ),
                child: const Icon(
                  Icons.folder,
                  color: CooperadoColors.grayLight,
                ),
              ),
              title: const Text('Relatório / Visita'),
            ),
            ListTile(
              onTap: () {
                Navigator.push(
                    context, FadeRoute(page: const InternalCommunicScreen()));
              },
              leading: Container(
                padding: const EdgeInsets.all(8.0),
                decoration: const BoxDecoration(
                  color: CooperadoColors.tealGreen,
                  borderRadius: BorderRadius.all(Radius.elliptical(50, 50)),
                ),
                child: const Icon(
                  Icons.folder,
                  color: CooperadoColors.grayLight,
                ),
              ),
              title: const Text('Comunicados Internos'),
            ),
          ],
        ),
      ),
    );
  }
}

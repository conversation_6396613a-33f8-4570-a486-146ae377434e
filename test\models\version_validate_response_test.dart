import 'package:cooperado_minha_unimed/models/version_validate.vo.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Test VersionValidateResponse', () {
    test('Constructor must assign correct values ​​to properties', () {
      const message = 'Message example';
      const lastVersion = '1.0.0';
      const version = '2.0.0';
      const force = true;
      const isOutOfDate = false;

      final response = VersionValidateResponse(
        message: message,
        lastVersion: lastVersion,
        version: version,
        force: force,
        isOutOfDate: isOutOfDate,
      );

      expect(response.message, equals(message));
      expect(response.lastVersion, equals(lastVersion));
      expect(response.version, equals(version));
      expect(response.force, equals(force));
      expect(response.isOutOfDate, equals(isOutOfDate));
    });

    test('fromJson must create a valid instance of the class', () {
      final json = {
        'message': 'Message example',
        'lastVersion': '1.0.0',
        'version': '2.0.0',
        'force': true,
      };

      final response = VersionValidateResponse.fromJson(json);

      expect(response.message, equals(json['message']));
      expect(response.lastVersion, equals(json['lastVersion']));
      expect(response.version, equals(json['version']));
      expect(response.force, equals(json['force']));
    });

    test('toJson must return a valid map with the class values', () {
      final response = VersionValidateResponse(
        message: 'Mensagem de exemplo',
        lastVersion: '1.0.0',
        version: '2.0.0',
        force: true,
        isOutOfDate: false,
      );

      final json = response.toJson();

      expect(json['message'], equals(response.message));
      expect(json['lastVersion'], equals(response.lastVersion));
      expect(json['version'], equals(response.version));
      expect(json['force'], equals(response.force));
      expect(json['isOutOfDate'], equals(response.isOutOfDate));
    });
  });
}

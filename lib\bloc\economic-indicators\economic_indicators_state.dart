part of 'economic_indicators_cubit.dart';

abstract class EconomicIndicatorsState extends Equatable {
  const EconomicIndicatorsState();
}

class InitialEconomicIndicatorsState extends EconomicIndicatorsState {
  @override
  List<Object> get props => [];
}

class LoadingEconomicIndicatorsState extends EconomicIndicatorsState {
  @override
  List<Object> get props => [];
}

class LoadedEconomicIndicatorsState extends EconomicIndicatorsState {
  final List<EconomicIndicatorsModel> economicIndicators;

  @override
  List<Object?> get props => [];

  const LoadedEconomicIndicatorsState({required this.economicIndicators});
}

class ErrorEconomicIndicatorsState extends EconomicIndicatorsState {
  final String? message;
  @override
  List<Object?> get props => [message];

  const ErrorEconomicIndicatorsState(this.message);
}

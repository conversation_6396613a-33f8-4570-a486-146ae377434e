import 'dart:convert';

import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:crypto/crypto.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:intl/intl.dart';

void main() {
  late UserCredentials credential;
  late Map jsonCredential;
  User? userModel;
  Map? userJson;
  late String token;
  late String today;
  // CouncilTopics? modelTest2;

  setUpAll(
    () {
      FlavorConfig(flavor: Flavor.dev, values: FlavorDEV());
      credential = UserCredentials(crm: "12345", password: "123456");
      today = DateFormat('dd/MM/yyyy').format(DateTime.now());

      final password =
          sha256.convert(utf8.encode(credential.password!)).toString();
      token = md5
          .convert(utf8.encode('${credential.crm}|$password|$today'))
          .toString();
      jsonCredential = {"crm": "crm", "password": "123456"};
      userModel = User(
        email: "<EMAIL>",
        especialidades: [
          Especialidades(
            codigo: 12,
            descricao: "descricao",
            qualificacao: "qualificacao",
            qualificacoes: "qualificacoes",
            especialidadePrincipal: "especialidadePrincipal",
          ),
        ],
        crm: "123 ",
        cpf: 123456789,
        dataNascimento: "01/01/2000",
        nome: "nome",
        codPrestador: "codPrestador",
        FCMUserId: "onesignalExternalUserId",
      );
      userJson = {
        "email": "<EMAIL>",
        "especialidades": [
          {
            "codigo": 250,
            "descricao": "GINECOLOGIA E OBSTETRÍCIA",
            "qualificacoes": null,
            "qualificacao": null,
            "especialidadePrincipal": "S"
          },
          {
            "codigo": 930,
            "descricao": "COLPOSCOPIA (SADT)",
            "qualificacoes": null,
            "qualificacao": null,
            "especialidadePrincipal": "N"
          }
        ],
        "crm": "5003 CE",
        "cpf": 36835404353,
        "dataNascimento": "19/03/1963",
        "nome": "MARIA JOSE CABRAL",
        "codPrestador": "5003",
        "onesignalExternalUserId": "6369081e21840e0011ac9489",
        "configs": {
          "transparencia": {
            "nome": "Marcos Antônio Aragão de Macedo",
            "texto":
                "Caro(a) Médico(a) Cooperado(a),\n\nSeja bem-vindo(a) ao Portal da Transparência. Nele, você receberá de forma mais simples, ágil e eficiente informações importantes para acompanhamento periódico do desempenho da Unimed Fortaleza. Portanto, é importante lembrá-lo(a) que, ao disponibilizarmos esse canal direto, será da responsabilidade de cada Cooperado(a) o compromisso com informações que só dizem respeito à Unimed Fortaleza e aos seus Cooperados(as). Tais informações não deverão, em hipótese alguma, serem repassadas e/ou utilizadas por terceiros, ficando cada usuário dessa página de acesso restrito responsável por sua própria conduta, em conformidade com o inciso XI do art. 7 do estatuto social.\n\nTransparência na gestão e participação do Cooperado, esse é o plano.",
            "cargo": "Presidente da Unimed Fortaleza",
            "assinaturaUrl":
                "https://firebasestorage.googleapis.com/v0/b/minha-unimed-cooperado.appspot.com/o/transparencia%2Fassinatura-transparencia-presidente.png?alt=media&token=f9b24be9-59f5-4cb0-8f4c-e908cf8a78ab"
          }
        }
      };
    },
  );

  group(
    "Other tests",
    () {
      test("Should be return token user", () async {
        expect(await User.createToken(credentials: credential), token);
      });

      test("Should be return User empty", () {
        expect(User.empty, isInstanceOf<User>());
      });

      test("Should be return crmOnlyNumbers", () {
        expect(userModel!.crmOnlyNumbers, "123");
      });
    },
  );

  group(
    "isInstanceOf UserCredentials model tests",
    () {
      test("Should be return instance of UserCredentials", () {
        expect(credential, isInstanceOf<UserCredentials>());
      });

      test("Should be return instance of Credential String Crm", () {
        expect(credential.crm, isInstanceOf<String>());
      });

      test('Crm not empty and return a UserCredentials', () {
        expect(() => UserCredentials(crm: ''), throwsAssertionError);
        expect(UserCredentials(crm: '1713'), isA<UserCredentials>());
      });

      test("Should be return instance of Credential String Crm is not null",
          () {
        expect(credential.crm, isNotNull);
      });

      test("Should be return instance of Credential String Password", () {
        expect(credential.password, isInstanceOf<String>());
      });

      test(
          "Should be return instance of Credential String Password is not null",
          () {
        expect(credential.password, isNotNull);
      });
      test("Should be return instance of User", () {
        expect(userModel, isInstanceOf<User>());
      });

      test("Should be return instance of user String", () {
        expect(userModel!.crm, isInstanceOf<String>());
      });
      test("Should be return instance of user Especialidades", () {
        expect(userModel!.especialidades![0], isInstanceOf<Especialidades>());
      });
    },
  );

  group("Json test ", () {
    test("Should be return instance of UserCredentials from json", () {
      expect(UserCredentials.fromJson(jsonCredential),
          isInstanceOf<UserCredentials>());
    });

    test("Should be return instance of jsonCredential to json", () {
      expect(credential.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });

    test("Should be return instance of User to json", () {
      expect(userModel!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of User from json", () {
      final json = userModel!.toJson();
      expect(User.fromJson(json), isInstanceOf<User>());
    });
    test("Should be return instance of Especialidades to json", () {
      expect(userModel!.especialidades![0].toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of Especialidades from json", () {
      final json = userModel!.especialidades![0].toJson();
      expect(Especialidades.fromJson(json), isInstanceOf<Especialidades>());
    });
  });

  group("Json test", () {
    test("test type", () {
      expect(userJson!["email"], isInstanceOf<String>());
      expect(userJson!["crm"], isInstanceOf<String>());
      expect(userJson!["cpf"], isInstanceOf<int>());
      expect(userJson!["dataNascimento"], isInstanceOf<String>());
      expect(userJson!["nome"], isInstanceOf<String>());
      expect(userJson!["codPrestador"], isInstanceOf<String>());
      expect(userJson!["onesignalExternalUserId"], isInstanceOf<String>());
      expect(userJson!["especialidade"], isInstanceOf<List?>());
      if (userJson!["especialidade"] != null) {
        expect(userJson!["especialidade"]![0]["codigo"], isInstanceOf<int>());
        expect(userJson!["especialidade"]![0]["descricao"],
            isInstanceOf<String?>());
        expect(userJson!["especialidade"]![0]["qualificacoes"],
            isInstanceOf<String?>());
        expect(userJson!["especialidade"]![0]["qualificao"],
            isInstanceOf<String?>());
        expect(userJson!["especialidade"]![0]["especialidadePrincipal"],
            isInstanceOf<String?>());
      }
      expect(userJson!["configs"], isInstanceOf<Map?>());
      expect(userJson!["configs"]["transparencia"], isInstanceOf<Map?>());
      expect(userJson!["configs"]["transparencia"]["nome"],
          isInstanceOf<String?>());
      expect(userJson!["configs"]["transparencia"]["texto"],
          isInstanceOf<String?>());
      expect(userJson!["configs"]["transparencia"]["cargo"],
          isInstanceOf<String?>());
      expect(userJson!["configs"]["transparencia"]["assinaturaUrl"],
          isInstanceOf<String?>());
    });

    test("Should be return true if is a valid url", () {
      expect(
          userJson!["configs"]["transparencia"]["assinaturaUrl"]
              .contains("https://"),
          true);
    });
  });

  group('Token model', () {
    test('createToken must return a valid token', () async {
      final credentials = UserCredentials(crm: '12345', password: 'senha123');

      final token = await User.createToken(credentials: credentials);

      expect(token, isNotEmpty);
    });

    test('isTokenInit should return false if token is not defined', () {
      final isInit = User.isTokenInit();

      expect(isInit, isFalse);
    });
  });
}

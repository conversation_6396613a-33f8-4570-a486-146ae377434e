import 'package:intl/intl.dart';

class ResProcedureDetailModel {
  late String nomeProcedimento;
  late String nomeMedico;
  late String dataProcedimento;
  late String dataEntrada;
  String? dataAlta;
  late String codigoAtendimentoEncode;

  ResProcedureDetailModel({
    required this.nomeProcedimento,
    required this.nomeMedico,
    required this.dataProcedimento,
  });

  ResProcedureDetailModel.fromJson(Map<String, dynamic> json) {
    nomeProcedimento = json['nomeProcedimento'];
    nomeMedico = json['nomeMedico'];
    dataProcedimento = json['dataProcedimento'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['nomeProcedimento'] = nomeProcedimento;
    data['nomeMedico'] = nomeMedico;
    data['dataProcedimento'] = dataProcedimento;
    return data;
  }

  String get dataProcedimentoFormatted {
    final DateTime dateTime = DateTime.parse(dataProcedimento).toLocal();
    return DateFormat('dd/MM/yyyy HH:mm:ss').format(dateTime);
  }
}

import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/models/password_rules.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';

part 'forgot_password_state.dart';

class ForgotPasswordCubit extends Cubit<ForgotPasswordState> {
  ForgotPasswordCubit() : super(ForgotPasswordInitial());
  Future<void> recoverPassword({required String crm}) async {
    try {
      emit(LoadingForgotPasswordState());

      final response = await Locator.instance!<AuthApi>().recoverPassword(
        crm: crm,
      );

      emit(SucessForgotPassworState(response));
    } catch (e) {
      emit(ErrorForgotPasswordState('$e'));
    }
  }

  Future<void> getPasswordRules({
    required bool isAnonymous,
  }) async {
    try {
      emit(LoadingGetPasswordRulesState());

      final response = await Locator.instance!<AuthApi>().getPasswordRules(
        isAnonymous: isAnonymous,
      );

      if (response != null && response.isNotEmpty) {
        emit(DoneGetPasswordRulesState(rules: response));
      } else {
        emit(
          const ErrorRulesState(
              "Não foi possível acessar esse serviço, tente novamente mais tarde."),
        );
      }
    } catch (e) {
      emit(
        const ErrorRulesState(
            "Não foi possível acessar esse serviço, tente novamente mais tarde."),
      );
    }
  }
}

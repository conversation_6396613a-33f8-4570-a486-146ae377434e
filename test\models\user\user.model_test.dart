import 'package:flutter_test/flutter_test.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';

void main() {
  group('User Model', () {
    test('Deve criar um User a partir de um JSON', () {
      // JSON de exemplo
      final json = {
        'email': '<EMAIL>',
        'crm': '123456',
        'cpf': 12345678901,
        'dataNascimento': '01/01/1990',
        'nome': 'Teste Usuário',
        'codPrestador': '001',
        'FCMUserId': 'fcm_user_id',
        'token': 'token123',
        'especialidades': [
          {
            'codigo': 1,
            'descricao': 'Cardiologia',
            'qualificacoes': 'Especialista',
            'qualificacao': 'Médico',
            'especialidadePrincipal': 'Sim',
          },
        ],
      };

      // Criação do objeto User a partir do JSON
      final user = User.fromJson(json);

      // Verificações
      expect(user.email, '<EMAIL>');
      expect(user.crm, '123456');
      expect(user.cpf, 12345678901);
      expect(user.dataNascimento, '01/01/1990');
      expect(user.nome, 'Teste Usuário');
      expect(user.codPrestador, '001');
      expect(user.FCMUserId, 'fcm_user_id');
      expect(user.token, 'token123');
      expect(user.especialidades!.length, 1);
      expect(user.especialidades![0].descricao, 'Cardiologia');
    });

    test('Deve converter um User para JSON corretamente', () {
      // Criação do objeto User
      final user = User(
        email: '<EMAIL>',
        crm: '123456',
        cpf: 12345678901,
        dataNascimento: '01/01/1990',
        nome: 'Teste Usuário',
        codPrestador: '001',
        FCMUserId: 'fcm_user_id',
        token: 'token123',
        especialidades: [
          Especialidades(
            codigo: 1,
            descricao: 'Cardiologia',
            qualificacoes: 'Especialista',
            qualificacao: 'Médico',
            especialidadePrincipal: 'Sim',
          ),
        ],
      );

      // Conversão para JSON
      final json = user.toJson();

      // Verificações
      expect(json['email'], '<EMAIL>');
      expect(json['crm'], '123456');
      expect(json['cpf'], 12345678901);
      expect(json['dataNascimento'], '01/01/1990');
      expect(json['nome'], 'Teste Usuário');
      expect(json['codPrestador'], '001');
      expect(json['FCMUserId'], 'fcm_user_id');
      expect(json['token'], 'token123');
      expect(json['especialidades'].length, 1);
      expect(json['especialidades'][0]['descricao'], 'Cardiologia');
    });

    test('Deve retornar um User vazio com o método estático empty', () {
      // Criação de um User vazio
      final user = User.empty;

      // Verificações
      expect(user.email, '');
      expect(user.crm, '');
      expect(user.cpf, 00000000000);
      expect(user.dataNascimento, '');
      expect(user.nome, '');
      expect(user.codPrestador, '');
      expect(user.FCMUserId, '');
      expect(user.especialidades, isEmpty);
    });
  });
}
import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/add_file/add_file_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/glose_resource/glosa_resource_bloc.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/glose_resource/glosa_resource_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/glosa_resource/glosa_resource.model.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/info_row.dart';
import 'package:cooperado_minha_unimed/screens/servicos/status-solicitacao-participacao/add_file_screen.dart';
import 'package:cooperado_minha_unimed/screens/servicos/status-solicitacao-participacao/button_glosa_resource.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/vo/servicos/honorary_solicitation.vo.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:collection/collection.dart' show IterableExtension;

class DetailsSocitationScreen extends StatelessWidget {
  final HonorarySolicitation solicitation;

  const DetailsSocitationScreen({super.key, required this.solicitation});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Solicitação"),
        backgroundColor: CooperadoColors.tealGreenDark,
        actions: [
          if (solicitation.isGlosa)
            IconButton(
              onPressed: () {
                context.read<GlosaResourceCubit>().getGlosaResourceEvent(
                    crm: context.read<AuthCubit>().credentials.crm,
                    guide: solicitation.guiaReferencia?.toString() ?? '');
              },
              icon: const Icon(Icons.refresh),
            ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.only(left: 16.0, right: 16.0, top: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                const Text(
                  "Solicitação",
                  style:
                      TextStyle(color: CooperadoColors.tealGreen, fontSize: 16),
                ),
                const SizedBox(
                  height: 10,
                ),
                solicitation.numeroSolicitacao != null
                    ? InfoRow(
                        label: "Ticket",
                        value: solicitation.numeroSolicitacao!.toString(),
                        selectable: true,
                      )
                    : Container(),
                const SizedBox(
                  height: 10,
                ),
                solicitation.guiaReferencia != null
                    ? InfoRow(
                        label: "Nº da guia geradora (GIH)",
                        value: solicitation.guiaReferencia!.toString(),
                        selectable: true,
                      )
                    : Container(),
                const SizedBox(
                  height: 10,
                ),
                solicitation.nomePrestadorGuiaRef != null
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          InfoRow(
                              label: "Prestador",
                              value: solicitation.nomePrestadorGuiaRef!),
                          const SizedBox(
                            height: 10,
                          ),
                        ],
                      )
                    : Container(),
                solicitation.dataSolicitacao != null
                    ? InfoRow(
                        label: "Feita em", value: solicitation.dataSolicitacao!)
                    : Container(),
                const SizedBox(
                  height: 10,
                ),
                solicitation.observacaoPrestador != null
                    ? InfoRow(
                        label: "Motivo",
                        value: solicitation.observacaoPrestador!)
                    : Container(),
                const SizedBox(
                  height: 20,
                ),
                _patientData(),
                solicitation.situacao != null
                    ? Text(
                        "Situação: ${solicitation.situacao}",
                        style: const TextStyle(
                          color: CooperadoColors.tealGreen,
                          fontSize: 16,
                        ),
                      )
                    : Container(),
                const SizedBox(
                  height: 10,
                ),
                solicitation.dataAuditoria != null
                    ? InfoRow(
                        label: "Auditoria", value: solicitation.dataAuditoria!)
                    : Container(),
                const SizedBox(
                  height: 10,
                ),
                solicitation.numeroNota != null
                    ? InfoRow(
                        label: "Nº da nota",
                        value: solicitation.numeroNota!.toString())
                    : Container(),
                const SizedBox(
                  height: 10,
                ),
                solicitation.observacaoAuditor != null
                    ? InfoRow(
                        label: "Justificativa",
                        value: solicitation.observacaoAuditor!)
                    : Container(),
                const SizedBox(
                  height: 20,
                ),
                solicitation.servicosSolicitados != null
                    ? _tableServices(
                        servicos: solicitation.servicosSolicitados!)
                    : Container(),
                const SizedBox(
                  height: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _tableServices({required List<ServicosSolicitados> servicos}) {
    List<Widget> widgets = [];

    widgets.add(Column(
      children: <Widget>[
        const Divider(
          color: CooperadoColors.grayDark,
        ),
        Row(
          children: <Widget>[
            const Expanded(flex: 3, child: Text("Cod. serviço ")),
            Expanded(
              flex: 7,
              child: Container(
                decoration: const BoxDecoration(
                    border: Border(
                  left: BorderSide(width: 1.0, color: CooperadoColors.grayDark),
                )),
                child: const Padding(
                  padding: EdgeInsets.only(left: 10),
                  child: Text("Serviço"),
                ),
              ),
            )
          ],
        ),
        const Divider(
          color: CooperadoColors.grayDark,
        )
      ],
    ));

    for (ServicosSolicitados element in servicos) {
      widgets.add(const SizedBox(
        height: 10,
      ));
      widgets.add(Column(
        children: <Widget>[
          Row(
            children: <Widget>[
              Expanded(flex: 3, child: Text(element.codigo.toString())),
              Expanded(
                flex: 7,
                child: Container(
                  decoration: const BoxDecoration(
                      border: Border(
                    left:
                        BorderSide(width: 1.0, color: CooperadoColors.grayDark),
                  )),
                  child: Padding(
                    padding: const EdgeInsets.only(left: 10),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        _statusService(element),
                        Text(element.servico != null
                            ? element.servico!
                            : "Serviço não cadastrado"),
                        _numNota(element),
                        _quantityStatus(element),
                        _dateSolicitation(element),
                        _buttonREquestResource(element),
                      ],
                    ),
                  ),
                ),
              )
            ],
          ),
          // ButtonRequestResource(
          //   list: [],
          // )
        ],
      ));
    }

    return Column(children: widgets);
  }

  _quantityStatus(ServicosSolicitados servicosSolicitado) {
    if (!solicitation.isGlosa) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Qtd. solicitada: ${servicosSolicitado.quantSolicitada ?? "-"}"),
          Text("Qtd. auditada: ${servicosSolicitado.quantAuditada ?? "-"}"),
        ],
      );
    } else {
      return BlocBuilder<GlosaResourceCubit, GlosaResourceState>(
          builder: (context, state) {
        if (state is DoneGetGlosaResourceState) {
          GlosaResourceData? data = state.list.firstWhereOrNull((element) =>
              element.servicoDv == servicosSolicitado.codigo?.toString());

          if (data != null) {
            int? quantSolicitada =
                data.quantSolicitada == null || data.quantSolicitada == -1
                    ? servicosSolicitado.quantSolicitada
                    : data.quantSolicitada;
            int? quantAuditada =
                data.quantAuditada == null || data.quantAuditada == -1
                    ? servicosSolicitado.quantAuditada
                    : data.quantAuditada;

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text("Qtd. solicitada: ${quantSolicitada ?? "-"}"),
                Text("Qtd. auditada: ${quantAuditada ?? "-"}"),
              ],
            );
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                  "Qtd. solicitada: ${servicosSolicitado.quantSolicitada ?? "-"}"),
              Text("Qtd. auditada: ${servicosSolicitado.quantAuditada ?? "-"}"),
            ],
          );
        }

        return Container();
      });
    }
  }

  _numNota(ServicosSolicitados servicosSolicitado) {
    if (!solicitation.isGlosa) {
      return Container();
    } else {
      return BlocBuilder<GlosaResourceCubit, GlosaResourceState>(
          builder: (context, state) {
        if (state is DoneGetGlosaResourceState) {
          GlosaResourceData? data = state.list.firstWhereOrNull((element) =>
              element.servicoDv == servicosSolicitado.codigo?.toString());

          if (data != null) {
            if (data.numNota != null) {
              return SelectableText("Nº da nota: ${data.numNota}");
            }
          }
        }

        return Container();
      });
    }
  }

  _dateSolicitation(ServicosSolicitados servicosSolicitado) {
    if (!solicitation.isGlosa) {
      return Container();
    } else {
      return BlocBuilder<GlosaResourceCubit, GlosaResourceState>(
          builder: (context, state) {
        if (state is DoneGetGlosaResourceState) {
          GlosaResourceData? data = state.list.firstWhereOrNull((element) =>
              element.servicoDv == servicosSolicitado.codigo?.toString());

          if (data != null) {
            if (data.dataSolicitacaoRecurso != null) {
              return Text(
                  "Data solicitação de recurso: ${data.dataSolicitacaoRecurso}");
            }
          }
        }

        return Container();
      });
    }
  }

  _buttonREquestResource(ServicosSolicitados servicosSolicitado) {
    if (!solicitation.isGlosa) {
      return Container();
    }

    return BlocBuilder<GlosaResourceCubit, GlosaResourceState>(
      builder: (context, state) {
        if (state is LoadingGetGlosaResourceState) {
          return const SizedBox(
            height: 84,
            child: Center(
              child: SpinKitThreeBounce(
                color: CooperadoColors.greenDark2,
                size: 20,
              ),
            ),
          );
        } else if (state is DoneGetGlosaResourceState) {
          GlosaResourceData? data = state.list.firstWhereOrNull((element) =>
              element.servicoDv == servicosSolicitado.codigo?.toString());

          if (data != null) {
            return Row(
              children: [
                Expanded(
                  child: ButtonRequestResource(
                    glosaResourceData: data,
                  ),
                ),
                if (data.showAddFileButton)
                  IconButton(
                      onPressed: () {
                        BlocProvider.of<GlosaResourceAddFileCubit>(context)
                            .setFilesConfig(
                                maxFiles: data.maxFilesNumber,
                                maxFileSizeInMb: data.maxFileSizeinMb);

                        Navigator.push(
                          context,
                          FadeRoute(
                            page: AddFileScreen(
                              glosaResourceData: data,
                            ),
                          ),
                        );
                      },
                      icon: const Icon(
                        Icons.attach_file_outlined,
                        color: CooperadoColors.tealGreenDark,
                      )),
              ],
            );
          } else {
            return _buttonServiceNotFound(context, servicosSolicitado);
          }
        } else if (state is ErrorGlosaResourceState) {
          return const Center(
            child: Text(
              'Erro ao buscar dados da guia',
              style: TextStyle(color: CooperadoColors.redCancel),
            ),
          );
        }

        return Container();
      },
    );
  }

  _statusService(ServicosSolicitados servicosSolicitado) {
    return BlocBuilder<GlosaResourceCubit, GlosaResourceState>(
        builder: (context, state) {
      if (state is LoadingGetGlosaResourceState) {
        return const SizedBox(
          height: 38,
        );
      }
      if (state is DoneGetGlosaResourceState) {
        GlosaResourceData? data = state.list.firstWhereOrNull((element) =>
            element.servicoDv == servicosSolicitado.codigo?.toString());

        if (data != null && data.statusAtual != null) {
          return Column(
            children: [
              const SizedBox(
                height: 10,
              ),
              Row(
                children: [
                  const Text("Status: "),
                  Text(
                    "${data.statusAtual}",
                    style: const TextStyle(
                        color: CooperadoColors.tealGreen,
                        fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              if (data.justifAuditor != null)
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        "Justificativa: ${data.justifAuditor}",
                      ),
                    ),
                  ],
                ),
              const SizedBox(
                height: 10,
              ),
            ],
          );
        }
      }

      return Container();
    });
  }

  _patientData() {
    if (solicitation.nomeBeneficiario != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "Beneficiário",
            style: TextStyle(color: CooperadoColors.tealGreen, fontSize: 16),
          ),
          const SizedBox(height: 10),
          solicitation.nomeBeneficiario != null
              ? Row(
                  children: [
                    const Text("Nome: ",
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    SelectableText(
                      "${solicitation.nomeBeneficiario}",
                    )
                  ],
                )
              : Container(),
          const SizedBox(
            height: 10,
          ),
          solicitation.intercambio != null
              ? Row(
                  children: [
                    const Text("Intercâmbio: ",
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    Text("${solicitation.intercambioLabel}"),
                  ],
                )
              : Container(),
          const SizedBox(
            height: 10,
          ),
          solicitation.carteira != null
              ? Row(
                  children: [
                    const Text("Carteira: ",
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    SelectableText("${solicitation.carteira}"),
                  ],
                )
              : Container(),
          const SizedBox(
            height: 20,
          ),
        ],
      );
    } else {
      return Container();
    }
  }

  _buttonServiceNotFound(
      BuildContext context, ServicosSolicitados servicosSolicitado) {
    return Row(
      children: [
        Expanded(
          child: TextButton(
            style: TextButton.styleFrom(
                backgroundColor: CooperadoColors.greenWhite,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5),
                  side: const BorderSide(color: CooperadoColors.greenDark5),
                )),
            onPressed: () {
              Alert.open(
                context,
                title: "Atenção",
                text:
                    "${servicosSolicitado.codigo.toString()}\n${servicosSolicitado.servico.toString()}\n\nNão foi possível enviar recurso para este serviço. Por favor, entre em contato com Apoio ao Médico Cooperado para mais informações.",
              );
            },
            child: const Text("Solicitar recurso",
                style: TextStyle(
                    color: CooperadoColors.greenDark2,
                    fontWeight: FontWeight.bold)),
          ),
        ),
      ],
    );
  }
}

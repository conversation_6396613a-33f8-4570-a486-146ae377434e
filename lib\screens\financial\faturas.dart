import 'package:cooperado_minha_unimed/bloc/beneficiary/beneficiary_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/financial/invoice_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/financial/my_invoices_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/financial/card_fatura.dart';
import 'package:cooperado_minha_unimed/shared/widgets/change_card_button.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../bloc/sensitive-data/sensitive_data_state.dart';

class FaturasScreen extends StatefulWidget {
  const FaturasScreen({super.key});

  @override
  FaturasWidgetState createState() => FaturasWidgetState();
}

class FaturasWidgetState extends State<FaturasScreen> {
  int indexFatura = 0;
  bool loadingFatura = false;

  @override
  void initState() {
    BlocProvider.of<MyInvoicesCubit>(context).getAllFaturas(
      carteira:
          BlocProvider.of<BeneficiaryCubit>(context).selectedCard.carteira,
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          title: const Text('Minhas Faturas'),
          backgroundColor: CooperadoColors.tealGreenDark,
          actions: <Widget>[
            IconButton(
                       icon: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
              builder: (context, state) {
                return Icon(
                  state.isSensitiveDataVisible ? Icons.visibility : Icons.visibility_off,
                );
              },
                       ),
                       onPressed: () {
              context.read<SensitiveDataCubit>().toggleSensitiveDataVisibility();
                       },
                     ),
            Padding(
              padding: const EdgeInsets.only(right: 10),
              child: BlocListener<InvoiceCubit, InvoiceState>(
                  listener: (context, state) {
                    setState(() {
                      loadingFatura = state is LoadingInvoiceState;
                    });
                  },
                  child: IconButton(
                    icon: const Icon(Icons.refresh),
                    iconSize: 30,
                    onPressed: loadingFatura ? null : () => _updateScreen(),
                  )),
            ),
            ChangeCardButton(
              onUpdate: (beneficiaryCardModel) {
                BlocProvider.of<MyInvoicesCubit>(context).getAllFaturas(
                  carteira: beneficiaryCardModel.carteira,
                );
              },
            ),
          ],
        ),
        body: Container(
          color: Colors.white54,
          child: Column(
            children: <Widget>[
              BlocBuilder<MyInvoicesCubit, MyInvoicesState>(
                builder: (context, state) {
                  if (state is ErrorMyInvoicesState) {
                    return _errorGetAllFaturas(state.message);
                  } else if (state is NoDataMyInvoicesState) {
                    return Expanded(
                        child: RefreshIndicator(
                            onRefresh: () async {
                              BlocProvider.of<MyInvoicesCubit>(context)
                                  .getAllFaturas(
                                carteira:
                                    BlocProvider.of<BeneficiaryCubit>(context)
                                        .selectedCard
                                        .carteira,
                              );
                            },
                            color: CooperadoColors.tealGreen,
                            child: ListView(
                                padding: const EdgeInsets.only(top: 8.0),
                                physics: const ClampingScrollPhysics(
                                    parent: AlwaysScrollableScrollPhysics()),
                                children: const [
                                  Padding(
                                      padding: EdgeInsets.all(16),
                                      child: Expanded(
                                        child: Center(
                                            child: ErrorBanner(
                                          message: "Nenhum dado encontrado.",
                                          backgroundColor: Colors.transparent,
                                        )),
                                      ))
                                ])));
                  } else if (state is LoadingMyInvoicesState) {
                    return const Expanded(
                      child: Center(
                        child: SpinKitCircle(
                          color: CooperadoColors.tealGreen,
                        ),
                      ),
                    );

                    //RobertaLoading(color: UnimedColors.green);
                  } else if (state is LoadedMyInvoicesState) {
                    return Expanded(
                      child: RefreshIndicator(
                          onRefresh: () async {
                            _updateScreen();
                          },
                          color: CooperadoColors.tealGreen,
                          child: ListView.builder(
                              padding: const EdgeInsets.all(8.0),
                              itemCount: state.list.length,
                              itemBuilder: (context, index) {
                                final fatura = state.list.elementAt(index);

                                return CardFatura(invoice: fatura);
                              })),
                    );
                  } else {
                    return Container();
                  }
                },
              )
            ],
          ),
        ));
  }

  Widget _errorGetAllFaturas(String? error) {
    return Expanded(
        child: RefreshIndicator(
            onRefresh: () async {
              _updateScreen();
            },
            color: CooperadoColors.tealGreen,
            child: ListView(
                padding: const EdgeInsets.only(top: 8.0),
                physics: const ClampingScrollPhysics(
                    parent: AlwaysScrollableScrollPhysics()),
                children: [
                  Padding(
                      padding: const EdgeInsets.all(16),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Center(
                            child: ErrorBanner(
                          message: error ?? "Nenhum dados encontrado.",
                          backgroundColor: Colors.transparent,
                        )),
                      ))
                ])));
  }

  _updateScreen() {
    BlocProvider.of<MyInvoicesCubit>(context).getAllFaturas(
      carteira:
          BlocProvider.of<BeneficiaryCubit>(context).selectedCard.carteira,
    );
  }
}

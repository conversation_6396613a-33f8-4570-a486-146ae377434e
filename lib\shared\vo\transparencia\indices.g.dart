// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'indices.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VOIndicatorModel _$VOIndicatorModelFromJson(Map json) => VOIndicatorModel(
      data: (json['dados'] as List<dynamic>?)
          ?.map((e) => VODataModel.fromJson(e as Map))
          .toList(),
      last: json['ultimo'] == null
          ? null
          : VODataModel.fromJson(json['ultimo'] as Map),
    );

Map<String, dynamic> _$VOIndicatorModelToJson(VOIndicatorModel instance) =>
    <String, dynamic>{
      'dados': instance.data,
      'ultimo': instance.last,
    };

VODataModel _$VODataModelFromJson(Map json) => VODataModel(
      referenceMonth: json['mesReferencia'] as int?,
      referenceYear: json['anoReferencia'] as int?,
      referenceMonthLabel: json['mesReferenciaTexto'] as String?,
      totalValue: (json['valorAcumulado'] as num?)?.toDouble(),
      monthValue: (json['valorMensal'] as num?)?.toDouble(),
      projectedValue: (json['valorProjetada'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$VODataModelToJson(VODataModel instance) =>
    <String, dynamic>{
      'anoReferencia': instance.referenceYear,
      'mesReferencia': instance.referenceMonth,
      'mesReferenciaTexto': instance.referenceMonthLabel,
      'valorAcumulado': instance.totalValue,
      'valorMensal': instance.monthValue,
      'valorProjetada': instance.projectedValue,
    };

///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class HistoryTransactionEcardModelViewAddress {
/*
{
  "__typename": "Address",
  "addressCode": 817862,
  "streetTypeCode": "AV",
  "streetCode": 9041,
  "streetTypeName": "AVENIDA",
  "streetName": "SANTOS DUMONT",
  "addressNumber": 3131,
  "addressComplement": "sl 316",
  "neighborhoodCode": 3,
  "neighborhoodName": "ALDEOTA",
  "postalCode": 60150161,
  "cityCode": 9533,
  "cityName": "FORTALEZA",
  "stateCode": "CE",
  "stateName": "CEARA"
} 
*/

 // String? _Typename;
  int? addressCode;
  String? streetTypeCode;
  int? streetCode;
  String? streetTypeName;
  String? streetName;
  int? addressNumber;
  String? addressComplement;
  int? neighborhoodCode;
  String? neighborhoodName;
  int? postalCode;
  int? cityCode;
  String? cityName;
  String? stateCode;
  String? stateName;

  HistoryTransactionEcardModelViewAddress({
   // this._Typename,
    this.addressCode,
    this.streetTypeCode,
    this.streetCode,
    this.streetTypeName,
    this.streetName,
    this.addressNumber,
    this.addressComplement,
    this.neighborhoodCode,
    this.neighborhoodName,
    this.postalCode,
    this.cityCode,
    this.cityName,
    this.stateCode,
    this.stateName,
  });
  HistoryTransactionEcardModelViewAddress.fromJson(Map<String, dynamic> json) {
  //  _Typename = json['__typename']?.toString();
    addressCode = json['addressCode']?.toInt();
    streetTypeCode = json['streetTypeCode']?.toString();
    streetCode = json['streetCode']?.toInt();
    streetTypeName = json['streetTypeName']?.toString();
    streetName = json['streetName']?.toString();
    addressNumber = json['addressNumber']?.toInt();
    addressComplement = json['addressComplement']?.toString();
    neighborhoodCode = json['neighborhoodCode']?.toInt();
    neighborhoodName = json['neighborhoodName']?.toString();
    postalCode = json['postalCode']?.toInt();
    cityCode = json['cityCode']?.toInt();
    cityName = json['cityName']?.toString();
    stateCode = json['stateCode']?.toString();
    stateName = json['stateName']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
  //  data['__typename'] = _Typename;
    data['addressCode'] = addressCode;
    data['streetTypeCode'] = streetTypeCode;
    data['streetCode'] = streetCode;
    data['streetTypeName'] = streetTypeName;
    data['streetName'] = streetName;
    data['addressNumber'] = addressNumber;
    data['addressComplement'] = addressComplement;
    data['neighborhoodCode'] = neighborhoodCode;
    data['neighborhoodName'] = neighborhoodName;
    data['postalCode'] = postalCode;
    data['cityCode'] = cityCode;
    data['cityName'] = cityName;
    data['stateCode'] = stateCode;
    data['stateName'] = stateName;
    return data;
  }
}

class HistoryTransactionEcardModel {
/*
{
  "__typename": "ECardActivationItemData",
  "codConsultEcad": 11,
  "codPrestador": 6358,
  "fullName": "MAC GONTEI",
  "addressCode": 817862,
  "validityDate": "2025-01-09T16:08:05.000Z",
  "startDate": "2025-01-09T15:08:05.000Z",
  "exclusionDate": "2025-01-10T13:00:47.000Z",
  "insertionDate": "2025-01-09T15:08:05.000Z",
  "insertionUser": "AUDIT_CONS",
  "viewAddress": {
    "__typename": "Address",
    "addressCode": 817862,
    "streetTypeCode": "AV",
    "streetCode": 9041,
    "streetTypeName": "AVENIDA",
    "streetName": "SANTOS DUMONT",
    "addressNumber": 3131,
    "addressComplement": "sl 316",
    "neighborhoodCode": 3,
    "neighborhoodName": "ALDEOTA",
    "postalCode": 60150161,
    "cityCode": 9533,
    "cityName": "FORTALEZA",
    "stateCode": "CE",
    "stateName": "CEARA"
  }
} 
*/

 // String? _Typename;
  int? codConsultEcad;
  int? codPrestador;
  String? fullName;
  int? addressCode;
  String? validityDate;
  String? startDate;
  String? exclusionDate;
  String? insertionDate;
  String? insertionUser;
  HistoryTransactionEcardModelViewAddress? viewAddress;

  HistoryTransactionEcardModel({
 //   this._Typename,
    this.codConsultEcad,
    this.codPrestador,
    this.fullName,
    this.addressCode,
    this.validityDate,
    this.startDate,
    this.exclusionDate,
    this.insertionDate,
    this.insertionUser,
    this.viewAddress,
  });
  HistoryTransactionEcardModel.fromJson(Map<String, dynamic> json) {
  //  _Typename = json['__typename']?.toString();
    codConsultEcad = json['codConsultEcad']?.toInt();
    codPrestador = json['codPrestador']?.toInt();
    fullName = json['fullName']?.toString();
    addressCode = json['addressCode']?.toInt();
    validityDate = json['validityDate']?.toString();
    startDate = json['startDate']?.toString();
    exclusionDate = json['exclusionDate']?.toString();
    insertionDate = json['insertionDate']?.toString();
    insertionUser = json['insertionUser']?.toString();
    viewAddress = (json['viewAddress'] != null) ? HistoryTransactionEcardModelViewAddress.fromJson(json['viewAddress']) : null;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
  //  data['__typename'] = _Typename;
    data['codConsultEcad'] = codConsultEcad;
    data['codPrestador'] = codPrestador;
    data['fullName'] = fullName;
    data['addressCode'] = addressCode;
    data['validityDate'] = validityDate;
    data['startDate'] = startDate;
    data['exclusionDate'] = exclusionDate;
    data['insertionDate'] = insertionDate;
    data['insertionUser'] = insertionUser;
    if (viewAddress != null) {
      data['viewAddress'] = viewAddress!.toJson();
    }
    return data;
  }
}

// ignore_for_file: use_build_context_synchronously

import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/support-cooperative/support_cooperative_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/utils/url_launcher.utils.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

class SupportCooperativeScreen extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const SupportCooperativeScreen(
      {super.key, required this.analytics, required this.observer});

  @override
  State<SupportCooperativeScreen> createState() =>
      _SupportCooperativeScreenState();
}

class _SupportCooperativeScreenState extends State<SupportCooperativeScreen> {
  @override
  void initState() {
    super.initState();

    BlocProvider.of<SupportCooperativeCubit>(context).loadSupportCooperative(
        BlocProvider.of<AuthCubit>(context)
                .modelGeneralConfigModel
                .links
                ?.apoioCooperado ??
            "");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Apoio ao Cooperado'),
      ),
      body: BlocBuilder<SupportCooperativeCubit, SupportCooperativeState>(
        builder: (context, state) {
          if (state is LoadingSupportCooperativeState) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          } else if (state is LoadedSupportCooperativeState) {
            return SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              child: Container(
                color: CooperadoColors.grayLight3,
                padding: const EdgeInsets.all(12),
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Html(
                      data: state.content,
                      onLinkTap: (url, attributes, element) {
                        debugPrint('url: $url');
                        if (url != null) UrlLaucherUtils.launchURL(url);
                      },
                    ),
                  ),
                ),
              ),
            );
          } else if (state is ErrorSupportCooperativeState) {
            return Center(
              child: Text(state.message),
            );
          }
          return Container();
        },
      ),
    );
  }
}

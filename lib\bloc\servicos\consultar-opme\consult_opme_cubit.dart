import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/shared/api/services.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/servicos/opme_list.vo.dart';
import 'package:flutter/material.dart';

import 'consult_opme_state.dart';

class ConsultOpmeCubit extends Cubit<ConsultOpmeState> {
  ConsultOpmeCubit() : super(ConsultOpmeInitial());

  getConsultOpmeEvent(String keySearch, int? pagina) async {
    emit(LoadingGetConsultOpmeState());
    try {
      if (keySearch.isEmpty) emit(ConsultOpmeInitial());

      final OpmeList opmeList = await Locator.instance!<ServicesApi>()
          .getOpmeList(keySearch: keySearch, pagina: pagina);

      emit(DoneGetConsultOpmeState(opmeList));
    } catch (ex) {
      debugPrint(ex.toString());
      emit(ErrorGetConsultOpmeState('$ex'));
    }
  }

  setInitialState() async {
    emit(ConsultOpmeInitial());
  }
}

import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/shared/api/services.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';

part 'ir_declaration_state.dart';

class IRDeclarationCubit extends Cubit<IRDeclarationState> {
  IRDeclarationCubit() : super(IRDeclarationInitial());

  getListIRDeclarationEvent() async {
    emit(LoadingGetIRDeclarationState());
    try {
      final link = (await Locator.instance!<ServicesApi>().getIRDeclaration());

      emit(DoneGetIRDeclarationState(link));
    } catch (ex) {
      emit(ErrorGetIRDeclarationState('$ex'));
    }
  }
}

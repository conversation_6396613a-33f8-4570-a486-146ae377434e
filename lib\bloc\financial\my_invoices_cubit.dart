import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/models/invoice.model.dart';
import 'package:cooperado_minha_unimed/shared/api/financial.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';

part 'my_invoices_state.dart';

class MyInvoicesCubit extends Cubit<MyInvoicesState> {
  MyInvoicesCubit() : super(MyInvoicesInitial());

  getAllFaturas({required String carteira}) async {
    emit(LoadingMyInvoicesState());
    try {
      final listInvoice = await Locator.instance!<FinancialApi>()
          .getAllInvoice(carteira: carteira);

      if (listInvoice.isEmpty) {
        emit(const NoDataMyInvoicesState());
      } else {
        emit(LoadedMyInvoicesState(list: listInvoice));
      }
    } catch (ex) {
      emit(ErrorMyInvoicesState('$ex'));
    }
  }
}

part of 'invoice_cubit.dart';

abstract class InvoiceState extends Equatable {
  const InvoiceState();

  @override
  List<Object?> get props => [];
}

class FinancialInitial extends InvoiceState {}

class LoadingInvoiceState extends InvoiceState {
  final String dataReferencia;
  final int tipoRetorno;
  @override
  List<Object> get props => [dataReferencia, tipoRetorno];

  const LoadingInvoiceState(
      {required this.dataReferencia, required this.tipoRetorno});
}

class LoadedInvoicePdfState extends InvoiceState {
  final String pathFile;
  final String invoiceDate;
  @override
  List<Object?> get props => [pathFile, invoiceDate];
  const LoadedInvoicePdfState(
      {required this.pathFile, required this.invoiceDate});
}

class LoadedInvoiceBarCodeState extends InvoiceState {
  final String barCode;
  final String invoiceDate;

  @override
  List<Object?> get props => [barCode, invoiceDate];

  const LoadedInvoiceBarCodeState(
      {required this.barCode, required this.invoiceDate});
}

class NoDataInvoiceState extends InvoiceState {
  @override
  List<Object?> get props => [];
  const NoDataInvoiceState();
}

class ErrorInvoiceState extends InvoiceState {
  final String message;
  @override
  List<Object> get props => [message];

  const ErrorInvoiceState(this.message);
}

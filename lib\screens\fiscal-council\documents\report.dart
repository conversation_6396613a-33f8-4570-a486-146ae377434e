import 'package:cooperado_minha_unimed/bloc/fiscal-council/fiscal_council_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/fiscal-council/form-council/form_council_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/fiscal-council/documents/card_report.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/widgets/header_chart.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class ReportScreen extends StatefulWidget {
  const ReportScreen({super.key});

  @override
  ReportScreenState createState() => ReportScreenState();
}

class ReportScreenState extends State<ReportScreen> {
  final TextEditingController _searchText = TextEditingController();
  bool showClearButton = true;
  @override
  void initState() {
    context.read<FiscalCouncilCubit>().getReports();
    _searchText.addListener(_onSearchText);

    setState(() {
      showClearButton = _searchText.text.isNotEmpty;
    });
    super.initState();
  }

  @override
  void dispose() {
    _searchText.removeListener(_onSearchText);
    _searchText.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (value, result) async {
        context.read<FormCouncilCubit>().getTopicEvent();
        context.read<FiscalCouncilCubit>().getCouncil();
      },
      child: Scaffold(
        appBar: AppBar(
          centerTitle: true,
          title: const Text("Relatório/Visita"),
          backgroundColor: CooperadoColors.tealGreenDark,
        ),
        body: SafeArea(
            child: Container(
                padding: const EdgeInsets.all(8.0), child: _bodyBloc())),
      ),
    );
  }

  Widget _bodyBloc() {
    return BlocBuilder<FiscalCouncilCubit, FiscalCouncilState>(
      builder: (context, state) {
        if (state is DoneGetReports) {
          return Column(
            children: <Widget>[
              _inputFilter(),
              Expanded(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: state.list!.length,
                  itemBuilder: (context, index) {
                    final notice = state.list!.elementAt(index);
                    return CardReport(
                      noticeModel: notice,
                    );
                  },
                ),
              )
            ],
          );
        } else if (state is ErrorGetReports) {
          return _card(ErrorBanner(message: state.message));
        } else {
          return const SpinKitCircle(
            color: CooperadoColors.tealGreen,
          );
        }
      },
    );
  }

  Widget _inputFilter() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextField(
        controller: _searchText,
        decoration: InputDecoration(
            border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.0),
                borderSide:
                    const BorderSide(color: CooperadoColors.grayLight2)),
            focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.0),
                borderSide:
                    const BorderSide(color: CooperadoColors.grayLight2)),
            labelText: "Buscar",
            labelStyle: const TextStyle(color: CooperadoColors.tealGreen),
            suffixIcon: _searchText.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear,
                        color: CooperadoColors.grayDark2),
                    onPressed: () => _searchText.clear(),
                  )
                : null,
            prefixIcon:
                const Icon(Icons.search, color: CooperadoColors.grayLight2)),
      ),
    );
  }

  void _onSearchText() {
    context.read<FiscalCouncilCubit>().filterReport(_searchText.text);
    setState(() {
      showClearButton = _searchText.text.isNotEmpty;
    });
  }

  Widget _card(Widget content, {bool showReload = false}) {
    return Card(
        elevation: 4.0,
        child: Container(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                _headerCard(context, showReload: showReload),
                content,
              ],
            )));
  }

  Widget _headerCard(contextt, {bool showReload = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Row(
          children: [
            const Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 8.0),
                child: TransparencyHeaderChart(
                  title: "Relatório/Visita",
                ),
              ),
            ),
            if (showReload) ...[
              IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () =>
                      context.read<FiscalCouncilCubit>().getReports())
            ]
          ],
        ),
        //SizedBox(height: 300, child: ProducaoChart())
      ],
    );
  }
}

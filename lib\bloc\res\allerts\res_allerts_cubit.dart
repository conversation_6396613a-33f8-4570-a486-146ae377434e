import 'package:cooperado_minha_unimed/models/res/res_allert_model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'res_allerts_state.dart';

class ResBrazilAllertsCubit extends Cubit<ResBrazilAllertsState> {
  ResBrazilAllertsCubit() : super(InitialResAllertsState());

  void listResAllerts(
      {required String crm,
      required String card,
      DateTimeRange? dataRange}) async {
    try {
      emit(LoadingResAllertsState());

      final listAllerts = await Locator.instance!<ResGraphQlApi>()
          .resGetAllerts(crm: crm, card: card, dataRange: dataRange);

      if (listAllerts.isEmpty) {
        emit(NoDataResAllertsState());
      } else {
        emit(LoadedResAllertsState(listAllerts: listAllerts));
      }
    } catch (e) {
      emit(ErrorResAllertsState(message: e.toString()));
    }
  }
}

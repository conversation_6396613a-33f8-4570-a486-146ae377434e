import 'package:intl/intl.dart';

class ResAllergieModel {
  late String alergeno;
  late String local;
  late String dataTime;
  late String categoria;

  ResAllergieModel({
    required this.alergeno,
    required this.local,
    required this.dataTime,
    required this.categoria,
  });

  ResAllergieModel.fromJson(Map<String, dynamic> json) {
    alergeno = json['alergeno'];
    local = json['local'];
    dataTime = json['data'];
    categoria = json['categoria'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['alergeno'] = alergeno;
    data['local'] = local;
    data['data'] = dataTime;
    data['categoria'] = categoria;
    return data;
  }

  String get dataTimeFormatted {
    final DateTime dateTime = DateTime.parse(dataTime).toLocal();
    return DateFormat('dd/MM/yyyy HH:mm:ss').format(dateTime);
  }
}

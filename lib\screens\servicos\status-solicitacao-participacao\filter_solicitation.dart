import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/participation_solicitation_status_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../colors.dart';

class FiltersPanel extends StatefulWidget {
  final FocusNode? focusNode;

  const FiltersPanel({super.key, this.focusNode});

  @override
  FiltersPanelState createState() => FiltersPanelState();
}

class FiltersPanelState extends State<FiltersPanel> {
  final TextEditingController _searchText = TextEditingController();
  bool showClearButton = true;

  @override
  void initState() {
    super.initState();
    _searchText.addListener(_onSearchText);

    setState(() {
      showClearButton = _searchText.text.isNotEmpty;
    });
  }

  @override
  void dispose() {
    _searchText.removeListener(_onSearchText);
    _searchText.dispose();
    super.dispose();
  }

  void _onSearchText() {
    context
        .read<SolicitationParticipationStatusCubit>()
        .solicitationFilter(_searchText.text);
    setState(() {
      showClearButton = _searchText.text.isNotEmpty;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Container(
        alignment: Alignment.topCenter,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          color: CooperadoColors.green,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
            bottomLeft: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: CooperadoColors.grayDark,
              blurRadius: 2.0,
              spreadRadius: 0.0,
              offset: Offset(2.0, 2.0),
            ),
          ],
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 4.0),
          child: TextField(
            controller: _searchText,
            style: const TextStyle(
              color: CooperadoColors.grayLight,
            ),
            textInputAction: TextInputAction.unspecified,
            focusNode: widget.focusNode,
            cursorColor: CooperadoColors.grayLight,
            decoration: InputDecoration(
                hintText: "Buscar...",
                hintStyle: const TextStyle(color: CooperadoColors.grayLight),
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                suffixIcon: _searchText.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear,
                            color: CooperadoColors.grayLight),
                        onPressed: () => _searchText.clear(),
                      )
                    : null,
                prefixIcon: const Icon(
                  Icons.search,
                  color: CooperadoColors.grayLight,
                )),
          ),
        ),
      ),
    );
  }
}

import 'dart:io';

import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

class WebViewPrivacyPolicy extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const WebViewPrivacyPolicy({
    super.key,
    required this.analytics,
    required this.observer,
  });

  @override
  WebViewPrivacyPolicyState createState() => WebViewPrivacyPolicyState();
}

class WebViewPrivacyPolicyState extends State<WebViewPrivacyPolicy> {
  late final WebViewController _controller;
  late final String url;
  bool loadingPage = true;

  @override
  void initState() {
    super.initState();

    // Obtém a URL da política
    url = BlocProvider.of<AuthCubit>(context).modelGeneralConfigModel.links?.privacyPolitics ?? '';

    // Analytics
    widget.analytics.logScreenView(
      screenName: 'Política de privacidade',
      screenClass: 'WebViewPrivacyPolicy',
    );

    // (Opcional) Forçar plataforma — na maioria dos casos não precisa
    if (Platform.isAndroid) {
      WebViewPlatform.instance = AndroidWebViewPlatform();
    } else if (Platform.isIOS || Platform.isMacOS) {
      WebViewPlatform.instance = WebKitWebViewPlatform();
    }

    // Configura o controller
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..addJavaScriptChannel(
        'Toaster',
        onMessageReceived: (JavaScriptMessage msg) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(msg.message)),
          );
        },
      )
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (_) => setState(() => loadingPage = true),
          onPageFinished: (_) => setState(() => loadingPage = false),
          onNavigationRequest: (_) => NavigationDecision.navigate,
          onWebResourceError: (_) {
            setState(() => loadingPage = false);
            Alert.open(
              context,
              callbackClose: () => Navigator.pop(context),
              title: 'Alerta',
              text: 'Não foi possível carregar essa página.',
            );
          },
        ),
      )
      ..loadRequest(Uri.parse(url)); // carrega a URL inicial
  }

  @override
  void dispose() {
    // Restaura orientação padrão, se você estiver alterando
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Política de privacidade"),
      ),
      body: SafeArea(
        child: Stack(
          children: [
            WebViewWidget(controller: _controller),
            if (loadingPage)
              const Center(
                child: SpinKitCircle(color: CooperadoColors.tealGreen),
              ),
          ],
        ),
      ),
    );
  }
}

import 'package:cooperado_minha_unimed/bloc/res/exame-result/res_exam_result_state.dart';
import 'package:cooperado_minha_unimed/models/res/res_exam_result_model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResExamResultCubit extends Cubit<ResExamResultState> {
  ResExamResultCubit() : super(InitialResExamResultState());

  List<ResExamResultModel> _listExamResult = List.empty(growable: true);
  List<ResExamResultModel> get listExamResult => _listExamResult;

  void listResExamResult({
    required String crm,
    required String card,
    DateTimeRange? dataRange,
  }) async {
    try {
      emit(LoadingResExamResultState());

      // _listExamResult = [
      //   ResExameResultModel.fromJson({
      //     "codigo": "2024-08-22_11:30:31VITAL IMAGEM_866",
      //     "tipo": "laboratorial",
      //     "nomeLocal": "hru fortaleza",
      //     "dataEntrada": "2024-08-22T14:30:31.000Z",
      //     "dataAlta": null,
      //     "codigoAtendimentoEncode":
      //         "MjAyNC0wOC0yMl8xMTozMDozMVZJVEFMIElNQUdFTV84NjY=",
      //     "itensAtendimento": [
      //       {"descricao": "exame_result"}
      //     ]
      //   })
      // ];

      _listExamResult = await Locator.instance!<ResGraphQlApi>().resExamResult(
        crm: crm,
        card: card,
        dataRange: dataRange,
      );

      if (_listExamResult.isEmpty) {
        emit(NoDataResExamResultState());
      } else {
        emit(LoadedResExamResultState(listExamResult: _listExamResult));
      }
    } catch (e) {
      emit(ErrorResExamResultState(message: e.toString()));
    }
  }
}

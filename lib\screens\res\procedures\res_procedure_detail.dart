import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/res/res_procedure_detail_model.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/info_row.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/title_result.dart';
import 'package:flutter/material.dart';

class ResProcedureDetail extends StatelessWidget {
  final List<ResProcedureDetailModel> resProcedureDetailModel;

  const ResProcedureDetail({super.key, required this.resProcedureDetailModel});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Container(
              height: 4,
              width: 100,
              margin: const EdgeInsets.symmetric(
                vertical: 10,
              ),
              decoration: BoxDecoration(
                color: CooperadoColors.grayLight3,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 27),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Procedimentos',
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                    color: CooperadoColors.blackText,
                  ),
                ),
                TitleResult(
                  title: resProcedureDetailModel.length > 1 ? 'Resultados ' : 'Resultado ',
                  quantity: resProcedureDetailModel.length.toString().padLeft(2, '0'),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          Expanded(
            child: Scrollbar(
              trackVisibility: true,
              thumbVisibility: true,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    ...resProcedureDetailModel.map(
                      (detail) => Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: CooperadoColors.grayLight3),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: Column(
                              children: [
                                InfoRow(label: 'Médico', value: detail.nomeMedico),
                                const SizedBox(height: 20),
                                InfoRow(label: 'Procedimento', value: detail.nomeProcedimento),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }
}

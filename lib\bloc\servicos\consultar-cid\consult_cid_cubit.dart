import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/shared/api/services.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/servicos/cid_list.vo.dart';
import 'package:flutter/material.dart';

import 'consult_cid_state.dart';

class ConsultCidCubit extends Cubit<ConsultCidState> {
  ConsultCidCubit() : super(ConsultCidInitial());

  getConsultCidEvent(String keySearch, int? pagina) async {
    emit(LoadingGetConsultCidState());
    try {
      final CidList cidList = await Locator.instance!<ServicesApi>()
          .getCidList(keySearch: keySearch, pagina: pagina);

      emit(DoneGetConsultCidState(cidList));
    } catch (ex) {
      debugPrint(ex.toString());
      emit(ErrorGetConsultCidState('$ex'));
    }
  }

  setInitialState() async {
    emit(ConsultCidInitial());
  }
}

part of 'loss_ratio.cubit.dart';

abstract class LossRatioState extends Equatable {
  const LossRatioState();
}

class InitialLossRatioState extends LossRatioState {
  @override
  List<Object> get props => [];
}

class LoadingLossRatioState extends LossRatioState {
  @override
  List<Object> get props => [];
}

class ErrorLossRatioState extends LossRatioState {
  final String? message;
  @override
  List<Object?> get props => [message];
  const ErrorLossRatioState(this.message);
}

class LoadedLossRatioState extends LossRatioState {
  final VOIndicatorModel lossRatio;
  @override
  List<Object> get props => [lossRatio];

  const LoadedLossRatioState(this.lossRatio);
}

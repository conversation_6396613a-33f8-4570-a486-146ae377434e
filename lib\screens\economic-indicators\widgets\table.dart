//esta classe inteira sera excluida estou mantendo ela apenas enquanto realizo os testes


/* // ignore_for_file: public_member_api_docs, sort_constructors_first

// ignore_for_file: prefer_final_fields


import 'package:cooperado_minha_unimed/bloc/economic-indicators/economic_indicators_cubit.dart';
import 'package:cooperado_minha_unimed/shared/api/economic_indicators.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:two_dimensional_scrollables/two_dimensional_scrollables.dart';

import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/economic_indicators.model.dart';
import 'package:unimed_select/unimed-select.dart';

class TableGride extends StatefulWidget {
  List<EconomicIndicatorsModel> economicIndicator;
 

  TableGride({
    Key? key,
    required this.economicIndicator,
  }) : super(key: key);

  @override
  State<TableGride> createState() => _TableGrideState();
}

  
  List<String> meses = [
    "Indicador",
    "Objetivo",
    "Formula",
    "Janeiro",
    "Fevereiro",
    "Março",
    "Abril",
    "Maio",
    "Junho",
    "Julho",
    "Agosto",
    "Setembro",
    "Outubro",
    "Novembro",
    "Dezembro",
  ];

   List<String> anos = [
    '2018',
    '2019',
    '2020',
    '2021',
    '2022',
    '2023',
  ];

  



 



class _TableGrideState extends State<TableGride> {
  final ScrollController _verticalController = ScrollController();
  late int _rowCount;
  late List<EconomicIndicatorsModel>listEconomic;
  int _columnCount = 15;
   late String selectedYear;
   List<String> suIndicators=[];
   //List<String> liberatedYears=[];
   
  TextEditingController _controller = TextEditingController();


 List<EconomicIndicatorsModel> selectByYear(String year){
    return widget.economicIndicator.where((item) => item.nuAno == year).toList();
  }
/*  knowingYears(){
   //este metodo filtra os anos do retorno da api, seleciona sem duplicar valores para o unimed select
   widget.economicIndicator.forEach((element) {
    if(!liberatedYears.contains(element.nuAno)){
      liberatedYears.add(element.nuAno.toString());
    }
    });
   
  } */

    List<String> selectIndicators(List<EconomicIndicatorsModel> list) {
    List<String> values = [];
    for (EconomicIndicatorsModel economicindicator in list) {
      
      if (!values.contains(economicindicator.dsIndicador)) {
        values.add(economicindicator.dsIndicador.toString());
      }
    }
    return values;
  }

 

  @override
  void initState() {

    super.initState();
  selectedYear = DateTime.now().year.toString();
    _controller.text = selectedYear.toString();
    listEconomic = selectByYear(selectedYear);

    suIndicators = selectIndicators(listEconomic);
    _rowCount = suIndicators.length +1;
  }

  updateTable(String ano) async {
      //context.read<EconomicIndicatorsCubit>().getEconomicIndicators(ano);
  
 /*   var recept = await Locator.instance!<EconomicIndicatorsApi>().updateEconomicIndicators(ano);
   print(recept.runtimeType);
   if(recept is List<EconomicIndicatorsModel>){
    widget.economicIndicator.clear();
    widget.economicIndicator = recept;
   }else if(recept.length == 0){
    print(recept);
   } */
 listEconomic = selectByYear(ano);
    suIndicators = selectIndicators(listEconomic);
    _rowCount = suIndicators.length +1;
    setState(() {
      
    });
  }

   
  @override
  Widget build(BuildContext context) {
    return SafeArea(

      child: Padding(
          padding:  EdgeInsets.symmetric( vertical: 10),
          child: SizedBox(
            height: MediaQuery.of(context).size.height,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                 SizedBox(height: 30,),
                                UnimedSelect<String?>(
                                  showClearButton: false,
                    title: 'Escolha o ano',
                    controller: _controller,
                    items: anos.map(
                          (entry) => UnimedSelectItemModel(
                            value: entry,
                            label: entry,
                          ),
                        )
                        .toList(),
                    onSelect: (item)async {
                      print(item);
                     
                     
                          selectedYear = item.toString()  ;
                           _controller.text = selectedYear.toString();
                           Future.delayed(Duration(seconds: 1),() async {
                              await  updateTable(selectedYear);
                           });
                     
                     
                    
                    },
                                ),
       
                SizedBox(height: 8,),
              
      SizedBox(height: 8,),
                Expanded(
                  child: TableView.builder(
                    
                    verticalDetails:
                        ScrollableDetails.vertical(controller: _verticalController),
                    cellBuilder: _buildCell,
                    columnCount: _columnCount,
                    columnBuilder: _buildColumnSpan,
                    rowCount: _rowCount,
                    rowBuilder: _buildRowSpan,
                    
                  ),
                ),
              ],
            ),
          ),
        ),
    );
  }

    Widget _buildCell(BuildContext context, TableVicinity vicinity) {
      //cria o cabeçalho da tabela
      if(vicinity.column == 0 && vicinity.row == 0 ){
         return  Center(
      child: Text(meses[vicinity.column], style: TextStyle(color: Colors.white, fontSize: 18),),
    );
      }else   if(vicinity.column == 1 && vicinity.row == 0 ){
         return  Center(
      child: Text(meses[vicinity.column], style: TextStyle(color: Colors.white, fontSize: 18),),
    );
      }else   if(vicinity.column == 2 && vicinity.row == 0 ){
         return  Center(
      child: Text(meses[vicinity.column], style: TextStyle(color: Colors.white, fontSize: 18),),
    );
      }else   if(vicinity.row == 0 ){
         return  Center(
      child: Text(meses[vicinity.column].substring(0,3), style: TextStyle(color: Colors.white, fontSize: 18),),
    );
    //cria a coluna de indicadores
      }else  if(vicinity.column == 0 && vicinity.row > 0 && vicinity.row <= suIndicators.length ){
         return  Center(
      child: Text(suIndicators[vicinity.row-1]),
    );

    //cria coluna de objetivos
     }else if(vicinity.column == 1 && vicinity.row > 0 && vicinity.row <= suIndicators.length ){
      
           return  Center(
      child: Text(listEconomic[vicinity.row-1].objetivo.toString()),
    );

     //cria coluna de formulas
     }else if(vicinity.column == 2 && vicinity.row > 0 && vicinity.row <= suIndicators.length ){
      
           return  Center(
      child: Text(listEconomic[vicinity.row-1].formula.toString()),
    );

      //preenche as colunas de meses
     }else if(vicinity.column > 2 && vicinity.row > 0 && vicinity.row <= suIndicators.length ){
      
    switch (vicinity.column) {
    case 3:
       return  Center(
      child: Text(listEconomic[vicinity.row-1].jan.toString()),
    );
      case 4:
       return  Center(
      child: Text(listEconomic[vicinity.row-1].fev.toString()),
    );
      case 5:
       return  Center(
      child: Text(listEconomic[vicinity.row-1].mar.toString()),
    );
      case 6:
       return  Center(
      child: Text(listEconomic[vicinity.row-1].abr.toString()),
    );
      case 7:
       return  Center(
      child: Text(listEconomic[vicinity.row-1].mai.toString()),
    );
      case 8:
       return  Center(
      child: Text(listEconomic[vicinity.row-1].jun.toString()),
    );
      case 9:
       return  Center(
      child: Text(listEconomic[vicinity.row-1].jul.toString()),
    );
      case 10:
       return  Center(
      child: Text(listEconomic[vicinity.row-1].ago.toString()),
    );
      case 11:
       return  Center(
      child: Text(listEconomic[vicinity.row-1].theSet.toString()),
    );
      case 12:
       return  Center(
      child: Text(listEconomic[vicinity.row-1].out.toString()),
    );
      case 13:
       return  Center(
      child: Text(listEconomic[vicinity.row-1].nov.toString()),
    );
      case 14:
       return  Center(
      child: Text(listEconomic[vicinity.row-1].dez.toString()),
    );
     

    default:
      print('Data inválida');
  }




     }


    return Center(
      child: Text("--",),
    );
  }

   TableSpan _buildColumnSpan(int index) {
    const TableSpanDecoration decoration = TableSpanDecoration(
      border: TableSpanBorder(
        trailing: BorderSide(
          width: 1,
        ),
      ),
    );

  
    switch (index) {
      case 0:
        return TableSpan(
          foregroundDecoration: decoration,
          extent:  FixedTableSpanExtent(MediaQuery.of(context).size.width * 0.5),
         padding:TableSpanPadding.all(8),
          onEnter: (_) => print('Entered column $index'),
          recognizerFactories: <Type, GestureRecognizerFactory>{
            TapGestureRecognizer:
                GestureRecognizerFactoryWithHandlers<TapGestureRecognizer>(
              () => TapGestureRecognizer(),
              (TapGestureRecognizer t) =>
                  t.onTap = () => print('Tap column $index'),
            ),
          },
        );
        case 1:
        return TableSpan(
          foregroundDecoration: decoration,
          extent:  FixedTableSpanExtent(MediaQuery.of(context).size.width * 0.2),
           padding:TableSpanPadding.all(5),
          onEnter: (_) => print('Entered column $index'),
          recognizerFactories: <Type, GestureRecognizerFactory>{
            TapGestureRecognizer:
                GestureRecognizerFactoryWithHandlers<TapGestureRecognizer>(
              () => TapGestureRecognizer(),
              (TapGestureRecognizer t) =>
                  t.onTap = () => print('Tap column $index'),
            ),
          },
        );
         case 2:
        return TableSpan(
          foregroundDecoration: decoration,
          extent:  FixedTableSpanExtent(MediaQuery.of(context).size.width * 0.2),
           padding:TableSpanPadding.all(5),
          onEnter: (_) => print('Entered column $index'),
          recognizerFactories: <Type, GestureRecognizerFactory>{
            TapGestureRecognizer:
                GestureRecognizerFactoryWithHandlers<TapGestureRecognizer>(
              () => TapGestureRecognizer(),
              (TapGestureRecognizer t) =>
                  t.onTap = () => print('Tap column $index'),
            ),
          },
        );

      default: 
        return TableSpan(
          foregroundDecoration: decoration,
          extent: FixedTableSpanExtent(MediaQuery.of(context).size.width * 0.3),
           padding:TableSpanPadding.all(5),
          onEnter: (_) => print('Entered column $index'),
        );
    
    }

  }

   TableSpan _buildRowSpan(int index) {
    final TableSpanDecoration decoration = TableSpanDecoration(
      
      color: index ==0 ?CooperadoColors.purple/* Colors.green.shade400  */: (index.isEven? Color.fromARGB(255, 244, 240, 231) : null),
      border: const TableSpanBorder(
        trailing: BorderSide(
          width: 1,
        ),
      ),
    );
     return TableSpan(
          backgroundDecoration: decoration,
          extent: const FixedTableSpanExtent(50),
           padding:TableSpanPadding.all(5),
          recognizerFactories: <Type, GestureRecognizerFactory>{
            TapGestureRecognizer:
                GestureRecognizerFactoryWithHandlers<TapGestureRecognizer>(
              () => TapGestureRecognizer(),
              (TapGestureRecognizer t) =>
                  t.onTap = () => print('Tap row $index'),
            ),
          },
        );

  }
} */
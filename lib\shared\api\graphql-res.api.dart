// ignore_for_file: sdk_version_since

import 'package:cooperado_minha_unimed/models/res-internal/lab_exam_detail.model.dart';
import 'package:cooperado_minha_unimed/models/res-internal/res_indicator_data.model.dart';
import 'package:cooperado_minha_unimed/models/res/res_allergie_model.dart';
import 'package:cooperado_minha_unimed/models/res/res_allert_model.dart';
import 'package:cooperado_minha_unimed/models/res/res_attendance_model.dart';
import 'package:cooperado_minha_unimed/models/res/res_brasil_beneficiary_model.dart';
import 'package:cooperado_minha_unimed/models/res/res_brasil_detail_exam_phisical_model.dart';
import 'package:cooperado_minha_unimed/models/res/res_brasil_diagnostico_model.dart';
import 'package:cooperado_minha_unimed/models/res/res_configs_model.dart';
import 'package:cooperado_minha_unimed/models/res/res_document_detail_model.dart';
import 'package:cooperado_minha_unimed/models/res/res_exam_result_image_detail_model.dart';
import 'package:cooperado_minha_unimed/models/res/res_exam_result_laboratory_detail_model.dart';
import 'package:cooperado_minha_unimed/models/res/res_exam_result_model.dart';
import 'package:cooperado_minha_unimed/models/res/res_procedure_detail_model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/messages.exceptions.dart';
import 'package:flutter/material.dart';
import 'package:graphql/client.dart';
import 'package:intl/intl.dart';

class ResGraphQlApi extends GraphQlApi {
  ResGraphQlApi(super.httpClient);

  Future<List<ResLabExamDetailModel>> resInternalLabExamDetailsByCardAndOrderId(
      {required String card, required String orderId}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
        query ResInternalLabExamDetailsByCardAndOrderId {
            resInternalLabExamDetailsByCardAndOrderId(
                card: "$card"
                orderId: "$orderId"
            ) {
                material
                resultScheduledDate
                samplePendency
                testId
                testName
                testPdfUrl
                testStatus
                collectionDate
            }
        }
      ''';

      logger.e('resInternalLabExamDetailsByCardAndOrderId query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e(
            'resInternalLabExamDetailsByCardAndOrderId exception : ${result.exception}');
        throw UnimedException(MessageException.general);
      } else {
        final data =
            result.data!['resInternalLabExamDetailsByCardAndOrderId'] as List;
        logger.d(
            'resInternalLabExamDetailsByCardAndOrderId success list ${data.length}');

        final collection = data.map((e) {
          return ResLabExamDetailModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on UnimedException catch (ex) {
      logger.e(
          'resInternalLabExamDetailsByCardAndOrderId ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('resInternalLabExamDetailsByCardAndOrderId exception : $ex');
      throw UnimedException(MessageException.general);
    }
  }

  Future<List<ResAttendanceModel>> resBrasilAttendanceByCard({
    required String crm,
    required String card,
    DateTime? startDateTime,
    DateTime? endDateTime,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
        query ResBrazilAttendancesByCRM {
          resBrazilAttendancesByCRM(
          crm: "$crm", 
          card: "$card",
          startDate: ${startDateTime != null ? '"${startDateTime.toIso8601String()}Z"' : null},
          endDate: ${endDateTime != null ? '"${endDateTime.toIso8601String()}Z"' : null}
          ) {
              codigo
              tipo
              nomeLocal
              dataEntrada
              dataAlta
              codigoAtendimentoEncode
              itensAtendimento {
                  descricao
              }
          }
      }
      ''';

      logger.e('resBrasilAttendanceByCard query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('resBrasilAttendanceByCard exception : ${result.exception}');

        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.general;

        throw UnimedException(message);
      } else {
        final data = result.data!['resBrazilAttendancesByCRM'] as List;
        logger.d('resBrazilAttendancesByCRM success list ${data.length}');

        final collection = data.map((e) {
          return ResAttendanceModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on UnimedException catch (ex) {
      logger.e('resBrasilAttendanceByCard ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('resBrasilAttendanceByCard exception : $ex');
      throw UnimedException(MessageException.general);
    }
  }

  Future<List<ResBrazilDiagnosticoModel>> resBrazilDetailDiagnosticResult({
    required String crm,
    required card,
    required code,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
         query ResBrazilDetailDiagnostic {
          resBrazilDetailDiagnostic(
            crm: "$crm"
            card: "$card",
            code: "$code"
          ) {
              descricao
              nomeMedico
              dataEntrada
          }
      }
      ''';

      logger.e('resBrazilDetailDiagnosticResult query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e(
            'resBrazilDetailDiagnosticResult exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.general;

        throw UnimedException(message);
      } else {
        final data = result.data!['resBrazilDetailDiagnostic'] as List;
        logger.d('resBrazilDetailDiagnostic success list ${data.length}');

        final collection = data.map((e) {
          return ResBrazilDiagnosticoModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on UnimedException catch (ex) {
      logger.e('resBrazilDetailDiagnosticResult ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('resBrazilDetailDiagnosticResult exception : $ex');
      throw UnimedException(MessageException.general);
    }
  }

  Future<List<ResDocumentDetailModel>> resDocumentDetail(
      {required String crm, required String card, required String code}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
        query ResBrazilDocumentDetails {
            resBrazilDocumentDetails(crm: "$crm", card: "$card", code: "$code") {
                nomeMedico
                especialidadeMedico
                conteudo
                streamDocumento
                tipoDocumento
                tipoArquivo
                urlDocumento
                data
            }
        }
      ''';

      logger.d('resDocumentDetail query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('resDocumentDetail exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.general;

        throw UnimedException(message);
      } else {
        final List<dynamic> data =
            result.data!['resBrazilDocumentDetails'] as List;
        logger.d('resDocumentDetail success list ${data.length}');

        final List<ResDocumentDetailModel> collection = data.map((e) {
          return ResDocumentDetailModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on UnimedException catch (ex) {
      logger.e('resDocumentDetail ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('resDocumentDetail exception : $ex');
      throw GraphQlException(MessageException.general);
    }
  }

  Future<List<ResAllergieModel>> resListAllergies(
      {required String crm, required String card}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
       query ResBrazilAllergies {
        resBrazilAllergies(crm: "$crm", card: "$card") {
            alergeno
            local
            data
            categoria
          }
        }
      ''';

      logger.e('ResBrazilAllergies query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('ResBrazilAllergies exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.general;

        throw UnimedException(message);
      } else {
        final List<dynamic> data = result.data!['resBrazilAllergies'] as List;
        logger.d('ResBrazilAllergies success list ${data.length}');

        final List<ResAllergieModel> collection = data.map((e) {
          return ResAllergieModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on UnimedException catch (ex) {
      logger.e('ResBrazilAllergies ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('ResBrazilAllergies exception : $ex');
      throw GraphQlException(MessageException.general);
    }
  }

  Future resConfigs({required String crm, String? cpf}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query;

      if (cpf != null && cpf.isNotEmpty) {
        query = '''
        query ConfigAppCooperadoResBrazil {
            configAppCooperadoResBrazil(crm: "$crm") {
                allergies {
                    categories {
                        code
                        description
                    }
                }
            }
            resBrazilBeneficiaryIndicatorList(cpf: "$cpf") {
                id
                description
            }
        }
        ''';
      } else {
        query = '''
        query ConfigAppCooperadoResBrazil {
            configAppCooperadoResBrazil(crm: "$crm") {
                allergies {
                    categories {
                        code
                        description
                    }
                }
            }
        }
        ''';
      }

      logger.e('ConfigAppCooperadoResBrazil query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('ConfigAppCooperadoResBrazil exception : ${result.exception}');
        throw UnimedException(MessageException.general);
      } else {
        Map<String, dynamic> data = (result.data!['configAppCooperadoResBrazil']
            as Map<String, dynamic>);

        if (result.data!['resBrazilBeneficiaryIndicatorList'] != null) {
          final dataIndicators =
              result.data!['resBrazilBeneficiaryIndicatorList'] as List;

          data['indicators'] = dataIndicators;
        } else {
          data['indicators'] = [];
        }

        logger.d('ConfigAppCooperadoResBrazil success list ${data.length}');

        final ResConfigModel collection = ResConfigModel.fromJson(data);

        return collection;
      }
    } on UnimedException catch (ex) {
      logger.e('ConfigAppCooperadoResBrazil ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('ConfigAppCooperadoResBrazil exception : $ex');
      throw GraphQlException(MessageException.general);
    }
  }

  Future<List<ResIndicatorModel>> loadResBrazilBeneficiaryIndicators({
    required String cpf,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
      query ResBrazilBeneficiaryIndicators {
        resBrazilBeneficiaryIndicators(cpf: "$cpf") {
          id
          description
          selected
        }
      }
    ''';

      logger.d('resBrazilBeneficiaryIndicators query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(query),
      );

      final QueryResult result = await client.query(options);
      if (result.hasException) {
        logger
            .e('resBrazilBeneficiaryIndicators exception: ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.general;

        throw UnimedException(message);
      } else {
        List<ResIndicatorModel> data = [];
        for (var element in result.data!['resBrazilBeneficiaryIndicators']) {
          data.add(ResIndicatorModel.fromJson(element));
        }

        logger.d('resBrazilBeneficiaryIndicators success list ${data.length}');

        return data;
      }
    } on UnimedException catch (ex) {
      logger.e('resBrazilBeneficiaryIndicators ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('resBrazilBeneficiaryIndicators exception : $ex');
      throw GraphQlException(MessageException.general);
    }
  }

  Future<List<ResIndicatorModel>> resBrazilBeneficiaryIndicatorList({
    required String cpf,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
      query ResBrazilBeneficiaryIndicatorList {
        resBrazilBeneficiaryIndicatorList(cpf: "$cpf") {
          id
          description
        }
      }
    ''';

      logger.d('resBrazilBeneficiaryIndicatorList query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(query),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e(
            'resBrazilBeneficiaryIndicatorList exception: ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.general;

        throw UnimedException(message);
      } else {
        List<ResIndicatorModel> data = [];
        for (var element in result.data!['resBrazilBeneficiaryIndicatorList']) {
          data.add(ResIndicatorModel.fromJson(element));
        }

        logger
            .d('resBrazilBeneficiaryIndicatorList success list ${data.length}');

        return data;
      }
    } on UnimedException catch (ex) {
      logger.e('resBrazilBeneficiaryIndicatorList ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('resBrazilBeneficiaryIndicatorList exception : $ex');
      throw GraphQlException(MessageException.general);
    }
  }

  Future<List<IndicatorDataModel>> resBrazilBeneficiaryIndicatorsData({
    required String cpf,
    required List<String> indicatorsId,
    required DateTimeRange dateRange,
    required String calendarType,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
      query ResBrazilBeneficiaryIndicatorsData {
        resBrazilBeneficiaryIndicatorsData(
          cpf: "$cpf"
          indicatorIds: "${indicatorsId.join('-')}"
          calendarType: $calendarType
         startDate: "${DateFormat("yyyy-MM-dd").format(dateRange.start)}"
              endDate: "${DateFormat("yyyy-MM-dd").format(dateRange.end)}"
        ) {
          id
          success
          unity
          indicatorsData {
            date
            x
            y
          }
        }
      }
    ''';

      logger.d('resBrazilBeneficiaryIndicatorsData query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(query),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e(
            'resBrazilBeneficiaryIndicatorsData exception: ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.general;

        throw UnimedException(message);
      } else {
        List<IndicatorDataModel> data = [];
        for (var element
            in result.data!['resBrazilBeneficiaryIndicatorsData']) {
          data.add(IndicatorDataModel.fromJson(element));
        }
        logger.d('resBrazilBeneficiaryIndicatorsData success list $data');

        return data;
      }
    } on UnimedException catch (ex) {
      logger.e('resBrazilBeneficiaryIndicatorsData ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('resBrazilBeneficiaryIndicatorsData exception : $ex');
      throw GraphQlException(MessageException.general);
    }
  }

  Future<List<ResAttendanceModel>> resGetAttendanceByType(
      {required String crm,
      required String card,
      required String type,
      DateTimeRange? dataRange}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String date = "";
      if (dataRange != null) {
        date = '''
          startDate: "${DateFormat("yyyy-MM-dd").format(dataRange.start)}"
          endDate:  "${DateFormat("yyyy-MM-dd").format(dataRange.end)}"
        ''';
      }

      String query = '''
        query ResBrazilAttendancesByCRM {
          resBrazilAttendancesByCRMAndType(
              crm: "$crm"
              card:  "$card"
              type: $type
              $date
          ) {
              codigo
              tipo
              nomeLocal
              dataEntrada
              dataAlta
              codigoAtendimentoEncode
              itensAtendimento {
                descricao
              }
          }
        }
      ''';

      logger.d('resGetAttendanceByType query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('resGetAttendanceByType exception: ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.general;

        throw UnimedException(message);
      } else {
        final List<dynamic> data =
            result.data!['resBrazilAttendancesByCRMAndType'] as List;
        logger.d('resGetAttendanceByType success list ${data.length}');

        final List<ResAttendanceModel> collection = data.map((e) {
          return ResAttendanceModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on UnimedException catch (ex) {
      logger.e('resGetAttendanceByType ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('resGetAttendanceByType exception : $ex');
      throw GraphQlException(MessageException.general);
    }
  }

  Future<List<ResAllertModel>> resGetAllerts({
    required String crm,
    required String card,
    DateTimeRange? dataRange,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String date = "";
      if (dataRange != null) {
        date = '''
          startDate: "${DateFormat("yyyy-MM-dd").format(dataRange.start)}"
          endDate:  "${DateFormat("yyyy-MM-dd").format(dataRange.end)}"
        ''';
      }

      String query = '''
        query ResBrazilAlerts {
            resBrazilAlerts(
              crm: "$crm"
              card: "$card"
              $date
            ) {
                alert
                origin
                date
                type
                typeOrigin
            }
        }
      ''';

      logger.d('ResBrazilProcedures query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('resGetAllerts exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.general;

        throw UnimedException(message);
      } else {
        final List<dynamic> data = result.data!['resBrazilAlerts'] as List;
        logger.d('resGetAllerts success list ${data.length}');

        final List<ResAllertModel> collection = data.map((e) {
          return ResAllertModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on UnimedException catch (ex) {
      logger.e('resGetAllerts ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('resGetAllerts exception : $ex');
      throw GraphQlException(MessageException.general);
    }
  }

  Future<List<ResProcedureDetailModel>> resProcedureDetail(
      {required String crm, required String card, required String code}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
        query ResBrazilProcedureDetails {
            resBrazilProcedureDetails(crm: "$crm", card: "$card", code: "$code") {
                nomeProcedimento
                nomeMedico
                dataProcedimento
            }
        }
      ''';

      logger.d('resProcedureDetail query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('resProcedureDetail exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.general;

        throw UnimedException(message);
      } else {
        final List<dynamic> data =
            result.data!['resBrazilProcedureDetails'] as List;
        logger.d('resProcedureDetail success list ${data.length}');

        final List<ResProcedureDetailModel> collection = data.map((e) {
          return ResProcedureDetailModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on UnimedException catch (ex) {
      logger.e('resProcedureDetail ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('resProcedureDetail exception : $ex');
      throw GraphQlException(MessageException.general);
    }
  }

  Future<List<ResDetailExameFisicoModel>> resExamesFisicosDetail({
    required String crm,
    required String card,
    required String code,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
        query ResBrazilPhisicalExamDetails {
            resBrazilPhisicalExamDetails(crm: "$crm", card: "$card",code: "$code") {
                nome
                valor
                unidadeMedida
            }
        }
      ''';

      logger.d('resExamesFisicosDetail query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('resExamesFisicosDetail exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.general;

        throw UnimedException(message);
      } else {
        final List<dynamic> data =
            result.data!['resBrazilPhisicalExamDetails'] as List;
        logger.d('resExamesFisicosDetail success list ${data.length}');

        final List<ResDetailExameFisicoModel> collection = data.map((e) {
          return ResDetailExameFisicoModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on UnimedException catch (ex) {
      logger.e('resExamesFisicosDetail ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('resExamesFisicosDetail exception : $ex');
      throw GraphQlException(MessageException.general);
    }
  }

  Future<ResBrazilBeneficiaryModel> resBrazilBeneficiaryByCpf(
      {required String crm, required String cpf}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
     query ResBrazilBeneficiary {
          resBrazilBeneficiary(cpf: "$cpf", crm: "$crm") {
              name
              socialName
              cpf
              card
              permissions {
                  name
                  value
              }
          }
      }
      ''';

      logger.d('resBrazilBeneficiaryByCpf query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        final errorMessage = result.exception!.graphqlErrors.first.message;
        if (errorMessage == "Beneficiário não encontrado") {
          throw NotFoundException();
        }
        logger.e('resBrazilBeneficiaryByCpf exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.general;

        throw UnimedException(message);
      } else {
        final Map<String, dynamic> data =
            result.data!['resBrazilBeneficiary'] as Map<String, dynamic>;
        logger.d('resBrazilBeneficiaryByCpf success list ${data.length}');

        final ResBrazilBeneficiaryModel collection =
            ResBrazilBeneficiaryModel.fromJson(data);

        return collection;
      }
    } on NotFoundException {
      throw NotFoundException();
    } on UnimedException catch (ex) {
      logger.e('resBrazilBeneficiaryByCpf ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('resBrazilBeneficiaryByCpf exception : $ex');
      throw GraphQlException(MessageException.general);
    }
  }

  Future<List<ResExamResultModel>> resExamResult({
    required String crm,
    required String card,
    DateTimeRange? dataRange,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String date = "";
      if (dataRange != null) {
        date = '''
          startDate: "${DateFormat("yyyy-MM-dd").format(dataRange.start)}"
          endDate:  "${DateFormat("yyyy-MM-dd").format(dataRange.end)}"
        ''';
      }
      String query = '''
        query ResBrazilExamsResults {
            resBrazilExamsResults(
                crm: "$crm",
                card: "$card",
                $date
                )
                {
                    url
                    date
                    codeTUSS
                    codeReport
                    codeDetail
                    type
                }
        }
        ''';

      logger.e('resExamResult query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('resExamResult exception : ${result.exception}');

        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.general;

        throw UnimedException(message);
      } else {
        final data = result.data!['resBrazilExamsResults'] as List;
        logger.d('resExamResult success list ${data.length}');

        final collection = data.map((e) {
          return ResExamResultModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on UnimedException catch (ex) {
      logger.e('resBrasilAttendanceByCard ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('resBrasilAttendanceByCard exception : $ex');
      throw GraphQlException(MessageException.general);
    }
  }

  Future<List<ResExamResultLaboratoryDetailModel>>
      resExamResultLaboratoryDetail(
          {required String crm,
          required String card,
          required String code}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
          query ResExamResultLaboratoryDetailModel {
            resBrazilExamsResultsLaboratory(crm: "$crm", card: "$card", code: "$code") {
                name
                codeExam
                applicants {
                    name
                }
            }
          }
          ''';

      logger.d('resExamResultLaboratoryDetail query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger
            .e('resExamResultLaboratoryDetail exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.general;

        throw UnimedException(message);
      } else {
        final List<dynamic> data =
            result.data!['resBrazilExamsResultsLaboratory'] as List;
        logger.d('resExamResultLaboratoryDetail success list ${data.length}');

        final List<ResExamResultLaboratoryDetailModel> collection =
            data.map((e) {
          return ResExamResultLaboratoryDetailModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on UnimedException catch (ex) {
      logger.e('resExamResultLaboratoryDetail ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('resExamResultLaboratoryDetail exception : $ex');
      throw GraphQlException(MessageException.general);
    }
  }

  Future<List<ResExamResultImageDetailModel>> resExamResultImageDetail(
      {required String crm, required String card, required String code}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
         query ResBrazilExamsResultsImage {
          resBrazilExamsResultsImage(crm: "6233", card: "123123123", code: "1231231") {
              name
              codeExam
              urlDocument
              applicants {
                  name
              }
          }
        }
          ''';

      logger.d('resExamResultImageDetail query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('resExamResultImageDetail exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.general;

        throw UnimedException(message);
      } else {
        final List<dynamic> data =
            result.data!['resBrazilExamsResultsImage'] as List;
        logger.d('resExamResultImageDetail success list ${data.length}');

        final List<ResExamResultImageDetailModel> collection = data.map((e) {
          return ResExamResultImageDetailModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on UnimedException catch (ex) {
      logger.e('resExamResultLaboratoryDetail ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('resExamResultLaboratoryDetail exception : $ex');
      throw GraphQlException(MessageException.general);
    }
  }
}

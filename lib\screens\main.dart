import 'package:cooperado_minha_unimed/bloc/auth/agree_terms/agree_terms_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/auth/register/register_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/beneficiary/beneficiary_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/chanel-ethics/channel_ethics_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/club-more-benefits/club_more_benefits_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/config-profile/config_profile_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/directors/directors_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/activation-check/activation_check_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/activation/activation_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/finish/finish_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/history-activations/history_activation_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/timer-card/timer_card.dart';
import 'package:cooperado_minha_unimed/bloc/economic-indicators/economic_indicators_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/financial/invoice_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/financial/my_invoices_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/fiscal-council/fiscal_council_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/fiscal-council/form-council/form_council_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/fiscal-council/form-council/send-form/send_form_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/forgot-password/forgot_password_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/forgot-password/redefine_password_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/honorarios-quimioterapia/honorarios_quimioterapia_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/indicators/attendance-comparative/attendance_comparative_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/indicators/cost_comparative/cost_comparative_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/indicators/daily_attendance/daily_attendance_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/indicators/historical_production/historical_production_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/indicators/last_production/last_production_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/indicators/medical_production/medical_production_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/location/location_permission_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/medicines/medicines-visibility/medicines_visibility_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/medicines/medicines_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/medicines/search-medicine/search_medicines_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/news/council/council_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/news/news_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/news/other/other_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/news/read/read_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/news/transparency/transparency_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/notificao/notificacao_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/notificao/notifications-count/notifications_count_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/office-notices/office_notices_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/pdf-factory/pdf_factory_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/profile/profile_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res-internal/image_result/res_image_exam_result_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res-internal/lab_exams/res_lab_exam_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res-internal/lab_exams_detail/res_lab_exam_detail_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res-internal/solicitations/res_solicitation_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/allergies/res_allergies_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/allerts/res_allerts_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/atendimento/res_brazil_atendimento_result_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/beneficiary/res_brasil_beneficiary_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/configs/res_configs_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/diagnostico/res_brazil_diagnostico_result_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/documents/detail/res_documents_detail_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/documents/res_documents_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/exame-result/detail/res_exam_result_detail_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/exame-result/res_exam_result_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/exames-fisicos/detail/res_brazil_detail_examesfisicos_result_cubit.state.dart';
import 'package:cooperado_minha_unimed/bloc/res/indicators/res_indicator_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/indicators/res_indicators_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/procedures/detail/res_procedures_detail_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/procedures/res_procedures_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/score-comparison/score_comparison_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/consultar-chbpm/consult_chbpm_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/consultar-cid/consult_cid_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/consultar-opme/consult_opme_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/extrato-quota-part/extract_quota_part_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/ir-declaration/ir_declaration_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/relatorio-producao/report_production_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/add_file/add_file_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/glose_resource/add_glosa_resource_bloc.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/glose_resource/glosa_resource_bloc.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/participation_solicitation_status_bloc.dart';
import 'package:cooperado_minha_unimed/bloc/splash-screen/splash_screen_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/support-cooperative/support_cooperative_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/theme/theme_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/transparency/assistance_costs/assistance_costs_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/transparency/beneficiaries_quantitative/beneficiaries_quantitative_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/transparency/contingency_index/contingency_index_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/transparency/demonstatives_results/demonstatives_results_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/transparency/loss_ratio/loss_ratio.cubit.dart';
import 'package:cooperado_minha_unimed/bloc/update-version/update_version_cubit.dart';
import 'package:cooperado_minha_unimed/screens/splash/splash_screen.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/services/version.service.dart';
import 'package:cooperado_minha_unimed/shared/utils/objectbox_utils.dart';
import 'package:cooperado_minha_unimed/shared/utils/router_observer.dart';
import 'package:cooperado_minha_unimed/shared/widgets/comparative-production/bloc/comparative_production_cubit.dart';
import 'package:cooperado_minha_unimed/shared/widgets/flavor_banner.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:personal_assistente_digital/utils/blocs_load.dart';

class CooperadoUnimed extends StatelessWidget {
  CooperadoUnimed({super.key}) {
    Locator.setup();

    ObjectBoxUtils.initializeStore();
    Locator.instance!.get<VersionService>().getInfo();
    // Escutando notificacoes recebidas

    FirebaseMessaging.instance.requestPermission();
  }

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  static FirebaseAnalyticsObserver observer =
      FirebaseAnalyticsObserver(analytics: analytics);

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    return MultiBlocProvider(
      providers: [
        BlocProvider<AuthCubit>(create: (context) => AuthCubit()),
        BlocProvider<ProfileCubit>(create: (context) => ProfileCubit()),
        BlocProvider<ComparativeProductionCubit>(
          create: (context) => ComparativeProductionCubit(),
        ),
        BlocProvider<DirectorsCubit>(create: (context) => DirectorsCubit()),
        BlocProvider<AssistanceCostsCubit>(
            create: (context) => AssistanceCostsCubit()),
        BlocProvider<LossRatioCubit>(create: (context) => LossRatioCubit()),
        BlocProvider<ContingencyIndexCubit>(
            create: (context) => ContingencyIndexCubit()),
        BlocProvider<DemonstrativesResultsCubit>(
            create: (context) => DemonstrativesResultsCubit()),
        BlocProvider<BeneficiariesQuantitativeCubit>(
            create: (context) => BeneficiariesQuantitativeCubit()),
        BlocProvider<CostComparativeCubit>(
            create: (context) => CostComparativeCubit()),
        BlocProvider<MedicalProductionCubit>(
            create: (context) => MedicalProductionCubit()),
        BlocProvider<DailyAttendanceCubit>(
            create: (context) => DailyAttendanceCubit()),
        BlocProvider<HistoricalProductionCubit>(
            create: (context) => HistoricalProductionCubit()),
        BlocProvider<AttendanceComparativeCubit>(
            create: (context) => AttendanceComparativeCubit()),
        BlocProvider<LastProductionCubit>(
            create: (context) => LastProductionCubit()),
        BlocProvider<ExtractQuotaPartCubit>(
            create: (context) => ExtractQuotaPartCubit()),
        BlocProvider<ReportProductionCubit>(
            create: (context) => ReportProductionCubit()),
        BlocProvider<SolicitationParticipationStatusCubit>(
            create: (context) => SolicitationParticipationStatusCubit()),
        BlocProvider<ConsultChbpmCubit>(
            create: (context) => ConsultChbpmCubit()),
        BlocProvider<ConsultOpmeCubit>(create: (context) => ConsultOpmeCubit()),
        BlocProvider<ConsultCidCubit>(create: (context) => ConsultCidCubit()),
        BlocProvider<IRDeclarationCubit>(
            create: (context) => IRDeclarationCubit()),
        BlocProvider<ScoreComparisonCubit>(
            create: (context) => ScoreComparisonCubit()),
        BlocProvider<ConfigProfileCubit>(
            create: (context) => ConfigProfileCubit()),
        BlocProvider<NewsCubit>(create: (context) => NewsCubit()),
        BlocProvider<TransparencyCubit>(
            create: (context) => TransparencyCubit()),
        BlocProvider<CouncilCubit>(create: (context) => CouncilCubit()),
        BlocProvider<OtherCubit>(create: (context) => OtherCubit()),
        BlocProvider<ReadCubit>(
          create: (context) => ReadCubit(),
        ),
        BlocProvider<FiscalCouncilCubit>(
            create: (context) => FiscalCouncilCubit()),
        BlocProvider<FormCouncilCubit>(create: (context) => FormCouncilCubit()),
        BlocProvider<SendFormCubit>(create: (context) => SendFormCubit()),
        BlocProvider<PdfFactoryCubit>(create: (context) => PdfFactoryCubit()),
        BlocProvider<UpdateVersionCubit>(
            create: (context) => UpdateVersionCubit()),
        BlocProvider<RegisterCubit>(
          create: (context) => RegisterCubit(),
        ),
        BlocProvider<ForgotPasswordCubit>(
          create: (context) => ForgotPasswordCubit(),
        ),
        BlocProvider<RedefinePasswordCubit>(
          create: (context) => RedefinePasswordCubit(),
        ),
        BlocProvider<ThemeCubit>(create: (context) => ThemeCubit()),
        BlocProvider<AgreeTermsCubit>(create: (context) => AgreeTermsCubit()),
        BlocProvider<EconomicIndicatorsCubit>(
            create: (context) => EconomicIndicatorsCubit()),
        BlocProvider<SplashScreenCubit>(
            create: (context) => SplashScreenCubit()),
        BlocProvider<MyInvoicesCubit>(create: (context) => MyInvoicesCubit()),
        BlocProvider<InvoiceCubit>(create: (context) => InvoiceCubit()),
        BlocProvider<BeneficiaryCubit>(create: (context) => BeneficiaryCubit()),
        BlocProvider<HonorarioCubit>(create: (context) => HonorarioCubit()),
        BlocProvider<ChannelEthicsCubit>(
            create: (context) => ChannelEthicsCubit()),
        BlocProvider<MedicinesVisibilityCubit>(
            create: (context) => MedicinesVisibilityCubit()),
        BlocProvider<MedicinesCubit>(create: (context) => MedicinesCubit()),
        BlocProvider<SearchMedicinesCubit>(
            create: (context) => SearchMedicinesCubit()),
        BlocProvider<ResSolicitationCubit>(
            create: (context) => ResSolicitationCubit()),
        BlocProvider<ResLabExamCubit>(create: (context) => ResLabExamCubit()),
        BlocProvider<ResLabExamDetailCubit>(
            create: (context) => ResLabExamDetailCubit()),
        BlocProvider<ResImageExamResultCubit>(
            create: (context) => ResImageExamResultCubit()),
        BlocProvider<OfficeNoticesCubit>(
            create: (context) => OfficeNoticesCubit()),
        BlocProvider<ResAllergieCubit>(create: (context) => ResAllergieCubit()),
        BlocProvider<ResConfigCubit>(create: (context) => ResConfigCubit()),
        BlocProvider<ResProcedureCubit>(
            create: (context) => ResProcedureCubit()),
        BlocProvider<ResDocumentsCubit>(
            create: (context) => ResDocumentsCubit()),
        BlocProvider<ResProcedureCubit>(
            create: (context) => ResProcedureCubit()),
        BlocProvider<ResProcedureDetailCubit>(
            create: (context) => ResProcedureDetailCubit()),
        BlocProvider<ResDocumentDetailCubit>(
            create: (context) => ResDocumentDetailCubit()),
        BlocProvider<ResBrasilAtendimentoResultCubit>(
            create: (context) => ResBrasilAtendimentoResultCubit()),
        BlocProvider<ResBrazilDiagnosticoResultCubit>(
            create: (context) => ResBrazilDiagnosticoResultCubit()),
        BlocProvider<ResExamResultCubit>(
            create: (context) => ResExamResultCubit()),
        BlocProvider<ResExamResultDetailCubit>(
            create: (context) => ResExamResultDetailCubit()),
        BlocProvider<ResBrazilBeneficiaryCubit>(
            create: (context) => ResBrazilBeneficiaryCubit()),
        BlocProvider<ResBrazilDetailExamesfisicosResultCubit>(
            create: (context) => ResBrazilDetailExamesfisicosResultCubit()),
        BlocProvider<ResBrazilBeneficiaryCubit>(
            create: (context) => ResBrazilBeneficiaryCubit()),
        BlocProvider<ResBrazilAllertsCubit>(
            create: (context) => ResBrazilAllertsCubit()),
        BlocProvider(create: (_) => SensitiveDataCubit()),
        BlocProvider<ResIndicatorsCubit>(
            create: (context) => ResIndicatorsCubit()),
        BlocProvider<ResIndsicatorsDataCubit>(
            create: (context) => ResIndsicatorsDataCubit()),
        BlocProvider<LocationPermissionCubit>(
            create: (context) => LocationPermissionCubit()),
        BlocProvider<EcardActivationCubit>(
            create: (context) => EcardActivationCubit()),
        BlocProvider<EcardFinishCubit>(create: (context) => EcardFinishCubit()),
        BlocProvider<EcardActivationCheckCubit>(
          create: (context) => EcardActivationCheckCubit(),
        ),
        BlocProvider<NotificacaoCubit>(create: (context) => NotificacaoCubit()),
        BlocProvider<NotificationsCountCubit>(
            create: (context) => NotificationsCountCubit()),
        BlocProvider<TimerEcardCubit>(create: (context) => TimerEcardCubit()),
        BlocProvider<HistoryEcardActivationCubit>(
            create: (context) => HistoryEcardActivationCubit()),
        BlocProvider<ClubMoreBenefitsCubit>(
            create: (context) => ClubMoreBenefitsCubit()),
        BlocProvider<SupportCooperativeCubit>(
            create: (context) => SupportCooperativeCubit()),
        BlocProvider<GlosaResourceCubit>(
            create: (context) => GlosaResourceCubit()),
        BlocProvider<AddGlosaResourceCubit>(
            create: (context) => AddGlosaResourceCubit()),
        BlocProvider<GlosaResourceAddFileCubit>(
            create: (context) => GlosaResourceAddFileCubit()),
      ],
      child: BlocsLoad(
        child: BlocBuilder<ThemeCubit, ThemeState>(
          builder: (context, state) {
            return MaterialApp(
              title: 'Cooperado Unimed',
              theme: context.read<ThemeCubit>().themeData,
              localizationsDelegates: const [
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
                DefaultCupertinoLocalizations.delegate,
              ],
              supportedLocales: const [
                Locale('pt', 'BR'),
              ],
              home: FlavorBanner(
                  child: SplashScreen(
                analytics: analytics,
                observer: observer,
              )),
              navigatorObservers: [routeObserver, observer],
            );
          },
        ),
      ),
    );
  }
}

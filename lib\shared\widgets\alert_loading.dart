import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class CooperadoAlertLoading extends StatelessWidget {
  const CooperadoAlertLoading({super.key});

  @override
  Widget build(context) {
    return const AlertDialog(
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(10),
              topRight: Radius.circular(10),
              bottomLeft: Radius.circular(10))),
      content: SizedBox(
        width: 100,
        height: 100,
        child: SpinKitCircle(
          color: CooperadoColors.tealGreen,
        ),
      ),
      backgroundColor: Colors.white,
    );
  }
}

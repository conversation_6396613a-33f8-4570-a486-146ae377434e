import 'package:cooperado_minha_unimed/models/fiscal_council.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  FiscalCouncil? modelTest;
  CouncilTopics? modelTest2;
  Map<String, dynamic>? json;
  Map<String, dynamic>? json2;
  Map<String, dynamic>? jsonTestUnitFiscalCouncilModel;

  setUpAll(
    () {
      modelTest = FiscalCouncil(
          nome: "nome",
          cargo: "cargo",
          crm: "crm",
          especialidade: "especialidade",
          imagem:
              "https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/04/<PERSON>-<PERSON>.png",
          thumb:
              "https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/04/Anderson-Costa.png");
      modelTest2 = CouncilTopics(
          codigo: 1,
          descricao: "descricao",
          servico: Servico(codigo: 1, descricao: null));

      json = {
        "conselheiros": [
          {
            "nome": "<PERSON>",
            "cargo": "Coordenador",
            "crm": "13991",
            "especialidade": "Anestesiologia",
            "imagem":
                "https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/04/Anderson-Costa.png",
            "thumb":
                "https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/04/Anderson-Costa.png"
          },
          {
            "nome": "Rogean Rodrigues Nunes",
            "cargo": "Secretário",
            "crm": "5100",
            "especialidade": "Anestesiologia",
            "imagem":
                "https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/04/Rogean-Nunes.png",
            "thumb":
                "https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/04/Rogean-Nunes.png"
          },
        ]
      };

      jsonTestUnitFiscalCouncilModel = {
        "nome": "Rogean Rodrigues Nunes",
        "cargo": "Secretário",
        "crm": "5100",
        "especialidade": "Anestesiologia",
        "imagem":
            "https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/04/Rogean-Nunes.png",
        "thumb":
            "https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/04/Rogean-Nunes.png"
      };
      json2 = {
        "codigo": 1,
        "descricao": "descricao",
        "servico": {"codigo": 1, "descricao": "aaaa"}
      };
    },
  );

  group(
    "isInstanceOf FiscalCouncil model tests",
    () {
      test("Should be return instance of FiscalCouncil", () {
        expect(modelTest, isInstanceOf<FiscalCouncil>());
      });

      test("Should be return instance of String", () {
        expect(modelTest!.especialidade, isInstanceOf<String>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of FiscalCouncil to json", () {
      expect(modelTest!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of FiscalCouncil from json", () {
      expect(FiscalCouncil.fromJson(jsonTestUnitFiscalCouncilModel!),
          isInstanceOf<FiscalCouncil>());
    });
  });
  group(
    "Other tests",
    () {
      test("Should be return true", () {
        expect(modelTest!.imagem!.contains("https://"), true);
      });
    },
  );

  group(
    "isInstanceOf CouncilTopics model tests",
    () {
      test("Should be return instance of CouncilTopics", () {
        expect(modelTest2, isInstanceOf<CouncilTopics>());
      });

      test("Should be return instance of String", () {
        expect(modelTest2!.descricao, isInstanceOf<String>());
      });

      test("Should be return instance of Servico", () {
        expect(modelTest2!.servico, isInstanceOf<Servico>());
      });

      test("Should be return instance of Int", () {
        expect(modelTest2!.servico!.codigo, isInstanceOf<int>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of CouncilTopics to json", () {
      expect(modelTest2!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of CouncilTopics from json", () {
      expect(CouncilTopics.fromJson(json2!), isInstanceOf<CouncilTopics>());
    });

    test("Should be return instance of Servico to json", () {
      expect(
          modelTest2!.servico!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of Servico from json", () {
      expect(Servico.fromJson(json2!["servico"]), isInstanceOf<Servico>());
    });
  });

  group("Json test", () {
    test("Type json", () {
      expect(json!["conselheiros"][0]["cargo"], isInstanceOf<String>());
      expect(json!["conselheiros"][0]["nome"], isInstanceOf<String>());
      expect(json!["conselheiros"][0]["crm"], isInstanceOf<String>());
      expect(json!["conselheiros"][0]["especialidade"], isInstanceOf<String>());
      expect(json!["conselheiros"][0]["imagem"], isInstanceOf<String>());
      expect(json!["conselheiros"][0]["thumb"], isInstanceOf<String>());

      expect(json2!["codigo"], isInstanceOf<int>());
      expect(json2!["descricao"], isInstanceOf<String>());
      expect(json2!["servico"]["codigo"], isInstanceOf<int>());
      expect(json2!["servico"]["descricao"], isInstanceOf<String>());
    });

    test("Can´t return if is null", () {
      expect(json!["conselheiros"][0]["nome"] == null, false);
      expect(json!["conselheiros"][0]["cargo"] == null, false);
      expect(json!["conselheiros"][0]["crm"] == null, false);
      expect(json!["conselheiros"][0]["especialidade"] == null, false);
      expect(json!["conselheiros"][0]["imagem"] == null, false);
      expect(json!["conselheiros"][0]["thumb"] == null, false);

      expect(json2!["codigo"] == null, false);
      expect(json2!["descricao"] == null, false);
      expect(json2!["servico"]["codigo"] == null, false);
      expect(json2!["servico"]["descricao"] == null, false);
    });
  });
  group(
    "Other tests",
    () {
      test("Should be return imagem https:// true", () {
        expect(modelTest!.imagem!.contains("https://"), true);
        expect(json!["conselheiros"][0]["imagem"].contains("https://"), true);
      });
      test("Should be return thumb https:// true", () {
        expect(modelTest!.thumb!.contains("https://"), true);
        expect(json!["conselheiros"][0]["thumb"].contains("https://"), true);
      });

      test("Should be return true png extension imagem", () {
        expect(modelTest!.imagem!.contains(".png"), true);
        expect(json!["conselheiros"][0]["imagem"].contains(".png"), true);
      });
      test("Should be return true png extension thumb", () {
        expect(modelTest!.thumb!.contains(".png"), true);
        expect(json!["conselheiros"][0]["thumb"].contains(".png"), true);
      });
    },
  );
}

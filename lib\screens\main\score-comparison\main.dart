import 'package:cooperado_minha_unimed/bloc/score-comparison/score_comparison_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/main/score-comparison/body_card.dart';
import 'package:cooperado_minha_unimed/screens/main/score-comparison/score_history.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/vo/score_comparison.vo.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class ScoreComparisonScreen extends StatefulWidget {
  const ScoreComparisonScreen({super.key});

  @override
  ScoreComparisonScreenState createState() => ScoreComparisonScreenState();
}

class ScoreComparisonScreenState extends State<ScoreComparisonScreen> {
  double _animatedHeight = 0.0;
  bool _expandedMode = false;

  @override
  void initState() {
    context.read<ScoreComparisonCubit>().getScoreComparisonEvent();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Text(
                      "Cooperativo",
                      style: TextStyle(
                        color: CooperadoColors.blackText,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _iconRefresh()
                ],
              ),
              _loadInfo()
            ],
          ),
        ),
      ),
    );
  }

  Widget _iconRefresh() {
    return BlocBuilder<ScoreComparisonCubit, ScoreComparisonState>(
        builder: (context, state) {
      if (state is ErrorGetScoreComparisonState) {
        return IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: () =>
              context.read<ScoreComparisonCubit>().getScoreComparisonEvent(),
        );
      } else {
        return Container();
      }
    });
  }

  Widget _loadInfo() {
  return BlocBuilder<ScoreComparisonCubit, ScoreComparisonState>(
    builder: (context, scoreState) {
      return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
        builder: (context, sensitiveState) {
          if (scoreState is DoneGetScoreComparisonState) {
            return Column(
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      const Text(
                        "Sua pontuação geral",
                        style: TextStyle(color: CooperadoColors.grayLight2),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: <Widget>[
                          Text(
                            !sensitiveState.isSensitiveDataVisible
                                ? '******'
                                : scoreState.responseScore?.scoreFormatted ?? '',
                            style: const TextStyle(
                                color: CooperadoColors.limaColor, fontSize: 50),
                          ),
                          const Text(
                            "PONTOS",
                            style: TextStyle(fontSize: 12),
                          )
                        ],
                      )
                    ],
                  ),
                ),
                _success(scoreState.responseScore),
              ],
            );
          } else if (scoreState is LoadingGetScoreComparisonState) {
            return const SpinKitCircle(
              color: CooperadoColors.tealGreen,
            );
          } else if (scoreState is ErrorGetScoreComparisonState) {
            return IntrinsicHeight(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Expanded(child: ErrorBanner(message: scoreState.message))
                ],
              ),
            );
          } else {
            return Container();
          }
        },
      );
    },
  );
}

  Widget _success(responseScore) {
    return Column(
      children: <Widget>[
        _expandedMode ? _expanded(responseScore) : Container(),
        GestureDetector(
          onTap: () => setState(() {
            _animatedHeight != 0.0
                ? _animatedHeight = 0.0
                : _animatedHeight = 150;
            _expandedMode = !_expandedMode;
          }),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.4,
            padding: const EdgeInsets.all(8.0),
            decoration: const BoxDecoration(
                color: CooperadoColors.tealGreen,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20))),
            child: Text(
              _expandedMode ? "VER MENOS" : "VEJA MAIS",
              textAlign: TextAlign.center,
              style: const TextStyle(color: CooperadoColors.grayLight),
            ),
          ),
        ),
      ],
    );
  }

  Widget _expanded(ResponseScore responseScore) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: <Widget>[
        BodyCard(
          list: responseScore.categorias,
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: CooperadoColors.tealGreenSecondary,
            ),
            onPressed: () {
              Navigator.push(
                  context,
                  FadeRoute(
                      page: ScoreHistory(
                    responseScore: responseScore,
                  )));
            },
            child: const Text("VER HISTÓRICO"),
          ),
        )
      ],
    );
  }
}

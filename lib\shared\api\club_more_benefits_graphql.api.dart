import 'dart:async';

import 'package:cooperado_minha_unimed/models/club-mais-vantagens.model.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/messages.exceptions.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:graphql/client.dart';
import 'package:http_client/http_client.dart';

class ClubMoreBenefitsGraphQlApi {
  final UnimedHttpClient httpClient;

  ClubMoreBenefitsGraphQlApi(this.httpClient);

  final logger = UnimedLogger(className: 'GraphQlApi');

  Future<GraphQLClient> getGithubGraphQLClient() async {
    final tokenPerfilApps = await Locator.instance!.get<AuthApi>().tokenPerfilApps();

    final Link link = HttpLink(
      FlavorConfig.instance!.values.graphql.url,
      defaultHeaders: {
        'Authorization': 'Bearer $tokenPerfilApps',
      },
    );

    return GraphQLClient(
      cache: GraphQLCache(),
      link: link,
      queryRequestTimeout: const Duration(seconds: 120),
    );
  }

  Future<ClubMaisVantagensModel> checkBenefitsClubUserRegistration() async {
    try {
      UserCredentials? credentials = await Locator.instance!.get<AuthApi>().getCredentials();
      final token = credentials != null ? await User.createToken(credentials: credentials) : "";

      final GraphQLClient client = await getGithubGraphQLClient();

      String query;
      query = '''
      query CheckBenefitsClubUserRegistration {
        cooperadoCheckBenefitsClubUserRegistration(
            token: "$token"
        ) {
            registeredUser
            redirectLink
            phoneFormatted
            email
        }
    }

    ''';

      logger.i('checkBenefitsClubUserRegistration query : $query');

      final options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final result = await client.query(options).timeout(const Duration(seconds: 120));

      if (result.hasException) {
        logger.e('checkBenefitsClubUserRegistration exception : ${result.exception}');
        throw UnimedException('Não foi possível no momento.');
      } else {
        final data = result.data!['cooperadoCheckBenefitsClubUserRegistration'];
        logger.d('checkBenefitsClubUserRegistration success');

        return ClubMaisVantagensModel.fromJson(data);
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException checkBenefitsClubUserRegistration -  $e');
      throw UnimedException(MessageException.general);
    } on UnimedException catch (ex) {
      logger.e('checkBenefitsClubUserRegistration ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('checkBenefitsClubUserRegistration exception : $ex');
      throw UnimedException('Não foi possível no momento.');
    }
  }

  Future<void> registerBenefitsClubUser() async {
    try {
      UserCredentials? credentials = await Locator.instance!.get<AuthApi>().getCredentials();
      final token = credentials != null ? await User.createToken(credentials: credentials) : "";

      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
          query CooperadoCheckBenefitsClubUserRegistration {
            cooperadoRegisterBenefitsClubUser(
                token: "$token"
              ) {
              message
            } 
          }
      ''';

      logger.i('registerBenefitsClubUser query : $query');

      final options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final result = await client.query(options).timeout(const Duration(seconds: 120));

      if (result.hasException) {
        logger.e('registerBenefitsClubUser exception : ${result.exception}');
        throw GraphQlException(result.exception?.graphqlErrors.first.message ?? MessageException.general);
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException registerBenefitsClubUser -  $e');
      throw UnimedException(MessageException.general);
    } on UnimedException catch (ex) {
      logger.e('registerBenefitsClubUser ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('registerBenefitsClubUser exception : $ex');
      throw UnimedException(ex.toString());
    }
  }
}

import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/forgot-password/redefine_password.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/widgets/elevated_button_custom.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:header_login/header_login.dart';

const String clientId = String.fromEnvironment('CLIENT_ID');

class ResetPasswordMessageScreen extends StatefulWidget {
  final String crm;
  final String message;
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const ResetPasswordMessageScreen({
    super.key,
    required this.crm,
    required this.message,
    required this.analytics,
    required this.observer,
  });
  @override
  ResetPasswordMessageScreenState createState() =>
      ResetPasswordMessageScreenState();
}

class ResetPasswordMessageScreenState
    extends State<ResetPasswordMessageScreen> {
  @override
  void initState() {
    super.initState();
    widget.analytics.logScreenView(
      screenName: 'Redefinir senha (Mensagem de envio de código)',
      screenClass: 'ResetPasswordMessageScreen',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CooperadoColors.grayLight,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0.0,
      ),
      body: SafeArea(
        top: false,
        child: Column(
          children: [
            HeaderLogin(
              clientId: clientId,
              innerCircleColor: CooperadoColors.tealGreen,
              outerCircleColor: CooperadoColors.tealGreenSecondary,
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20.0).copyWith(top: 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    const Icon(
                      Icons.email,
                      size: 100,
                      color: CooperadoColors.tealGreenSecondary,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 16, bottom: 16),
                      child: Html(
                        data: '<div>${widget.message}</div>',
                        style: {
                          "div": Style(
                            color: CooperadoColors.tealGreen,
                            fontSize: FontSize(18),
                            textAlign: TextAlign.center,
                          ),
                        },
                      ),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width,
                      child: ElevatedButtonCustom(
                        title: 'Inserir Código',
                        onPressed: () {
                          Navigator.push(
                            context,
                            FadeRoute(
                              page: RedefinePasswordScreen(
                                crm: widget.crm,
                                analytics: widget.analytics,
                                observer: widget.observer,
                              ),
                            ),
                          );
                        },
                      ),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

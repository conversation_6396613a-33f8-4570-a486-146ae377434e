class QuantitativoBeneficiariosVO {
  List<QuantitativoSexos> quantitativoSexos;
  List<QuantitativoPlanos> quantitativoPlanos;
  List<QuantitativoFaixasEtarias> quantitativoFaixasEtarias;
  int quantidadeBeneficiarioAtivos;

  QuantitativoBeneficiariosVO({
    List<QuantitativoSexos>? quantitativoSexos,
    List<QuantitativoPlanos>? quantitativoPlanos,
    List<QuantitativoFaixasEtarias>? quantitativoFaixasEtarias,
    int? quantidadeBeneficiarioAtivos,
  })  : quantitativoSexos = quantitativoSexos ?? [],
        quantitativoPlanos = quantitativoPlanos ?? [],
        quantitativoFaixasEtarias = quantitativoFaixasEtarias ?? [],
        quantidadeBeneficiarioAtivos = quantidadeBeneficiarioAtivos ?? 0;

  QuantitativoBeneficiariosVO.fromJson(Map<String, dynamic> json)
      : quantitativoSexos = (json['quantitativoSexos'] as List<dynamic>?)
                ?.map((e) => QuantitativoSexos.fromJson(e))
                .toList() ??
            [],
        quantitativoPlanos = (json['quantitativoPlanos'] as List<dynamic>?)
                ?.map((e) => QuantitativoPlanos.fromJson(e))
                .toList() ??
            [],
        quantitativoFaixasEtarias =
            (json['quantitativoFaixasEtarias'] as List<dynamic>?)
                    ?.map((e) => QuantitativoFaixasEtarias.fromJson(e))
                    .toList() ??
                [],
        quantidadeBeneficiarioAtivos =
            json['quantidadeBeneficiarioAtivos'] ?? 0;

  Map<String, dynamic> toJson() {
    return {
      'quantitativoSexos': quantitativoSexos.map((e) => e.toJson()).toList(),
      'quantitativoPlanos': quantitativoPlanos.map((e) => e.toJson()).toList(),
      'quantitativoFaixasEtarias':
          quantitativoFaixasEtarias.map((e) => e.toJson()).toList(),
      'quantidadeBeneficiarioAtivos': quantidadeBeneficiarioAtivos,
    };
  }
}

class QuantitativoSexos {
  String descricao;
  int quantidade;

  QuantitativoSexos({
    required this.descricao,
    required this.quantidade,
  });

  QuantitativoSexos.fromJson(Map<String, dynamic> json)
      : descricao = json['descricao'],
        quantidade = json['quantidadeSexo'];

  Map<String, dynamic> toJson() => {
        'descricao': descricao,
        'quantidadeSexo': quantidade,
      };
}

class QuantitativoPlanos {
  String descricao;
  int quantidade;

  QuantitativoPlanos({
    required this.descricao,
    required this.quantidade,
  });

  QuantitativoPlanos.fromJson(Map<String, dynamic> json)
      : descricao = json['descricao'],
        quantidade = json['quantidadePlano'];

  Map<String, dynamic> toJson() => {
        'descricao': descricao,
        'quantidadePlano': quantidade,
      };
}

class QuantitativoFaixasEtarias {
  String descricao;
  int quantidade;

  QuantitativoFaixasEtarias({
    required this.descricao,
    required this.quantidade,
  });

  QuantitativoFaixasEtarias.fromJson(Map<String, dynamic> json)
      : descricao = json['descricao'],
        quantidade = json['quantidadeFaixaEtaria'];

  Map<String, dynamic> toJson() => {
        'descricao': descricao,
        'quantidadeFaixaEtaria': quantidade,
      };
}

import 'dart:io';

import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:http_client/exceptions/exceptions.dart';
import 'package:http_client/http_client.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart';

class PdfApi {
  final logger = UnimedLogger(className: 'PdfApi');
  final UnimedHttpClient httpClient;
  PdfApi(this.httpClient);

  Future<File> createPDFFileFromUrl(String url, String filename) async {
    try {
      final documentDirectory = (await getApplicationDocumentsDirectory()).path;
      File file = File(
          '$documentDirectory/${DateTime.now().millisecondsSinceEpoch.toString()}_$filename');
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();

      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json; charset=UTF-8",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );
      if (response.statusCode == 200) {
        logger.d(
            'createPDFFileFromUrl success | response ${response.statusCode} bodybytes pdf');
        file.writeAsBytesSync(response.bodyBytes);
        return file;
      } else {
        logger.e(
            'createPDFFileFromUrl - statusCode : ${response.statusCode} ${response.body}');
        throw PdfException('Serviço indisponível no momento.');
      }
    } on UnimedException catch (ex) {
      throw PdfException(ex.message);
    } catch (ex) {
      logger.e('_getFileFromUrl catch exception - $ex - url: $url');
      throw PdfException('Não foi possível no momento.');
    }
  }

  Future<File> getFirebaseFile(String url, String filename) async {
    try {
      final request = await HttpClient().getUrl(Uri.parse(url));
      final response = await request.close();

      final documentDirectory = (await getApplicationDocumentsDirectory()).path;
      final path = join(documentDirectory,
          '${DateTime.now().millisecondsSinceEpoch}_$filename');
      await response.pipe(File(path).openWrite());
      return File(path);
    } on SocketException catch (error) {
      logger.e('getFirebaseFile SocketException $error');
      throw NoInternetException();
    } on NoInternetException catch (error) {
      logger.e('getFirebaseFile NoInternetException $error');
      rethrow;
    } on ServiceTimeoutException catch (error) {
      logger.e('getFirebaseFile ServiceTimeoutException $error');
      rethrow;
    } on UnimedException catch (ex) {
      throw PdfException(ex.message);
    } catch (ex) {
      logger.e('getFirebaseFile catch exception - $ex - url: $url');
      throw PdfException('Não foi possível no momento.');
    }
  }
}

part of 'form_council_cubit.dart';

abstract class FormCouncilState extends Equatable {
  const FormCouncilState();

  @override
  List<Object?> get props => [];
}

class FormCouncilInitial extends FormCouncilState {}

class LoadingGetTopic extends FormCouncilState {}

//TODO adicionar um state de NODATA
class DoneGetTopic extends FormCouncilState {
  final List<CouncilTopics>? list;
  @override
  List<Object?> get props => [list];
  const DoneGetTopic({this.list});
}

class ErrorGetTopic extends FormCouncilState {
  final String? message;
  @override
  List<Object?> get props => [message];
  const ErrorGetTopic({this.message});
}

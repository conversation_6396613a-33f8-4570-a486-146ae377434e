part of 'res_lab_exam_cubit.dart';

abstract class ResLabExamState extends Equatable {
  const ResLabExamState();

  @override
  List<Object> get props => [];
}

class InitialResLabExamState extends ResLabExamState {}

class LoadingResLabExamState extends ResLabExamState {
  @override
  List<Object> get props => [];
}

class ErrorResLabExamState extends ResLabExamState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResLabExamState({required this.message});
}

class LoadedResLabExamState extends ResLabExamState {
  final List<ResLabExamModel> listLabExams;

  @override
  List<Object> get props => [listLabExams];

  const LoadedResLabExamState({required this.listLabExams});
}

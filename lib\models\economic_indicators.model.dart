
class EconomicIndicatorsModel {
  late String jul;
  late String jun;
late   String theSet;
 late  String ago;
 late  String out;
 late  int objetivo;
late   String abr;
 late  String mai;
 late  String nov;
 late  String jan;
late   String fev;
late   String nuAno;
late   String dez;
late   int formula;
 late  String dsIndicador;
late   String mar;

  EconomicIndicatorsModel({
     required this.jul,
    required this.jun,
   required this.theSet,
  required  this.ago,
   required this.out,
  required  this.objetivo,
   required this.abr,
  required  this.mai,
 required   this.nov,
  required  this.jan,
  required  this.fev,
  required  this.nuAno,
  required  this.dez,
  required  this.formula,
  required  this.dsIndicador,
   required this.mar,
  });

  EconomicIndicatorsModel.fromJson(Map<String, dynamic> json){
        jul = (json['jul'] ?? "--").toString();
    jun = (json['jun']?? "--").toString();
    theSet = (json['set']?? "--").toString();
    ago = (json['ago']?? "--").toString();
    out = (json['out']?? "--").toString();
    objetivo = (json['objetivo']?? "--").toInt();
    abr = (json['abr'] ?? "--").toString();
    mai = (json['mai'] ?? "--").toString();
    nov = (json['nov'] ?? "--").toString();
    jan = (json['jan']?? "--").toString();
    fev = (json['fev']?? "--").toString();
    nuAno = (json['nu_ano']?? "--").toString();
    dez = (json['dez']?? "--").toString();
    formula = (json['formula']??"--").toInt();
    dsIndicador = (json['ds_indicador']??"--").toString();
    mar = (json['mar']??"--").toString();
  }


  Map<String, dynamic> toJson(){
  final data = <String, dynamic>{};
    data['jul'] = jul;
    data['jun'] = jun;
    data['set'] = theSet;
    data['ago'] = ago;
    data['out'] = out;
    data['objetivo'] = objetivo;
    data['abr'] = abr;
    data['mai'] = mai;
    data['nov'] = nov;
    data['jan'] = jan;
    data['fev'] = fev;
    data['nu_ano'] = nuAno;
    data['dez'] = dez;
    data['formula'] = formula;
    data['ds_indicador'] = dsIndicador;
    data['mar'] = mar;
    return data;

  }
}

import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/custom_button_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ExpandableCard extends StatefulWidget {
  final String pathIcon;
  final String title;
  final String subtitle;
  final List<Map<String, String>> additionalInfo;
  final List<CustomButtonCard>? buttons;
  final bool loading;
  final Function(bool)? onExpansionChanged;
  final ExpansionTileController controller;

  const ExpandableCard({
    super.key,
    required this.pathIcon,
    required this.title,
    required this.subtitle,
    required this.additionalInfo,
    this.onExpansionChanged,
    required this.controller,
    this.loading = false,
    this.buttons,
  });

  @override
  ExpandableCardState createState() => ExpandableCardState();
}

class ExpandableCardState extends State<ExpandableCard> with SingleTickerProviderStateMixin {
  @override
  Widget build(BuildContext context) {
    return Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: EdgeInsets.zero,
        elevation: 0,
        child: ExpansionTile(
          title: Text(
            widget.title,
            style: const TextStyle(
              color: CooperadoColors.blackText,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          subtitle: Text(
            widget.subtitle,
            style: const TextStyle(
              color: CooperadoColors.opcionalGray3,
              fontSize: 12,
            ),
          ),
          leading: SvgPicture.asset(
            widget.pathIcon,
            width: 32,
            height: 32,
          ),
          controller: widget.controller,
          onExpansionChanged: (value) {
            if (widget.onExpansionChanged != null) {
              widget.onExpansionChanged!(value);
            }
          },
          children: _buildExpandedContent(),
        ));
  }

  List<Widget> _buildExpandedContent() {
    return [
      if (widget.additionalInfo.isNotEmpty) const Divider(color: Colors.grey),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: widget.additionalInfo.map((info) {
          return SizedBox(
            width: MediaQuery.of(context).size.width,
            child: Padding(
              padding: const EdgeInsets.only(bottom: 12, left: 12, right: 12),
              child: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: '${info['title']!} : ',
                      style: const TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                        fontSize: 13,
                      ),
                    ),
                    TextSpan(
                      text: info['description']!,
                      style: const TextStyle(
                        color: CooperadoColors.grayLight7,
                        fontSize: 13,
                        overflow: TextOverflow.visible,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ),
      if (widget.buttons != null) ...[
        const Divider(color: Colors.grey),
        widget.loading
            ? const Padding(
                padding: EdgeInsets.only(bottom: 12, left: 12, right: 12),
                child: SizedBox(
                  height: 48,
                  child: SpinKitThreeBounce(color: CooperadoColors.tealGreen, size: 24),
                ),
              )
            : Padding(
                padding: const EdgeInsets.only(bottom: 12, left: 12, right: 12),
                child: SizedBox(
                  width: double.infinity,
                  child: Wrap(
                    spacing: 8.0,
                    runSpacing: 4.0,
                    children: widget.buttons!,
                  ),
                ),
              ),
      ],
    ];
  }
}

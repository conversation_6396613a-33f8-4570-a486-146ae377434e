import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';

class AlertEscolhaDataDialog extends StatefulWidget {
  const AlertEscolhaDataDialog(
      {super.key,
      required this.title,
      required this.onPressed,
      required this.textWidget,
      this.iconData = Icons.info_outline,
      this.textButton = "Ok",
      this.colorIcon = CooperadoColors.tealGreen,
      required this.valueDropDown});

  final IconData iconData;
  final Widget title;
  final VoidCallback onPressed;
  final Widget textWidget;
  final String textButton;
  final Color colorIcon;
  final String valueDropDown;
  @override
  AlertEscolhaDataDialogState createState() => AlertEscolhaDataDialogState();
}

class AlertEscolhaDataDialogState extends State<AlertEscolhaDataDialog> {
  String? _newValue = "";
  @override
  void initState() {
    _newValue = widget.valueDropDown;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
      title: Center(child: widget.title),
      content: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            widget.textWidget,
            const Divider(
              height: 5.0,
              color: Colors.transparent,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: <Widget>[
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      decoration: BoxDecoration(
                          border: Border.all(
                              color: CooperadoColors.tealGreenSecondary),
                          borderRadius: BorderRadius.circular(10)),
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                            enabledBorder: UnderlineInputBorder(
                                borderSide:
                                    BorderSide(color: Colors.transparent))),
                        isExpanded: true,
                        value: _newValue,
                        icon: const Icon(Icons.arrow_drop_down),
                        iconSize: 24,
                        elevation: 16,
                        style: const TextStyle(
                            color: CooperadoColors.tealGreenSecondary),
                        onChanged: (String? newValue) {
                          setState(() {
                            _newValue = newValue;
                          });
                        },
                        items: <String>[
                          '1',
                          '2',
                          '3',
                          '4',
                          '5',
                          '6',
                          '7',
                          '8',
                          '9',
                          '10',
                          '11'
                        ].map<DropdownMenuItem<String>>((String value) {
                          return DropdownMenuItem<String>(
                            value: value,
                            child: Text(value),
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    decoration: BoxDecoration(
                        border: Border.all(
                            color: CooperadoColors.tealGreenSecondary),
                        borderRadius: BorderRadius.circular(10)),
                    child: DropdownButtonFormField<String>(
                      isExpanded: true,
                      value: _newValue,
                      icon: const Icon(Icons.arrow_drop_down),
                      iconSize: 24,
                      elevation: 16,
                      style: const TextStyle(
                          color: CooperadoColors.tealGreenSecondary),
                      decoration: const InputDecoration(
                          enabledBorder: UnderlineInputBorder(
                              borderSide:
                                  BorderSide(color: Colors.transparent))),
                      onChanged: (String? newValue) {
                        setState(() {
                          _newValue = newValue;
                        });
                      },
                      items: <String>[
                        '1',
                        '2',
                        '3',
                        '4',
                        '5',
                        '6',
                        '7',
                        '8',
                        '9',
                        '10',
                        '11'
                      ].map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10.0),
            Wrap(
              direction: Axis.horizontal,
              crossAxisAlignment: WrapCrossAlignment.center,
              alignment: WrapAlignment.center,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(5.0),
                              side: const BorderSide(
                                  color: CooperadoColors.tealGreenSecondary)),
                          textStyle: const TextStyle(
                              color: CooperadoColors.tealGreenSecondary)),
                      onPressed: widget.onPressed,
                      child: const Text("Cancelar")),
                ),
                ElevatedButton(
                    style: ElevatedButton.styleFrom(
                        backgroundColor: CooperadoColors.tealGreenSecondary,
                        textStyle: const TextStyle(color: Colors.white)),
                    onPressed: widget.onPressed,
                    child: Text(widget.textButton)),
              ],
            )
          ]),
      backgroundColor: Colors.white,
    );
  }
}

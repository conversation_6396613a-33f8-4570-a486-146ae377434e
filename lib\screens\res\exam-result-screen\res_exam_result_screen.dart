import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/exame-result/detail/res_exam_result_detail_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/exame-result/res_exam_result_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/exame-result/res_exam_result_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/res/res_exam_result_model.dart';
import 'package:cooperado_minha_unimed/screens/res/exam-result-screen/res_exam_result_image_detail.dart';
import 'package:cooperado_minha_unimed/screens/res/exam-result-screen/res_exam_result_laboratory_detail.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/app_bar_res.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/custom_button_card.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/empty_list.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/erro_service.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/expandable_card.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/filters_widget.dart';
import 'package:cooperado_minha_unimed/shared/utils/router_observer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:intl/intl.dart';

class ResExameResultScreen extends StatefulWidget {
  final String nameBeneficiary;
  final String crm;
  final String card;

  const ResExameResultScreen({
    super.key,
    required this.nameBeneficiary,
    required this.crm,
    required this.card,
  });

  @override
  State<ResExameResultScreen> createState() => _ResExameResultScreenState();
}

class _ResExameResultScreenState extends State<ResExameResultScreen>
    with RouteAware {
  FocusNode? focusTextFieldSearch;
  final bool _isLoading = false;
  int? _filterMonthSelected;
  DateTimeRange? _dateRangeToFilter;
  final List<int> _lastMonthsToFilter = [3, 6];

  List<ExpansionTileController> controllers = [];
  int? previusSelectedIndex;

  void _carregarExames() {
    setState(() {
      controllers = [];
      previusSelectedIndex = null;
    });

    context.read<ResExamResultCubit>().listResExamResult(
        crm: widget.crm, card: widget.card, dataRange: _dateRangeToFilter);
  }

  @override
  void initState() {
    super.initState();
    _carregarExames();
    focusTextFieldSearch = FocusNode();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void dispose() {
    focusTextFieldSearch!.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
        listeners: [
          BlocListener<AuthCubit, AuthState>(listener: (context, state) {
            if (state is LoadedAuthState) {
              _carregarExames();
            }
          }),
        ],
        child: Scaffold(
          appBar: AppBarRes(
            title: 'Resultados de Exames',
            nameBeneficiary: widget.nameBeneficiary,
          ),
          body: Padding(
            padding: const EdgeInsets.only(left: 20, right: 20, top: 24),
            child: Column(
              children: [
                FiltersWidget(
                  isLoading: _isLoading,
                  lastMonthsToFilter: _lastMonthsToFilter,
                  filterMonthSelected: _filterMonthSelected,
                  dateRangeToFilter: _dateRangeToFilter,
                  onMonthFilterChanged: (filterSelected) {
                    setState(() {
                      _filterMonthSelected = filterSelected;
                      filterSelected == null
                          ? _dateRangeToFilter = null
                          : _dateRangeToFilter = DateTimeRange(
                              start: _selectedDataStart(filterSelected),
                              end: DateTime.now(),
                            );
                    });
                    _carregarExames();
                  },
                  onClearDateRange: () {
                    setState(() {
                      _filterMonthSelected = null;
                      _dateRangeToFilter = null;
                      _carregarExames();
                    });
                  },
                  selectDateToFilter: _selectDateToFilter,
                  onDateRangeSelected: (dateRange) {
                    setState(() {
                      _filterMonthSelected = null;
                      _dateRangeToFilter = dateRange;
                    });
                    _carregarExames();
                  },
                ),
                const SizedBox(
                  height: 30,
                ),
                BlocBuilder<ResExamResultCubit, ResExamResultState>(
                  builder: (context, state) {
                    if (state is LoadedResExamResultState) {
                      if (state.listExamResult.isEmpty &&
                          (_filterMonthSelected != null ||
                              _dateRangeToFilter != null)) {
                        return const EmptyList(
                          pathIcon: 'assets/svg/icon_hospital.svg',
                          message: 'Não foi detectado nenhum exame.',
                        );
                      }
                      return Expanded(
                        child: ListView.builder(
                          itemCount: state.listExamResult.length,
                          itemBuilder: (context, index) {
                            ExpansionTileController controller =
                                ExpansionTileController();

                            controllers.add(controller);
                            final exame = state.listExamResult[index];
                            return ExpandableCard(
                              controller: controller,
                              pathIcon: 'assets/svg/icon_exam_result.svg',
                              title: exame.type?.toUpperCase() ?? '',
                              subtitle: exame.date != null
                                  ? DateFormat('dd/MM/yyyy - HH:mm')
                                      .format(DateTime.parse(exame.date!))
                                  : '',
                              additionalInfo: const [],
                              onExpansionChanged: (value) {
                                setState(() {
                                  if (value && previusSelectedIndex != index) {
                                    if (previusSelectedIndex != null) {
                                      controllers[previusSelectedIndex!]
                                          .collapse();
                                    }
                                    previusSelectedIndex = index;
                                  }
                                });
                              },
                              buttons: _buildButtons(exame),
                            );
                          },
                        ),
                      );
                    } else if (state is NoDataResExamResultState) {
                      return Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            EmptyList(
                              pathIcon: 'assets/svg/icon_file.svg',
                              message: _dateRangeToFilter != null
                                  ? 'Sem dados de exame para o período de ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.start)} até  ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.end)}'
                                  : 'Sem dados de exame.',
                            ),
                          ],
                        ),
                      );
                    } else if (state is ErrorResExamResultState) {
                      return SizedBox(
                        height: MediaQuery.of(context).size.height / 2,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ErroService(
                              message: state.message,
                              onPressed: () {
                                _carregarExames();
                              },
                            ),
                          ],
                        ),
                      );
                    } else if (state is LoadingResExamResultState) {
                      return const Expanded(
                        child: Center(
                            child: SpinKitCircle(
                                color: CooperadoColors.tealGreen)),
                      );
                    }
                    return Container();
                  },
                ),
              ],
            ),
          ),
        ));
  }

  List<CustomButtonCard> _buildButtons(ResExamResultModel exame) {
    List<CustomButtonCard> buttons = [];

    buttons.add(
      CustomButtonCard(
        text: 'Exame - TUSS: ${exame.codeTUSS}',
        onPressed: () {
          if (exame.type == 'laboratorial') {
            context
                .read<ResExamResultDetailCubit>()
                .getExamResultLaboratoryDetail(
                  crm: widget.crm, // Passa o CRM recebido via construtor
                  card: widget.card, // Passa o cartão recebido via construtor
                  code: exame.codeDetail,
                );
          } else {
            context.read<ResExamResultDetailCubit>().getExamResultImageDetail(
                  crm: widget.crm, // Passa o CRM recebido via construtor
                  card: widget.card, // Passa o cartão recebido via construtor
                  code: exame.codeDetail,
                );
          }

          _showDetailsBottomSheet(
            context,
            exame.codeTUSS ?? '',
            exame.codeDetail,
          );
        },
      ),
    );

    return buttons;
  }

  DateTime _selectedDataStart(int meses) {
    DateTime dataAtual = DateTime.now();
    DateTime novaData = DateTime(
      dataAtual.year,
      dataAtual.month - meses,
      dataAtual.day,
      dataAtual.hour,
      dataAtual.minute,
      dataAtual.second,
      dataAtual.millisecond,
      dataAtual.microsecond,
    );

    return novaData;
  }

  void _showDetailsBottomSheet(BuildContext context, String tuss, String code) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(15))),
      builder: (BuildContext context) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            BlocBuilder<ResExamResultDetailCubit, ResExamResultDetailState>(
              builder: (context, state) {
                if (state is LoadingResExamResultDetailState) {
                  return Column(
                    children: [
                      Center(
                        child: Container(
                          height: 4,
                          width: 100,
                          margin: const EdgeInsets.symmetric(
                            vertical: 10,
                          ),
                          decoration: BoxDecoration(
                            color: CooperadoColors.grayLight3,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),
                      const Center(
                        child: SpinKitCircle(color: CooperadoColors.tealGreen),
                      ),
                      const SizedBox(height: 16),
                    ],
                  );
                } else if (state is LoadedResExamResultLaboratoryDetailState) {
                  return Expanded(
                    child: ExamResultLaboratoryDetail(
                      tuss: tuss,
                      listResultExam: state.resExamResultLaboratoryDetailModel,
                    ),
                  );
                } else if (state is LoadedResExamResultImageDetailState) {
                  return Expanded(
                    child: ExamResultImageDetail(
                      tuss: tuss,
                      listResultExam: state.resExamResultImageDetailModel,
                    ),
                  );
                } else if (state is ErrorResExamResultDetailState) {
                  return Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Center(
                        child: ErroService(
                      message: state.message,
                      onPressed: () {
                        context
                            .read<ResExamResultDetailCubit>()
                            .getExamResultLaboratoryDetail(
                              crm: widget
                                  .crm, // Passa o CRM recebido via construtor
                              card: widget
                                  .card, // Passa o cartão recebido via construtor
                              code: code,
                            );
                      },
                    )),
                  );
                } else if (state is NoDataResExamResultDetailState) {
                  return const Padding(
                    padding: EdgeInsets.all(12.0),
                    child: Center(
                      child: EmptyList(
                        pathIcon: 'assets/svg/icon_file.svg',
                        message: 'Sem dados de exame.',
                      ),
                    ),
                  );
                } else {
                  return Container();
                }
              },
            )
          ],
        );
      },
    );
  }

  DateTime _getFirstDate() {
    DateTime now = DateTime.now();
    int year = now.year;
    bool isLeapYear = (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    return now.subtract(Duration(days: isLeapYear ? 366 : 365));
  }

  Future<DateTime?> _selectDateToFilter(BuildContext context, String helpText,
      {DateTime? firstDate}) async {
    return await showDatePicker(
      context: context,
      helpText: helpText,
      initialDate: DateTime.now(),
      firstDate: firstDate ?? _getFirstDate(),
      lastDate: DateTime.now(),
      keyboardType: TextInputType.text,
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: const ColorScheme.light(
              primary: CooperadoColors.tealGreen,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );
  }
}

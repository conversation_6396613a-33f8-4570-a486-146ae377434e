part of 'directors_cubit.dart';

abstract class DirectorsState extends Equatable {
  const DirectorsState();
}

class InitialDirectorsState extends DirectorsState {
  @override
  List<Object> get props => [];
}

class LoadingDirectorsState extends DirectorsState {
  @override
  List<Object> get props => [];
}

class LoadedDirectorsState extends DirectorsState {
  final RetornoDiretoria? retornoDiretoria;
  final Diretores? president;
  final List<Diretores>? diretoriaNoPresident;

  @override
  List<Object?> get props =>
      [retor<PERSON><PERSON><PERSON><PERSON>, president, diretoriaNoPresident];

  const LoadedDirectorsState(
      {this.retornoDiretoria, this.president, this.diretoriaNoPresident});
}

class ErrorDirectorsState extends DirectorsState {
  final String? message;
  @override
  List<Object?> get props => [message];

  const ErrorDirectorsState(this.message);
}

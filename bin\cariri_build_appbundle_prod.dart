import 'dart:convert';
import 'dart:io';

void main(List<String> args) {
  // print('Hi args $args');
  print('Build Android PROD...');

  Process.start('flutter', [
    'build',
    'appbundle',
    '-t',
    'lib/main_cariri_prod.dart',
    '--flavor',
    'cariri',
    '--dart-define=CLIENT_ID=UNIMED_CARIRI',
    '--release',
    // '--verbose',
    ...args
  ]).then((Process process) {
    process.exitCode.then((exitCode) {
      print('exit code: $exitCode');
    });
    process.stdout.transform(utf8.decoder).listen((data) {
      print(data);
    });

    process.stderr.transform(utf8.decoder).listen((data) {
      print(data);
    });
  }).catchError((onError) {
    print('onError $onError');
  });
}

import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/config-profile/config_profile_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:cooperado_minha_unimed/shared/utils/validators.dart';
import 'package:cooperado_minha_unimed/shared/vo/profile/profile-payload.vo.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class AlertUpdateTelefone extends StatefulWidget {
  final Enderecos? address;
  final Contatos? contato;
  final Function? onPressed;
  const AlertUpdateTelefone(
      {super.key, this.contato, this.onPressed, this.address});
  @override
  AlertUState createState() => AlertUState();
}

class AlertUState extends State<AlertUpdateTelefone> {
  TextEditingController? controller;
  late MaskTextInputFormatter foneFormatter;
  FocusNode? focusTextFieldSearch;
  bool isPhoneValid = false;

  @override
  void initState() {
    final initialValue = StringUtils.formatPhone(widget.contato!.numeroContato);
    controller = TextEditingController(text: initialValue);
    foneFormatter = MaskTextInputFormatter(
      mask: '(00) 00000-0000',
      filter: {"0": RegExp(r'[0-9]')},
      initialText: initialValue,
    );
    isPhoneValid = TextFieldValidators.validadePhone(initialValue);
    context.read<ConfigProfileCubit>().setInitial();
    focusTextFieldSearch = FocusNode();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
      title: const Text('Atualizar Telefone/Celular'),
      content: Form(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: TextFormField(
                autofocus: true,
                focusNode: focusTextFieldSearch,
                controller: controller,
                keyboardType: TextInputType.number,
                style: const TextStyle(color: CooperadoColors.tealGreen),
                decoration: InputDecoration(
                    enabledBorder: _borderInput(),
                    focusedBorder: _borderInput(),
                    errorBorder: _borderInput(),
                    focusedErrorBorder: _borderInput(),
                    labelText: 'Telefone/Celular',
                    labelStyle:
                        const TextStyle(color: CooperadoColors.tealGreen)),
                inputFormatters: [foneFormatter],
                onChanged: (value) {
                  setState(() {
                    isPhoneValid = TextFieldValidators.validadePhone(value);
                  });
                },
              ),
            ),
            _indicatorStateUpadeContact()
          ],
        ),
      ),
      actions: [
        _buttonCancelar(),
        _buttonConfirmar(),
      ],
    );
  }

  Widget _buttonCancelar() {
    return BlocBuilder<ConfigProfileCubit, ConfigProfileState>(
      builder: (context, state) {
        if (state is UpdatingProfileState) {
          return ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: CooperadoColors.grayLight,
              ),
              onPressed: null,
              child: const Text('Cancelar'));
        } else {
          return ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: CooperadoColors.grayLight2,
            ),
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          );
        }
      },
    );
  }

  Widget _buttonConfirmar() {
    return BlocBuilder<ConfigProfileCubit, ConfigProfileState>(
      builder: (context, state) {
        if (state is UpdatingProfileState || !isPhoneValid) {
          return ElevatedButton(
              style: ElevatedButton.styleFrom(
                  backgroundColor: CooperadoColors.tealGreenSecondary),
              onPressed: null,
              child: const Text('Confirmar'));
        } else {
          return ElevatedButton(
              style: ElevatedButton.styleFrom(
                  backgroundColor: CooperadoColors.tealGreenSecondary),
              onPressed: _updatePhone,
              child: const Text('Confirmar'));
        }
      },
    );
  }

  _updatePhone() {
    final newContato = Contatos.fromJson(widget.contato!.toJson());
    final crm = context.read<AuthCubit>().credentials.crm;
    newContato.numeroContato = controller!.text;
    context.read<ConfigProfileCubit>().updateContato(
        crm: crm, endereco: widget.address, newContato: newContato);
  }

  Widget _indicatorStateUpadeContact() {
    return BlocConsumer<ConfigProfileCubit, ConfigProfileState>(
      listener: (context, state) {
        if (state is UpdatedProfileState) {
          Alert.open(context,
              barrierDismissible: false,
              title: 'Sucesso',
              text: 'Número atualizado com sucesso', callbackClose: () {
            Navigator.pop(context);
          });
        }
      },
      builder: (context, state) {
        if (state is UpdatingProfileState) {
          return const SpinKitThreeBounce(
              color: CooperadoColors.tealGreen, size: 30);
        } else if (state is ErrorUpdateProfileState) {
          return Text(state.message,
              style: const TextStyle(color: Colors.red, fontSize: 12));
        } else {
          return Container();
        }
      },
    );
  }

  InputBorder _borderInput() {
    return const OutlineInputBorder(
      borderSide: BorderSide(color: CooperadoColors.grayLight),
    );
  }
}

class OpmeList {
  int? totalPaginas;
  int? pagina;
  Opme? opme;
  List<ListaOPME>? listaOPME;

  OpmeList({this.totalPaginas, this.pagina, this.opme, this.listaOPME});

  OpmeList.fromJson(Map<String, dynamic> json) {
    totalPaginas = json['totalPaginas'];
    pagina = json['pagina'];
    opme = json['opme'] != null ? Opme.fromJson(json['opme']) : null;
    if (json['listaOPME'] != null) {
      listaOPME = [];
      json['listaOPME'].forEach((v) {
        listaOPME!.add(ListaOPME.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['totalPaginas'] = totalPaginas;
    data['pagina'] = pagina;
    if (opme != null) {
      data['opme'] = opme!.toJson();
    }
    if (listaOPME != null) {
      data['listaOPME'] = listaOPME!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Opme {
  String? descricao;
  Opme({this.descricao});

  Opme.fromJson(Map<String, dynamic> json) {
    descricao = json["descricao"];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data["descricao"] = descricao;

    return data;
  }
}

class ListaOPME {
  int? codigo;
  String? descricao;
  String? validade;
  Fornecedor? fornecedor;
  dynamic
      valor; //foi colocado como dynamic pq as vezes ele vem int, e as vezes double

  ListaOPME(
      {this.codigo,
      this.descricao,
      this.validade,
      this.fornecedor,
      this.valor});

  ListaOPME.fromJson(Map<String, dynamic> json) {
    codigo = json['codigo'];
    descricao = json['descricao'];
    validade = json['validade'];
    fornecedor = json['fornecedor'] != null
        ? Fornecedor.fromJson(json['fornecedor'])
        : null;
    valor = json['valor'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codigo'] = codigo;
    data['descricao'] = descricao;
    data['validade'] = validade;
    if (fornecedor != null) {
      data['fornecedor'] = fornecedor!.toJson();
    }
    data['valor'] = valor;
    return data;
  }
}

class Fornecedor {
  String? nome;

  Fornecedor({this.nome});

  Fornecedor.fromJson(Map<String, dynamic> json) {
    nome = json['nome'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['nome'] = nome;
    return data;
  }
}

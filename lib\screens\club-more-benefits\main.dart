import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/club-more-benefits/club_more_benefits_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/club-more-benefits/club_more_benefits_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/apoio-cooperado/cooperative_support.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/widgets/pdf_view/pdf_view_platform.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_masked_text2/flutter_masked_text2.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/svg.dart';
import 'package:url_launcher/url_launcher.dart';

class ClubMoreBenefitsScreen extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;
  const ClubMoreBenefitsScreen(
      {super.key, required this.analytics, required this.observer});

  @override
  State<ClubMoreBenefitsScreen> createState() => _ClubMoreBenefitsScreenState();
}

class _ClubMoreBenefitsScreenState extends State<ClubMoreBenefitsScreen> {
  bool _isChecked = false;

  @override
  void initState() {
    super.initState();
    _checkRegistration();
  }

  void _checkRegistration() async {
    BlocProvider.of<ClubMoreBenefitsCubit>(context)
        .checkBenefitsClubUserRegistration();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Clube Mais Vantagens'),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: BlocConsumer<ClubMoreBenefitsCubit, ClubMoreBenefitsState>(
          listener: (context, state) {
            if (state is DoneRegisterUserClubeMaisVantagens) {
              _showTransactionModalSuccess(
                context: context,
              );
            }
          },
          builder: (context, state) {
            if (state is LoadingClubMoreBenefitsState) {
              return const SpinKitCircle(
                color: CooperadoColors.tealGreen,
              );
            } else if (state is ErrorClubMoreBenefitsState) {
              return RefreshIndicator(
                onRefresh: () async {
                  _checkRegistration();
                },
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 80.0),
                      child: Text(
                        state.message,
                        style: const TextStyle(
                            fontSize: 16, color: Colors.black87),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
              );
            } else if (state is DoneCheckUserRegistrationState) {
              return SingleChildScrollView(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.5),
                        spreadRadius: 1,
                        blurRadius: 4,
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Text(
                        'A vida merece um desconto',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: CooperadoColors.tealGreen,
                        ),
                      ),
                      const SizedBox(height: 40),
                      const Text(
                        'O Clube de Vantagens da Unimed Fortaleza oferece aos nossos clientes e cooperados os melhores benefícios, apresentando uma plataforma repleta de ofertas exclusivas. Com descontos em estabelecimentos parceiros em todo o Brasil, nos mais diversos segmentos: gastronomia, educação, entretenimento, saúde, shopping e serviços e conveniência.',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.black87,
                        ),
                      ),
                      if (!(state.clubMaisVantagens.registeredUser ??
                          false)) ...[
                        const SizedBox(height: 20),
                        RichText(
                          text: const TextSpan(
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.black87,
                            ),
                            children: [
                              TextSpan(text: 'Ao'),
                              TextSpan(
                                text:
                                    ' clicar no botão ‘Aproveite suas vantagens’',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: CooperadoColors.tealGreen,
                                ),
                              ),
                              TextSpan(
                                text:
                                    ', você solicitará seu acesso à plataforma de descontos Clube Mais Vantagens. Após realizar o cadastro, será disponibilizado abaixo o link de acesso.',
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],
                      if (state.clubMaisVantagens.registeredUser ?? false)
                        Column(
                          children: [
                            const SizedBox(height: 20),
                            const Text(
                              'Seu benefício já está ativo',
                              style: TextStyle(
                                fontSize: 16,
                                color: CooperadoColors.tealGreen,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed: () {
                                  // Ação para acessar a plataforma
                                  if (state.clubMaisVantagens.redirectLink !=
                                      null) {
                                    _openLink(
                                        state.clubMaisVantagens.redirectLink!);
                                  }
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: CooperadoColors.tealGreen,
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                ),
                                child: const Text(
                                  'Acessar Plataforma',
                                  style: TextStyle(
                                      fontSize: 16, color: Colors.white),
                                ),
                              ),
                            ),
                          ],
                        )
                      else
                        Column(
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Checkbox(
                                  value: _isChecked,
                                  onChanged: (bool? newValue) {
                                    setState(() {
                                      _isChecked = newValue ?? false;
                                    });
                                  },
                                ),
                                Expanded(
                                  child: RichText(
                                    text: TextSpan(
                                      style: const TextStyle(
                                          fontSize: 14, color: Colors.black87),
                                      children: [
                                        const TextSpan(
                                            text: 'Declaro que li o '),
                                        TextSpan(
                                          text: 'regulamento',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: CooperadoColors.tealGreen,
                                            decoration:
                                                TextDecoration.underline,
                                          ),
                                          recognizer: TapGestureRecognizer()
                                            ..onTap = () {
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      PDFViewPlatform(
                                                    BlocProvider.of<AuthCubit>(
                                                            context)
                                                        .modelGeneralConfigModel
                                                        .links
                                                        ?.regulamentoClubeMaisVantagens,
                                                    title:
                                                        'Termo Clube Mais Vantagens',
                                                  ),
                                                ),
                                              );
                                            },
                                        ),
                                        const TextSpan(
                                          text:
                                              ', concordo com o mesmo e autorizo o compartilhamento dos meus dados (nome, e-mail e telefone) com a REDE PARCEIRA(S) para adesão ao Clube Mais Vantagens.',
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed: _isChecked
                                    ? () {
                                        _showContactModal(
                                          phone: state.clubMaisVantagens
                                                  .phoneFormatted ??
                                              '',
                                          email:
                                              state.clubMaisVantagens.email ??
                                                  '',
                                        );
                                      }
                                    : null,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: _isChecked
                                      ? CooperadoColors.tealGreen
                                      : Colors.grey,
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                ),
                                child: const Text(
                                  'Aproveite suas vantagens',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),
              );
            }
            return const Center();
          },
        ),
      ),
    );
  }

  void _showContactModal({required String phone, required String email}) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(15),
        ),
      ),
      builder: (BuildContext context) {
        var phoneController =
            MaskedTextController(mask: '(00) 0 0000-0000', text: phone);
        bool isButtonEnabled = phone.isNotEmpty && email.isNotEmpty;
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                      height: 4,
                      width: 100,
                      margin: const EdgeInsets.symmetric(
                        vertical: 10,
                      ),
                      decoration: BoxDecoration(
                        color: CooperadoColors.grayLight3,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: MediaQuery.of(context).size.width * 0.9,
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'Confirme os dados de contato',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        const Icon(Icons.email, color: Colors.black54),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            email.isNotEmpty ? email : 'E-mail não cadastrado',
                            style: const TextStyle(color: Colors.black87),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    const Divider(height: 24),
                    Row(
                      children: [
                        const Icon(Icons.phone, color: Colors.black54),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            phone.isNotEmpty
                                ? phoneController.text
                                : 'Telefone não cadastrado',
                            style: const TextStyle(color: Colors.black87),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    const Divider(height: 24),
                    RichText(
                      text: TextSpan(
                        style: const TextStyle(
                            fontSize: 14, color: Colors.black87),
                        children: [
                          const TextSpan(
                            text:
                                'Caso seus dados estejam desatualizados ou não cadastrados, favor entrar em contato com o  ',
                          ),
                          TextSpan(
                            text: '‘Apoio ao Cooperado’',
                            style: const TextStyle(
                              fontSize: 14,
                              color: CooperadoColors.tealGreen,
                              fontWeight: FontWeight.bold,
                              decoration: TextDecoration.underline,
                            ),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                Navigator.push(
                                  context,
                                  FadeRoute(
                                    page: SupportCooperativeScreen(
                                      analytics: widget.analytics,
                                      observer: widget.observer,
                                    ),
                                  ),
                                );
                              },
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: isButtonEnabled
                            ? () async {
                                context
                                    .read<ClubMoreBenefitsCubit>()
                                    .registerBenefitsClubUser();
                                Navigator.pop(context);
                              }
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: isButtonEnabled
                              ? CooperadoColors.tealGreen
                              : Colors.grey,
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          'Confirmar',
                          style: TextStyle(fontSize: 16, color: Colors.white),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showTransactionModalSuccess({
    required BuildContext context,
  }) {
    showModalBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(25.0)),
      ),
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.0),
                  child: Text(
                    'Parabéns!',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                ),
                SvgPicture.asset(
                  'assets/svg/icon_clubmais_sucess.svg',
                  height: 85,
                ),
                const SizedBox(height: 20),
                RichText(
                  textAlign: TextAlign.center,
                  text: const TextSpan(
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.black,
                      height: 1.5,
                    ),
                    children: [
                      TextSpan(
                        text: 'Cadastro confirmado com sucesso.',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      TextSpan(text: '\n\n'),
                      TextSpan(
                        text:
                            'Para acessar o Clube de vantagens basta clicar no botão ‘Acessar Plataforma‘',
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () async {
                    context
                        .read<ClubMoreBenefitsCubit>()
                        .checkBenefitsClubUserRegistration();
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: CooperadoColors.tealGreen,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    padding: const EdgeInsets.symmetric(
                        vertical: 15, horizontal: 30),
                  ),
                  child: const Text(
                    'Confirmar',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _openLink(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      throw 'Could not launch $url';
    }
  }
}

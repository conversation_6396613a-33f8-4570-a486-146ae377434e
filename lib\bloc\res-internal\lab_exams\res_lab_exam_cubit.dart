import 'package:cooperado_minha_unimed/models/res-internal/lab_exam.model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res-internal.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'res_lab_exam_cubit_state.dart';

class ResLabExamCubit extends Cubit<ResLabExamState> {
  ResLabExamCubit() : super(InitialResLabExamState());

  List<ResLabExamModel> _listLabExams = List.empty(growable: true);
  List<ResLabExamModel> get listLabExams => _listLabExams;

  void listResLabExams({required String card}) async {
    try {
      emit(LoadingResLabExamState());

      _listLabExams = await Locator.instance!<GraphQlApiInternal>()
          .resInternalLabExamsByCard(card: card);

      emit(LoadedResLabExamState(listLabExams: _listLabExams));
    } catch (e) {
      emit(ErrorResLabExamState(message: e.toString()));
    }
  }

  void searchListLabExams({required String searchKey}) async {
    try {
      emit(LoadingResLabExamState());

      final filtredList = _listLabExams
          .where((element) => element
              .toJson()
              .toString()
              .toLowerCase()
              .contains(searchKey.toLowerCase()))
          .toList();

      emit(LoadedResLabExamState(listLabExams: filtredList));
    } catch (e) {
      emit(ErrorResLabExamState(message: e.toString()));
    }
  }
}

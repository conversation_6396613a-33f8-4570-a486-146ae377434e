import 'package:cooperado_minha_unimed/models/res/res_attendance_model.dart';
import 'package:cooperado_minha_unimed/models/res/res_brasil_diagnostico_model.dart';
import 'package:equatable/equatable.dart';

abstract class ResBrazilDiagnosticoResultCubitState extends Equatable {
  const ResBrazilDiagnosticoResultCubitState();

  @override
  List<Object> get props => [];
}

class InitialResBrazilDiagnosticoState
    extends ResBrazilDiagnosticoResultCubitState {}

class LoadingResBrazilDiagnosticoResultState
    extends ResBrazilDiagnosticoResultCubitState {
  @override
  List<Object> get props => [];
}

class ErroResBrazilDiagnosticoResultState
    extends ResBrazilDiagnosticoResultCubitState {
  final String message;

  const ErroResBrazilDiagnosticoResultState({required this.message});
}

class NoDataResBrazilDiagnosticoResultState
    extends ResBrazilDiagnosticoResultCubitState {
  const NoDataResBrazilDiagnosticoResultState();
}

class LoadedResBrazilDiagnosticoResultState
    extends ResBrazilDiagnosticoResultCubitState {
  final List<ResBrazilDiagnosticoModel> listDiagnostico;

  @override
  List<Object> get props => [listDiagnostico];

  const LoadedResBrazilDiagnosticoResultState({required this.listDiagnostico});
}

class LoadedResBrazilListDiagnosticoResultState
    extends ResBrazilDiagnosticoResultCubitState {
  final List<ResAttendanceModel> listAllDiagnostico;

  @override
  List<Object> get props => [listAllDiagnostico];

  const LoadedResBrazilListDiagnosticoResultState(
      {required this.listAllDiagnostico});
}

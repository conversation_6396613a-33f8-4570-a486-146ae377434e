import 'package:intl/intl.dart';

class ResAllertModel {
  final String alert;
  final String origin;
  final String date;
  final String type;
  final String typeOrigin;

  ResAllertModel({
    required this.alert,
    required this.origin,
    required this.date,
    required this.type,
    required this.typeOrigin,
  });

  String get dateFormatted {
    final DateTime dateTime = DateTime.parse(date).toLocal();
    return DateFormat('dd/MM/yyyy').format(dateTime);
  }

  ResAllertModel.fromJson(Map<String, dynamic> json)
      : alert = json['alert'] as String,
        origin = json['origin'] as String,
        date = json['date'] as String,
        type = json['type'] as String,
        typeOrigin = json['typeOrigin'] as String;

  Map<String, dynamic> toJson() => {
        'alert': alert,
        'origin': origin,
        'date': date,
        'type': type,
        'typeOrigin': typeOrigin,
      };
}

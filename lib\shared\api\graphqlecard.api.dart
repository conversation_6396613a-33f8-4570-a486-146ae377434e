import 'package:cooperado_minha_unimed/models/e-card/ecard.model.dart';
import 'package:cooperado_minha_unimed/models/e-card/finish.model.dart';
import 'package:cooperado_minha_unimed/models/ecard-models/history_transaction_model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/messages.exceptions.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:graphql/client.dart';
import 'package:http_client/http_client.dart';

class GraphQlApiEcard {
  final UnimedHttpClient httpClient;

  GraphQlApiEcard(this.httpClient);

  final logger = UnimedLogger(className: 'GraphQlApiEcard');

  Future<GraphQLClient> getGithubGraphQLClient() async {
    final tokenPerfilApps = await Locator.instance!.get<AuthApi>().tokenPerfilApps();

    final Link link = HttpLink(
      FlavorConfig.instance!.values.graphql.url,
      defaultHeaders: {
        'Authorization': 'Bearer $tokenPerfilApps',
      },
    );

    return GraphQLClient(
      cache: GraphQLCache(),
      link: link,
      queryRequestTimeout: const Duration(seconds: 120),
    );
  }

  Future<String> activatedAndReactivatedEcard({required String codPrestador, required int periodInHours, bool isReactivated = false}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query;
      query = '''
        query ECardActiveCard {
            ${isReactivated ? 'eCardReactivateCard' : 'eCardActiveCard'}(codPrestador: "$codPrestador", periodInHours: $periodInHours) {
         message
        }
      }
    ''';

      logger.e('ecardActivation query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('ecardActivation exception : ${result.exception}');
        throw UnimedException(result.exception?.graphqlErrors.first.message ?? MessageException.general);
      } else {
        final data = result.data![isReactivated ? 'eCardReactivateCard' : 'eCardActiveCard']['message'];

        logger.e('eCardGetPendingCard data : $data');

        return data;
      }
    } on UnimedException catch (ex) {
      logger.e('ecardActivation ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('ecardActivation exception : $ex');
      throw GraphQlException(MessageException.general);
    }
  }

  Future historyEcardActivation({required String codPrestador, required DateTime startDate, required DateTime endDate}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();
      final String dateStart = startDate.toIso8601String();
      final String dateEnd = endDate.toIso8601String();

      String query;
      query = '''
      query ECardGetActivationHistory {
    eCardGetActivationHistory(
        codPrestador: "$codPrestador"
        startDate: "$dateStart"
        endDate: "$dateEnd"
    ) {
        codConsultEcad
        codPrestador
        fullName
        addressCode
        validityDate
        startDate
        exclusionDate
        insertionDate
        insertionUser
        viewAddress {
            addressCode
            streetTypeCode
            streetCode
            streetTypeName
            streetName
            addressNumber
            addressComplement
            neighborhoodCode
            neighborhoodName
            postalCode
            cityCode
            cityName
            stateCode
            stateName
        }
    }
}

    ''';

      logger.e('ecardActivation query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('historyEcardActivation exception : ${result.exception}');
       final errorMessage = result.exception?.graphqlErrors.first.message ?? MessageException.general;
      if (errorMessage.contains("A data de término (endDate) não pode ser anterior à data de início (startDate)")) {
          throw GraphQlException("A data final não pode ser anterior à data inicial. Por favor, verifique o período selecionado.");
        }
        throw GraphQlException(result.exception?.graphqlErrors.first.message ?? MessageException.general);
      } else {
        final data = result.data!['eCardGetActivationHistory'] as List;
        List<HistoryTransactionEcardModel> listHistoryActivarion = data.map((element) => HistoryTransactionEcardModel.fromJson(element)).toList();

        return listHistoryActivarion;
      }
    } on UnimedException catch (ex) {
      logger.e('ecardActivation ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('ecardActivation exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future ecardFinish({required String codPrestador}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query;
      query = '''
       query ECardGetActiveCard {
          eCardDisableCard(codPrestador: "$codPrestador") {
              message
          }
      }
    ''';

      logger.e('ecardFinish query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('ecardFinish exception : ${result.exception}');
        throw UnimedException(
          result.exception?.graphqlErrors.first.message ?? MessageException.general,
        );
      } else {
        final data = result.data!['eCardDisableCard'];

        final EcardFinishCardModel ecardFinishCardModel = EcardFinishCardModel.fromJson(data);

        return ecardFinishCardModel;
      }
    } on UnimedException catch (ex) {
      logger.e('ecardFinish ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('ecardFinish exception : $ex');
      throw GraphQlException(MessageException.general);
    }
  }

  Future<ECardModel> ecardActivationCheck({required String codPrestador}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query;
      query = '''
      query ECardGetActiveSessionDetail {
          eCardGetActiveCard(codPrestador: "$codPrestador") {
              providerCard
              activationStartDate
              validityDate
              status
              viewAddress {
                  streetTypeCode
                  streetName
                  addressNumber
              }
          }
      }
    ''';

      logger.e('ecardActivationCheck query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('ecardActivationCheck exception : ${result.exception}');
        throw UnimedException(result.exception?.graphqlErrors.first.message ?? MessageException.general);
      } else {
        final data = result.data!['eCardGetActiveCard'];

        final ECardModel eCardModel = ECardModel.fromJson(data);

        return eCardModel;
      }
    } on UnimedException catch (ex) {
      logger.e('ecardActivationCheck ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('ecardActivationCheck exception : $ex');
      throw GraphQlException(MessageException.general);
    }
  }

  Future<ECardModel> ecardPendingActivationCheck({required String codPrestador}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query;
      query = '''
      query ECardGetActiveSessionDetail {
          eCardGetPendingCard(codPrestador: "$codPrestador") {
              providerCard
              activationStartDate
              validityDate
              status
              viewAddress {
                  streetTypeCode
                  streetName
                  addressNumber
              }
          }
      }
    ''';

      logger.e('ecardPendingActivationCheck query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('ecardPendingActivationCheck exception : ${result.exception}');
        throw UnimedException(result.exception?.graphqlErrors.first.message ?? MessageException.general);
      } else {
        final data = result.data!['eCardGetPendingCard'];

        final ECardModel eCardModel = ECardModel.fromJson(data);

        return eCardModel;
      }
    } on UnimedException catch (ex) {
      logger.e('ecardPendingActivationCheck ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('ecardPendingActivationCheck exception : $ex');
      throw GraphQlException('Nenhum e-card vinculado ao cooperado');
    }
  }
}

part of 'res_procedures_cubit.dart';

abstract class ResProceduresState extends Equatable {
  const ResProceduresState();

  @override
  List<Object> get props => [];
}

class InitialResProceduresState extends ResProceduresState {}

class LoadingResProceduresState extends ResProceduresState {
  @override
  List<Object> get props => [];
}

class ErrorResProceduresState extends ResProceduresState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResProceduresState({required this.message});
}

class LoadedResProceduresState extends ResProceduresState {
  final List<ResAttendanceModel> listResProcedures;

  @override
  List<Object> get props => [listResProcedures];

  const LoadedResProceduresState({required this.listResProcedures});
}

class LoadingResAllergiesSearchState extends ResProceduresState {
  @override
  List<Object> get props => [];
}

class NoDataResProceduresState extends ResProceduresState {
  @override
  List<Object> get props => [];
}

import 'package:cooperado_minha_unimed/models/res/res_configs_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ResConfigModel', () {
    test('fromJson - sucesso', () {
      final json = {
        'allergies': {
          'categories': [
            {'code': 'DA', 'description': 'Alergia a Medicamentos'},
            {'code': 'FA', 'description': 'Alergia Alimentar'},
          ],
        },
      };

      final ResConfigModel model = ResConfigModel.fromJson(json);

      expect(model.allergies.categories!.length, 2);
      expect(model.allergies.categories![0].code, 'DA');
      expect(
          model.allergies.categories![0].description, 'Alergia a Medicamentos');
      expect(model.allergies.categories![1].code, 'FA');
      expect(model.allergies.categories![1].description, 'Alergia Alimentar');
    });

    test('fromJson - erro', () {
      final Map<String, dynamic> json = {};

      expect(() => ResConfigModel.fromJson(json), throwsA(isA<TypeError>()));
    });

    test('toJson - sucesso', () {
      final model = ResConfigModel(
        allergies: Allergies(
          categories: [
            Categories(code: 'DA', description: 'Alergia a Medicamentos'),
            Categories(code: 'FA', description: 'Alergia Alimentar'),
          ],
        ),
        indicators: [],
      );

      final Map<String, dynamic> json = model.toJson();

      expect(json['allergies']['categories'].length, 2);
      expect(json['allergies']['categories'][0]['code'], 'DA');
      expect(json['allergies']['categories'][0]['description'],
          'Alergia a Medicamentos');
      expect(json['allergies']['categories'][1]['code'], 'FA');
      expect(json['allergies']['categories'][1]['description'],
          'Alergia Alimentar');
    });
  });

  group('Allergies', () {
    test('fromJson - sucesso', () {
      final json = {
        'categories': [
          {'code': 'DA', 'description': 'Alergia a Medicamentos'},
          {'code': 'FA', 'description': 'Alergia Alimentar'},
        ],
      };

      final Allergies allergies = Allergies.fromJson(json);

      expect(allergies.categories!.length, 2);
      expect(allergies.categories![0].code, 'DA');
      expect(allergies.categories![0].description, 'Alergia a Medicamentos');
      expect(allergies.categories![1].code, 'FA');
      expect(allergies.categories![1].description, 'Alergia Alimentar');
    });

    test('fromJson - erro', () {
      final Map<String, dynamic> json = {};

      final Allergies allergies = Allergies.fromJson(json);

      expect(allergies.categories, isNull);
    });

    test('toJson - sucesso', () {
      final allergies = Allergies(
        categories: [
          Categories(code: 'DA', description: 'Alergia a Medicamentos'),
          Categories(code: 'FA', description: 'Alergia Alimentar'),
        ],
      );

      final Map<String, dynamic> json = allergies.toJson();

      expect(json['categories'].length, 2);
      expect(json['categories'][0]['code'], 'DA');
      expect(json['categories'][0]['description'], 'Alergia a Medicamentos');
      expect(json['categories'][1]['code'], 'FA');
      expect(json['categories'][1]['description'], 'Alergia Alimentar');
    });
  });

  group('Categories', () {
    test('fromJson - sucesso', () {
      final Map<String, dynamic> json = {
        'code': 'DA',
        'description': 'Alergia a Medicamentos',
      };

      final Categories category = Categories.fromJson(json);

      expect(category.code, 'DA');
      expect(category.description, 'Alergia a Medicamentos');
    });

    test('toJson - sucesso', () {
      final Categories category = Categories(
        code: 'DA',
        description: 'Alergia a Medicamentos',
      );

      final Map<String, dynamic> json = category.toJson();

      expect(json['code'], 'DA');
      expect(json['description'], 'Alergia a Medicamentos');
    });
  });

  group('ResIndicatorModel', () {
    test('fromJson - sucesso', () {
      final json = {
        'id': 1,
        'description': 'Indicator 1',
        'selected': 1,
      };

      final indicator = ResIndicatorModel.fromJson(json);

      expect(indicator.id, 1);
      expect(indicator.description, 'Indicator 1');
      expect(indicator.selected, 1);
    });

    test('toJson - sucesso', () {
      final indicator =
          ResIndicatorModel(id: 1, description: 'Indicator 1', selected: 1);

      final json = indicator.toJson();

      expect(json['id'], 1);
      expect(json['description'], 'Indicator 1');
      expect(json['selected'], 1);
    });
  });
}

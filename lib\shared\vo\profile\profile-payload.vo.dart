class ContactType {
  static const String email = 'EMAIL';
  static const String telefone = 'TELEFONE';
  static const String celular = 'CELULAR';
}

class PayloadAddress {
  int? codPrestador;
  String? nome;
  int? crm;
  int? codUnimed;
  int? cpf;
  List<Enderecos?>? enderecos;
  List<Especialidade>? especialidades;

  PayloadAddress(
      {this.codPrestador,
      this.nome,
      this.crm,
      this.codUnimed,
      this.cpf,
      this.enderecos,
      this.especialidades});

  Enderecos? get enderecoCorrespondencia {
    return enderecos!.firstWhere((element) => element!.correspondencia == 'S');
  }

  Enderecos? get enderecoConsultorio {
    return enderecos!.firstWhere((element) => element!.tipoEndereco == 'C');
  }

  PayloadAddress.fromJson(Map<String, dynamic> json) {
    codPrestador = json['codPrestador'];
    nome = json['nome'];
    crm = json['crm'];
    codUnimed = json['codUnimed'];
    cpf = json['cpf'];
    if (json['enderecos'] != null) {
      enderecos = [];
      json['enderecos'].forEach((v) {
        enderecos!.add(Enderecos.fromJson(v));
      });
    }
    if (json['especialidades'] != null) {
      especialidades = [];
      json['especialidades'].forEach((v) {
        especialidades!.add(Especialidade.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codPrestador'] = codPrestador;
    data['nome'] = nome;
    data['crm'] = crm;
    data['codUnimed'] = codUnimed;
    data['cpf'] = cpf;
    if (enderecos != null) {
      data['enderecos'] = enderecos!.map((v) => v!.toJson()).toList();
    }
    if (especialidades != null) {
      data['especialidades'] = especialidades!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Enderecos {
  int? codPrestador;
  int? codEndereco;
  int? codLogradouro;
  String? codBairro;
  String? cep;
  int? codCidade;
  String? codUf;
  String? tipoEndereco;
  String? correspondencia;
  List<Contatos?>? contatos;
  String? nomeLogradouro;
  String? numEndereco;
  String? complEndereco;
  String? nomeBairro;
  String? nomeCidade;

  List<Contatos?> get contatosEmail {
    return contatos!
        .where((element) => element!.tipoContato == 'EMAIL')
        .toList();
  }

  List<Contatos?> get contatosPhone {
    return contatos!
        .where(
          (element) =>
              element!.tipoContato == 'TELEFONE' ||
              element.tipoContato == 'CELULAR',
        )
        .toList();
  }

  List<String?> get getPhones => contatos!
      .where((element) =>
          element!.tipoContato == 'TELEFONE' ||
          element.tipoContato == 'CELULAR')
      .map((e) => e!.numeroContato)
      .toList();

  List<String?> get getEmails => contatos!
      .where((element) => element!.tipoContato == 'EMAIL')
      .map((e) => e!.numeroContato)
      .toList();

  Enderecos(
      {this.codPrestador,
      this.codEndereco,
      this.codLogradouro,
      this.codBairro,
      this.cep,
      this.codCidade,
      this.codUf,
      this.tipoEndereco,
      this.correspondencia,
      this.contatos,
      this.nomeLogradouro,
      this.numEndereco,
      this.complEndereco,
      this.nomeBairro,
      this.nomeCidade});

  String get enderecoCompl =>
      '$nomeLogradouro, $numEndereco ${complEndereco ?? ""}, $nomeBairro, $nomeCidade-$codUf';

  // String get getPhones => this
  //     .contatos
  //     .where((element) => element.tipoContato == 'TELEFONE')
  //     .map((e) => e.numeroContato)
  //     .toList()
  //     .join(' / ');

  Enderecos.fromJson(Map<String, dynamic> json) {
    codPrestador = json['codPrestador'];
    codEndereco = json['codEndereco'];
    codLogradouro = json['codLogradouro'];
    codBairro = json['codBairro'];
    cep = json['cep'];
    codCidade = json['codCidade'];
    codUf = json['codUf'];
    tipoEndereco = json['tipoEndereco'];
    correspondencia = json['correspondencia'];
    if (json['contatos'] != null) {
      contatos = [];
      json['contatos'].forEach((v) {
        contatos!.add(Contatos.fromJson(v));
      });
    }
    nomeLogradouro = json['nomeLogradouro'];
    numEndereco = json['numEndereco'];
    complEndereco = json['complEndereco'];
    nomeBairro = json['nomeBairro'];
    nomeCidade = json['nomeCidade'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codPrestador'] = codPrestador;
    data['codEndereco'] = codEndereco;
    data['codLogradouro'] = codLogradouro;
    data['codBairro'] = codBairro;
    data['cep'] = cep;
    data['codCidade'] = codCidade;
    data['codUf'] = codUf;
    data['tipoEndereco'] = tipoEndereco;
    data['correspondencia'] = correspondencia;
    if (contatos != null) {
      data['contatos'] = contatos!.map((v) => v!.toJson()).toList();
    }
    data['nomeLogradouro'] = nomeLogradouro;
    data['numEndereco'] = numEndereco;
    data['complEndereco'] = complEndereco;
    data['nomeBairro'] = nomeBairro;
    data['nomeCidade'] = nomeCidade;
    return data;
  }
}

class Contatos {
  int? id;
  String? tipoContato;
  int? codTipoMeioContato;
  String? tipoEndereco;
  String? dataInsercaoContato;
  bool? removido;
  bool? emailPortal;
  String? numeroContato;

  Contatos(
      {this.id,
      this.tipoContato,
      this.codTipoMeioContato,
      this.tipoEndereco,
      this.dataInsercaoContato,
      this.removido,
      this.emailPortal,
      this.numeroContato});

  bool get isEmail => tipoContato == 'EMAIL' ? true : false;

  Contatos.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    tipoContato = json['tipoContato'];
    codTipoMeioContato = json['codTipoMeioContato'];
    tipoEndereco = json['tipoEndereco'];
    dataInsercaoContato = json['dataInsercaoContato'];
    removido = json['removido'];
    emailPortal = json['emailPortal'];
    numeroContato = json['numeroContato'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['tipoContato'] = tipoContato;
    data['codTipoMeioContato'] = codTipoMeioContato;
    data['tipoEndereco'] = tipoEndereco;
    data['dataInsercaoContato'] = dataInsercaoContato;
    data['removido'] = removido;
    data['emailPortal'] = emailPortal;
    data['numeroContato'] = numeroContato;
    return data;
  }
}

class Especialidade {
  int? codigo;
  String? descricao;
  String? especialidadePrincipal;

  Especialidade({this.codigo, this.descricao, this.especialidadePrincipal});

  Especialidade.fromJson(Map<String, dynamic> json) {
    codigo = json['codigo'];
    descricao = json['descricao'];
    especialidadePrincipal = json['especialidadePrincipal'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codigo'] = codigo;
    data['descricao'] = descricao;
    data['especialidadePrincipal'] = especialidadePrincipal;
    return data;
  }
}

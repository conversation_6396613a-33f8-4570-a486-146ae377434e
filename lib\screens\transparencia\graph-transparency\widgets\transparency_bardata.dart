// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:cooperado_minha_unimed/shared/widgets/graphics/individual_bar.dart';
import 'package:flutter/material.dart';

class TransparencyBarData {
  double mensal;
  double acumulado;
  double projetado;
  List<Color> colors;

  TransparencyBarData({
    required this.mensal,
    required this.acumulado,
    required this.projetado,
    required this.colors,
  });

  List<IndividualBar> barData = [];

  void initializeBarData() {
    barData = [
      IndividualBar(x: 0, y: mensal),
      IndividualBar(x: 1, y: acumulado),
      IndividualBar(x: 2, y: projetado),
    ];
  }
}

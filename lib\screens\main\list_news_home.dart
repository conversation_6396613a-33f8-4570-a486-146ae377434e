import 'package:cooperado_minha_unimed/bloc/news/news_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/main/card_home.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/demostrativo_resultado.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class ListNewsHome extends StatefulWidget {
  const ListNewsHome({super.key});

  @override
  ListNewsHomeState createState() => ListNewsHomeState();
}

class ListNewsHomeState extends State<ListNewsHome>
    with TickerProviderStateMixin {
  late AnimationController animationControllerPanel;
  late Animation<double> animationPanel;

  @override
  void initState() {
    _updateListNoticias();
    _setupAnimations();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return _loadBloc();
  }

  Widget _loadBloc() {
    return BlocBuilder<NewsCubit, NewsState>(
      builder: (context, state) {
        if (state is DoneGetListNewsState) {
          animationControllerPanel.forward();
          return Center(child: _listNoticias(state.list));
        } else if (state is LoadingGetListNewsState) {
          animationControllerPanel.reset();
          return const SpinKitCircle(
            color: CooperadoColors.tealGreen,
          );
        } else {
          return Container();
        }
      },
    );
  }

  Widget _listNoticias(List<Noticia> noticias) {
    double height = MediaQuery.of(context).size.height * 0.21;
    double width = MediaQuery.of(context).size.width * 0.545;
    return Padding(
      padding: const EdgeInsets.all(5),
      child: FadeTransition(
        opacity: animationPanel,
        child: SizedBox(
          height: height,
          width: MediaQuery.of(context).size.width,
          child: noticias.isEmpty
              ? const Center(
                  child: Text(
                  "Não há noticias no momento",
                ))
              : ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: noticias.length,
                  itemBuilder: (context, index) {
                    return SizedBox(
                      height: height,
                      width: width,
                      child: CardHome(
                        noticia: noticias.elementAt(index),
                        height: height,
                        width: width,
                      ),
                    );
                  }),
        ),
      ),
    );
  }

  _setupAnimations() {
    animationControllerPanel = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    animationPanel =
        CurvedAnimation(parent: animationControllerPanel, curve: Curves.easeIn);
  }

  _updateListNoticias() async {
    context.read<NewsCubit>().getListNewsEvent(
      categories: ["todos"],
      page: 1,
    );
  }
}

import 'package:cooperado_minha_unimed/bloc/forgot-password/forgot_password_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/forgot-password/redefine_password_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/password_rules.model.dart';
import 'package:cooperado_minha_unimed/models/redefine_password.model.dart';
import 'package:cooperado_minha_unimed/screens/login.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/utils/validators.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:cooperado_minha_unimed/shared/widgets/elevated_button_custom.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:cooperado_minha_unimed/shared/widgets/item_form.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:header_login/header_login.dart';
import 'package:password_validation/password_validation.dart';

const String clientId = String.fromEnvironment('CLIENT_ID');

class RedefinePasswordScreen extends StatefulWidget {
  final String? crm;
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const RedefinePasswordScreen({
    super.key,
    this.crm,
    required this.analytics,
    required this.observer,
  });
  @override
  RedefinePasswordScreenState createState() => RedefinePasswordScreenState();
}

class RedefinePasswordScreenState extends State<RedefinePasswordScreen> {
  final _formKey = GlobalKey<FormState>();

  final _passwordValidationKey = GlobalKey<PasswordValidationComponentState>();
  bool _hidePassword = true;

  final TextEditingController _verificationCodeController =
      TextEditingController();
  final TextEditingController _newPassController = TextEditingController();
  final TextEditingController _confirmNewPassController =
      TextEditingController();

  final FocusNode _verificationCodeFocus = FocusNode();
  final FocusNode _newPassFocus = FocusNode();
  final FocusNode _confirmNewPassFocus = FocusNode();

  @override
  void initState() {
    super.initState();
    context.read<ForgotPasswordCubit>().getPasswordRules(isAnonymous: true);
    widget.analytics.logScreenView(
      screenName: 'Redefinir senha (Nova senha)',
      screenClass: 'RedefinePasswordScreen',
    );

    // Adiciona listeners para atualizar o estado do botão
    _newPassController.addListener(_updateButtonState);
    _confirmNewPassController.addListener(_updateButtonState);
  }

  @override
  void dispose() {
    _verificationCodeFocus.dispose();
    _newPassFocus.dispose();
    _confirmNewPassController.dispose();
    _newPassController.removeListener(_updateButtonState);
    _confirmNewPassController.removeListener(_updateButtonState);
    super.dispose();
  }

  void _updateButtonState() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CooperadoColors.grayLight,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0.0,
      ),

      // resizeToAvoidBottomInset: false,
      body: SafeArea(
        top: false,
        child: Column(
          children: [
            HeaderLogin(
              clientId: clientId,
              innerCircleColor: CooperadoColors.tealGreen,
              outerCircleColor: CooperadoColors.tealGreenSecondary,
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20.0).copyWith(top: 0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    BlocBuilder<ForgotPasswordCubit, ForgotPasswordState>(
                      builder: (context, state) {
                        if (state is LoadingGetPasswordRulesState) {
                          return const SpinKitThreeBounce(
                            color: CooperadoColors.tealGreen,
                            size: 20,
                          );
                        } else if (state is ErrorRulesState) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Center(
                                child: ErrorBanner(
                                  message: state.message,
                                ),
                              ),
                            ],
                          );
                        } else if (state is DoneGetPasswordRulesState) {
                          return _form(
                            rules: state.rules,
                          );
                        }
                        return const Center();
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _form({List<PasswordRulesModel>? rules}) {
    return Form(
      key: _formKey,
      child: ListView(
        padding: EdgeInsets.zero,
        physics: const PageScrollPhysics(),
        shrinkWrap: true,
        children: [
          ItemForm(
            title: 'Código de redefinição',
            controller: _verificationCodeController,
            focusNode: _verificationCodeFocus,
            next: true,
            validator: (value) => TextFieldValidators.requiredField(value!),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
            child: PasswordValidationComponent(
              key: _passwordValidationKey,
              controllerTextFormFieldPassword: _newPassController,
              keyTextFormFieldPassword: const Key('tfSenha'),
              obscureTextFormFieldPassword: _hidePassword,
              textInputActionTextFormFieldPassword: TextInputAction.next,
              focusNodTextFormFieldPassword: _newPassFocus,
              autovalidateModeTextFormFieldPassword:
                  AutovalidateMode.onUserInteraction,
              maxLengthTextFormFieldPassword: 100,
              styleTextFormFieldPassword: const TextStyle(
                color: unimedGreen,
              ),
              inputDecorationTextFormFieldPassword: InputDecoration(
                counterText: '',
                focusedBorder: const OutlineInputBorder(
                  borderSide: BorderSide(color: CooperadoColors.tealGreen),
                ),
                border: const OutlineInputBorder(),
                contentPadding:
                    const EdgeInsets.fromLTRB(20.0, 15.0, 20.0, 15.0),
                hintText: 'Nova senha',
                suffixIcon: IconButton(
                  icon: _hidePassword
                      ? const Icon(
                          Icons.visibility_off,
                          color: CooperadoColors.grayDark,
                        )
                      : const Icon(
                          Icons.remove_red_eye,
                          color: unimedGreen,
                        ),
                  onPressed: () async {
                    setState(() {
                      _hidePassword = !_hidePassword;
                    });
                  },
                ),
              ),
              requirements: (rules ?? []).map((e) {
                return RulesPasswordRequirement(
                  regex: e.regex,
                  message: e.message,
                  required: e.required,
                );
              }).toList(),
            ),
          ),
          ItemForm(
            title: "Confirmar nova senha",
            isPassword: true,
            maxLength: 100,
            validator: (value) =>
                _validatorPassword(value: value!, rules: rules),
            controller: _confirmNewPassController,
            focusNode: _confirmNewPassFocus,
            next: true,
          ),
          _buttonConfirm(),
        ],
      ),
    );
  }

  Widget _buttonConfirm() {
    return BlocConsumer<RedefinePasswordCubit, RedefinePasswordState>(
      listener: (context, state) {
        if (state is ErrorRedefinePassworddState) {
          showDialog(
            context: context,
            builder: (context) => CooperadoAlertDialog(
              textWidget: Text(
                state.message,
                textAlign: TextAlign.center,
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          );
        } else if (state is SucessRedefinePasswordState) {
          showDialog(
            context: context,
            builder: (context) => CooperadoAlertDialog(
              textWidget: const Text(
                "Senha alterada com sucesso!",
                textAlign: TextAlign.center,
              ),
              textButton: 'Fazer login',
              onPressed: () async {
                await Navigator.pushAndRemoveUntil(
                  context,
                  FadeRoute(
                    page: LoginScreen(
                      openBiometryOnInit: false,
                      analytics: widget.analytics,
                      observer: widget.observer,
                    ),
                  ),
                  (Route<dynamic> route) => route.isFirst,
                );
              },
            ),
          );
        }
      },
      builder: (context, state) {
        if (state is LoadingRedefinePassworddState) {
          return const SpinKitThreeBounce(
            color: CooperadoColors.tealGreen,
            size: 20,
          );
        }

        bool isSenhasPreenchidas = _newPassController.text.isNotEmpty &&
            _confirmNewPassController.text.isNotEmpty;

        return ElevatedButtonCustom(
          title: 'Salvar nova senha',
          onPressed: (isSenhasPreenchidas) ? () => _submit() : null,
        );
      },
    );
  }

  void _submit() {
    FocusScope.of(context).unfocus();
    if (_formKey.currentState!.validate()) {
      context.read<RedefinePasswordCubit>().redefinePassword(
            redefinePasswordModel: RedefinePasswordModel(
              crm: widget.crm,
              token: _verificationCodeController.text,
              senha: _newPassController.text.trim(),
              confirmarSenha: _confirmNewPassController.text.trim(),
            ),
          );
    }
  }

  String? _validatorPassword({
    required String value,
    List<PasswordRulesModel>? rules,
  }) {
    // Verifica se o campo está vazio
    if (value.isEmpty) {
      return 'Campo obrigatório';
    }

    // Valida as regras fornecidas
    if (rules != null) {
      for (final element in rules) {
        if (element.required) {
          final regex = RegExp(element.regex.replaceAll('//', '/'));
          if (!regex.hasMatch(value)) {
            return element.message; // Retorna a mensagem de erro da regra
          }
        }
      }
    }

    // Verifica se as senhas coincidem
    if (_newPassController.text != _confirmNewPassController.text) {
      return 'As senhas não coincidem';
    }

    return null; // Retorna null se não houver erros
  }
}

part of 'res_image_exam_result_cubit.dart';

abstract class ResImageExamResultState extends Equatable {
  const ResImageExamResultState();

  @override
  List<Object> get props => [];
}

class InitialResImageExamResultState extends ResImageExamResultState {}

class LoadingResImageExamResultState extends ResImageExamResultState {
  @override
  List<Object> get props => [];
}

class ErrorResImageExamResultState extends ResImageExamResultState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResImageExamResultState({required this.message});
}

class LoadedResImageExamResultState extends ResImageExamResultState {
  final List<ResImageExamResultModel> listImageExamResults;

  @override
  List<Object> get props => [listImageExamResults];

  const LoadedResImageExamResultState({required this.listImageExamResults});
}

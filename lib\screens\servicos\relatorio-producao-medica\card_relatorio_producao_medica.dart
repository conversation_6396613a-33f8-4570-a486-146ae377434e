import 'package:cooperado_minha_unimed/bloc/servicos/relatorio-producao/report_production_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/utils/file.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:cooperado_minha_unimed/shared/widgets/graphics/choose_date.dart';
import 'package:cooperado_minha_unimed/shared/widgets/pdf_view/pdf_view_platform.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:intl/intl.dart';

class CardReportProduction extends StatefulWidget {
  const CardReportProduction({super.key});

  @override
  CardReportProductionState createState() => CardReportProductionState();
}

class CardReportProductionState extends State<CardReportProduction> {
  DateTime? selectedDateTime;

  @override
  void initState() {
    context.read<ReportProductionCubit>().resetState();
    selectedDateTime = context.read<ReportProductionCubit>().lastDate;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Container(
        padding: const EdgeInsets.all(10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const Text(
              "Relatório Produção Médica",
              style: TextStyle(
                  color: CooperadoColors.blackText,
                  fontSize: 16,
                  fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            indicatorErrorState(),
            const SizedBox(height: 5),
            Row(
              children: <Widget>[
                Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: ChooseDateWidget(
                      firstDate: DateTime.now()
                          .subtract(const Duration(days: 30 * 12)),
                      textBox:
                          Text(DateFormat("MM/yyyy").format(selectedDateTime!)),
                      date: selectedDateTime,
                      onPressed: (date) {
                        if (date != null) {
                          setState(() {
                            selectedDateTime = date;
                          });
                        }
                        context.read<ReportProductionCubit>().resetState();
                        context
                            .read<ReportProductionCubit>()
                            .setSelectedDateTime(selectedDateTime);
                      },
                    )),
                _buttonGeneratePDF(),
              ],
            ),
            const SizedBox(height: 5)
          ],
        ),
      ),
    );
  }

  Widget _buttonGeneratePDF() {
    bool activate = false;
    return BlocConsumer<ReportProductionCubit, ReportProductionState>(
      listener: (context, state) {
        if (state is DoneGetReportProductionState && activate) {
          activate = false;
          _viewPDFFromBase64(
              base64Report: state.base64Report, context: context);
        }
      },
      builder: (context, state) {
        if (state is LoadingGetReportProductionState) {
          return Expanded(
              child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: CooperadoColors.tealGreen,
            ),
            onPressed: () {},
            child: const SpinKitThreeBounce(
              color: Colors.white,
              size: 20,
            ),
          ));
        } else {
          return Expanded(
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                  backgroundColor: CooperadoColors.tealGreen),
              onPressed: () {
                activate = true;
                context
                    .read<ReportProductionCubit>()
                    .getBase64ReportProductionEvent(selectedDateTime);
              },
              child: const Text("GERAR PDF"),
            ),
          );
        }
      },
    );
  }

  Widget indicatorErrorState() {
    return BlocBuilder<ReportProductionCubit, ReportProductionState>(
      builder: (context, state) {
        if (state is ErrorGetReportProductionState) {
          return Center(child: ErrorBanner(message: state.message));
        } else {
          return Container();
        }
      },
    );
  }

  Future<void> _viewPDFFromBase64({
    required base64Report,
    required context,
  }) async {
    final pathFile = await FileUtils.createFileFromString(
        base64String: base64Report, extension: FileExtension.pdf);

    Navigator.push(
      context,
      FadeRoute(
        page: PDFViewPlatform(
          pathFile,
          share: true,
          isPath: true,
          filename: '',
          title: 'Produção médica',
        ),
      ),
    );
  }
}

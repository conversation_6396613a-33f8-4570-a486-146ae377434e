// criamos a classe apenas com os atributos utilizados para fins de gráficos no app
// json completo possui chaves identicas e não utilizadas pelo app

class CustosAssistenciaisVO {
  List<Custo>? custos;
  double total = 0;
  String? observacao;

  CustosAssistenciaisVO({this.custos, this.observacao});

  CustosAssistenciaisVO.fromJson(Map<String, dynamic> json) {
    if (json['custosAssistenciais'] != null) {
      custos = [];
      json['custosAssistenciais'].forEach((c) {
        Custo custo = Custo.fromJson(c);
        custos!.add(custo);
        total += custo.valor!;
        total = double.parse(total.toStringAsFixed(2));
      });
    }
    observacao = json['observacao'];
  }
}

class Custo {
  late int codigo;
  String? descricao;
  double? valor;
  int? mes;
  int? ano;

  Custo({required this.codigo, this.descricao, this.valor, this.mes, this.ano});

  Custo.fromJson(Map<String, dynamic> json) {
    codigo = json['codigo'];
    descricao = json['descricao'];
    valor = double.parse(json['valor'].toString());
    mes = json['mes'];
    ano = json['ano'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codigo'] = codigo;
    data['descricao'] = descricao;
    data['valor'] = valor;
    data['mes'] = mes;
    data['ano'] = ano;
    return data;
  }
}

import 'dart:async';

import 'package:cooperado_minha_unimed/bloc/indicators/attendance-comparative/attendance_comparative_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/attendace-comparative/line_chart_compartative.dart';
import 'package:cooperado_minha_unimed/shared/widgets/card_refresh.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:unimed_select/utils.dart';

class AttendanceComparativeScreen extends StatefulWidget {
  final DateTime lastDate;

  const AttendanceComparativeScreen({super.key, required this.lastDate});

  @override
  AttendanceComparativeScreenState createState() =>
      AttendanceComparativeScreenState();
}

class AttendanceComparativeScreenState
    extends State<AttendanceComparativeScreen> {
  double total = 0.0;
  late DateTime selectedDateTime;
  int? selectedRadio = 0;

  @override
  void initState() {
    _init();
    super.initState();
  }

  void _init() {
    selectedDateTime = widget.lastDate;

    context
        .read<AttendanceComparativeCubit>()
        .getAttendanceComparative(selectedDateTime);
  }

  @override
  Widget build(BuildContext context) {
    return CardRefresh(
      title: const Text("Histórico de quantidade de serviços",
          style: TextStyle(
            color: CooperadoColors.blackText,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          )),
      refresh: _iconRefresh(),
      child:
          BlocBuilder<AttendanceComparativeCubit, AttendanceComparativeState>(
        builder: (context, state) {
          if (state is LoadedAttendanceComparativeState) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                const SizedBox(height: 10),
                Column(children: _groupRadioButton()),
                const SizedBox(height: 5),
                LineChartComparative(
                  data: state.serviceHistoric,
                  animate: true,
                  dataToShow: selectedRadio,
                ),
                const SizedBox(height: 10)
              ],
            );
          } else if (state is ReLoadingAttendanceComparativeState) {
            return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Column(children: _groupRadioButton()),
                  SizedBox(height: 200, child: Container()),
                ]);
          } else if (state is LoadingAttendanceComparativeState) {
            return const SpinKitCircle(color: CooperadoColors.tealGreen);
          } else if (state is ErrorAttendanceComparativeState) {
            return ErrorBanner(message: state.message);
          } else {
            return Container();
          }
        },
      ),
    );
  }

  Widget _iconRefresh() {
    return BlocBuilder<AttendanceComparativeCubit, AttendanceComparativeState>(
      builder: (context, state) {
        if (state is ErrorAttendanceComparativeState) {
          return InkWell(
              child: const Icon(Icons.refresh), onTap: () => _init());
        } else {
          return Container();
        }
      },
    );
  }

  List<Widget> _groupRadioButton() {
    return List<Widget>.generate(radioTextsOptions.length, (int index) {
      return Column(
        children: <Widget>[
          Row(
            children: <Widget>[
              SizedBox(
                height: 30,
                child: Radio(
                  groupValue: selectedRadio,
                  value: index,
                  onChanged: (dynamic val) {
                    setState(() {
                      selectedRadio = val;
                      context.read<AttendanceComparativeCubit>().reloadChart();
                      Timer(const Duration(milliseconds: 250), () {
                        context
                            .read<AttendanceComparativeCubit>()
                            .repaintChart();
                      });
                    });
                  },
                  fillColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
                  if (states.contains(WidgetState.selected)) {
                    return UnimedColors.green; // Use your desired color
                  }
                  return Theme.of(context).disabledColor; // Default color
                }),

                ),
              ),
              Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 0),
                  child: Text(radioTextsOptions[index]!))
            ],
          )
        ],
      );
    });
  }

  final radioTextsOptions = {
    0: 'Mostrar Todos',
    1: 'Consultas Eletivas',
    2: 'Serviços Diversos',
    3: 'Honorários'
  };
}

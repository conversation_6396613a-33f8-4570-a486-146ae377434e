part of 'honorarios_quimioterapia_cubit.dart';

abstract class ReportHonorarioState extends Equatable {
  const ReportHonorarioState();
}

class HonorariosInitial extends ReportHonorarioState {
  @override
  List<Object> get props => [];
}

class LoadingGetReportHonorarioState extends ReportHonorarioState {
  @override
  List<Object> get props => [];
}

class VisibilityGetHonorariosState extends ReportHonorarioState {
   final String crmId;
    @override
  List<Object> get props => [crmId];

  const VisibilityGetHonorariosState({required this.crmId});
 
}

class DoneGetReportHonorarioState extends ReportH<PERSON>rarioState {
  final String? honorarioCode;
  final String url;
  @override
  List<Object?> get props => [honorarioCode];

  const DoneGetReportHonorarioState(this.honorarioCode, this.url);
}

class ErrorGetReportHonorarioState extends ReportHonorarioState {
  final String message;
  @override
  List<Object> get props => [message];
  const ErrorGetReportHonorarioState(this.message);
}

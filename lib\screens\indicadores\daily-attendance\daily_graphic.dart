import 'package:cooperado_minha_unimed/bloc/indicators/daily_attendance/daily_attendance_cubit.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/daily-attendance/daily_piegraph.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DailyGraphic extends StatelessWidget {
  final bool animate;
  const DailyGraphic({super.key, this.animate = true});
  @override
  Widget build(BuildContext context) {
    List<DailyAttendanceSerie> seriesDaily =
        BlocProvider.of<DailyAttendanceCubit>(context).listDaily;

    return PieGraph(
      seriesDaily: seriesDaily,
    );
  }
}

class DailyAttendanceSerie {
  final double? quantity;
  final String title;
  final int? type;
  final Color barColor;

  DailyAttendanceSerie(
      {this.quantity, required this.title, required this.barColor, this.type});
}

class AttendanceComparativeVO {
  String? dataInicio;
  String? dataFim;
  List<Atendimentos>? atendimentosPrestador;
  List<Atendimentos>? atendimentosEspecialidade;
  int services = 0, consults = 0, fees = 0, total = 0;

  AttendanceComparativeVO(
      {this.dataInicio,
      this.dataFim,
      this.atendimentosPrestador,
      this.atendimentosEspecialidade});

  AttendanceComparativeVO.fromJson(Map<String, dynamic> json) {
    dataInicio = json['dataInicio'];
    dataFim = json['dataFim'];
    if (json['atendimentosPrestador'] != null) {
      atendimentosPrestador = [];
      json['atendimentosPrestador'].forEach((v) {
        atendimentosPrestador!.add(Atendimentos.fromJson(v));
      });
    }
    if (json['atendimentosEspecialidade'] != null) {
      atendimentosEspecialidade = [];
      json['atendimentosEspecialidade'].forEach((v) {
        atendimentosEspecialidade!.add(Atendimentos.fromJson(v));
      });
    }

    if (atendimentosEspecialidade!.length > atendimentosPrestador!.length) {
      _ajustList();
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['dataInicio'] = dataInicio;
    data['dataFim'] = dataFim;
    if (atendimentosPrestador != null) {
      data['atendimentosPrestador'] =
          atendimentosPrestador!.map((v) => v.toJson()).toList();
    }
    if (atendimentosEspecialidade != null) {
      data['atendimentosEspecialidade'] =
          atendimentosEspecialidade!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  void _ajustList() {
    for (int i = 0; i < atendimentosEspecialidade!.length; i++) {
      if (atendimentosPrestador!.length < atendimentosEspecialidade!.length ||
          atendimentosPrestador![i].mes != atendimentosEspecialidade![i].mes) {
        atendimentosPrestador!.insert(
            i,
            Atendimentos(
                mes: atendimentosEspecialidade![i].mes, media: 0.0, total: 0));
      }
    }
  }
}

class Atendimentos {
  int? mes;
  int? ano;
  List<Procedimentos>? procedimentos;
  double? media;
  //double total;
  int services = 0, consults = 0, fees = 0, total = 0;
  Atendimentos(
      {this.mes,
      this.ano,
      this.procedimentos,
      this.media,
      required this.total});

  String get ano2Digitos {
    final stringAno = '$ano'.padLeft(4, '0');
    return stringAno.substring(2, 4).toString();
  }

  Atendimentos.fromJson(Map<String, dynamic> json) {
    mes = json['mes'];
    ano = json['ano'];
    if (json['procedimentos'] != null) {
      procedimentos = [];
      json['procedimentos'].forEach((v) {
        Procedimentos p = Procedimentos.fromJson(v);

        if (p.tipo!.descricao == 'SERVIÇOS DIVERSOS') {
          services += p.quantidade!;
        } else if (p.tipo!.descricao == 'HONORÁRIOS') {
          fees += p.quantidade!;
        } else {
          consults += p.quantidade!;
        }
        total += p.quantidade!;
        procedimentos!.add(p);
      });
    }
    media = json['media'];
    // total = json['total'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['mes'] = mes;
    data['ano'] = ano;
    if (procedimentos != null) {
      data['procedimentos'] = procedimentos!.map((v) => v.toJson()).toList();
    }
    data['media'] = media;
    //data['total'] = this.total;
    return data;
  }

  int? getDataToShow(int index) {
    switch (index) {
      case 0:
        return total;
      case 1:
        return consults;
      case 2:
        return services;
      case 3:
        return fees;
      default:
        return 0;
    }
  }
}

class Procedimentos {
  int? quantidade;
  Tipo? tipo;

  Procedimentos({this.quantidade, this.tipo});

  Procedimentos.fromJson(Map<String, dynamic> json) {
    quantidade = json['quantidade'];
    tipo = json['tipo'] != null ? Tipo.fromJson(json['tipo']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['quantidade'] = quantidade;
    if (tipo != null) {
      data['tipo'] = tipo!.toJson();
    }
    return data;
  }
}

class Tipo {
  String? descricao;

  Tipo({this.descricao});

  Tipo.fromJson(Map<String, dynamic> json) {
    descricao = json['descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['descricao'] = descricao;
    return data;
  }
}

class AttendanceValues {
  List<Atendimentos>? atendimentos;
  int services = 0, consults = 0, fees = 0, total = 0;
}

class GraphicValues {
  final String mes;
  final double valor;
  final int index;

  GraphicValues(this.mes, this.valor, this.index);
}

const messes = [
  "JAN",
  "FEV",
  "MAR",
  "ABR",
  "MAI",
  "JUN",
  "JUL",
  "AGO",
  "SET",
  "OUT",
  "NOV",
  "DEZ"
];

import 'package:auto_size_text/auto_size_text.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/activation-check/activation_check_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/activation-check/activation_check_state.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/timer-card/timer_card.dart';
import 'package:cooperado_minha_unimed/bloc/profile/profile_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/e-card/widgets/count_down_timer_widget.dart';
import 'package:cooperado_minha_unimed/screens/e-card/widgets/ecard_buttons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class CardWidget extends StatefulWidget {
  const CardWidget({
    super.key,
  });

  @override
  State<CardWidget> createState() => _CardWidgetState();
}

class _CardWidgetState extends State<CardWidget> {
  @override
  void initState() {
    context.read<EcardActivationCheckCubit>().ecardActivationCheck(
          codPrestador: context.read<ProfileCubit>().user.codPrestador.toString(),
        );
    context.read<TimerEcardCubit>().setStateTimerEcard(
          isTimerRunner: false,
        );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<EcardActivationCheckCubit, EcardActivationCheckState>(
      listener: (context, state) {
        if (state is ErrorEcardActivationCheckState) {
          context.read<EcardActivationCheckCubit>().ecardPendingActivationCheck(
                codPrestador: context.read<ProfileCubit>().user.codPrestador.toString(),
              );
        }
      },
      builder: (context, state) {
        return Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(9.0),
              child: Center(
                child: Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height * 0.27,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    boxShadow: const [
                      BoxShadow(
                        color: Colors.black26,
                        offset: Offset(0, 4),
                        blurRadius: 10,
                      ),
                    ],
                    gradient: const LinearGradient(
                      colors: [
                        Color.fromARGB(226, 10, 95, 85),
                        CooperadoColors.greenDark,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      stops: [0.5, 0.5],
                      transform: GradientRotation(0.2),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: state is LoadingEcardActivationCheckState
                        ? const Center(
                            child: SpinKitThreeBounce(
                              color: Colors.white,
                              size: 20,
                            ),
                          )
                        : state is LoadedEcardActivationCheckState
                            ? BlocBuilder<SensitiveDataCubit, SensitiveDataState>(builder: (context, sensitiveState) {
                                bool isSensitiveDataVisible = !sensitiveState.isSensitiveDataVisible;

                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: <Widget>[
                                    _buildRow(
                                      'Uni Card',
                                      isSensitiveDataVisible ? _obscureText(text: state.eCardModel.status) : state.eCardModel.status,
                                    ),
                                    const SizedBox(height: 30),
                                    _buildText(
                                      isSensitiveDataVisible
                                          ? _obscureText(
                                              text: context.read<ProfileCubit>().user.nome ?? 'Nome não cadastrado',
                                            )
                                          : context.read<ProfileCubit>().user.nome ?? 'Nome não cadastrado',
                                    ),
                                    _buildText(
                                      isSensitiveDataVisible
                                          ? _obscureText(
                                              text: state.eCardModel.providerCard,
                                            )
                                          : state.eCardModel.providerCard,
                                      fontSize: 22,
                                      fontWeight: FontWeight.bold,
                                      letterSpacing: 2.0,
                                    ),
                                    const Spacer(),
                                    _buildAddressRow(
                                      isSensitiveDataVisible,
                                      state,
                                    ),
                                  ],
                                );
                              })
                            : state is ErrorEcardPendingCheckState
                                ? Center(
                                    child: Text(
                                      state.message,
                                      style: const TextStyle(color: Colors.white),
                                    ),
                                  )
                                : Container(),
                  ),
                ),
              ),
            ),
            BlocBuilder<TimerEcardCubit, bool>(builder: (context, stateTimeEcard) {
              return Visibility(
                visible: state is LoadedEcardActivationCheckState,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 15.0),
                  child: EcardButtons(
                    isReactivated: stateTimeEcard == true
                        ? stateTimeEcard
                        : state is LoadedEcardActivationCheckState
                            ? state.eCardModel.getRemainingTime()
                            : false,
                    isActivated: state is LoadedEcardActivationCheckState ? state.eCardModel.isStatusActive() : false,
                    isButtonEnabled: stateTimeEcard == true
                        ? stateTimeEcard
                        : state is LoadedEcardActivationCheckState
                            ? (state.eCardModel.isStatusPending())
                                ? true
                                : false
                            : false,
                  ),
                ),
              );
            }),
          ],
        );
      },
    );
  }

  Row _buildRow(String title, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          value,
          style: const TextStyle(color: Colors.white),
        ),
      ],
    );
  }

  Widget _buildText(String text, {double fontSize = 15, FontWeight fontWeight = FontWeight.normal, double letterSpacing = 0.0}) {
    return AutoSizeText(
      text,
      style: TextStyle(
        color: Colors.white,
        fontWeight: fontWeight,
        letterSpacing: letterSpacing,
      ),
      maxFontSize: fontSize,
    );
  }

  Widget _buildAddressRow(bool isSensitiveDataVisible, LoadedEcardActivationCheckState state) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: AutoSizeText(
            isSensitiveDataVisible
                ? _obscureText(text: '${state.eCardModel.viewAddress.streetTypeCode}, ${state.eCardModel.viewAddress.streetName}, ${state.eCardModel.viewAddress.addressNumber}')
                : '${state.eCardModel.viewAddress.streetTypeCode}, ${state.eCardModel.viewAddress.streetName}, ${state.eCardModel.viewAddress.addressNumber}',
            style: const TextStyle(
              color: CooperadoColors.grayLight4,
              fontWeight: FontWeight.bold,
            ),
            minFontSize: 6,
            maxFontSize: 15,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Visibility(
          visible: !state.eCardModel.isStatusPending(),
          child: Padding(
            padding: const EdgeInsets.only(left: 12.0),
            child: CountdownTimerWidget(
              initialTime: state.eCardModel.activationStartDate.toString(),
              finalTime: state.eCardModel.validityDate.toString(),
            ),
          ),
        ),
      ],
    );
  }

  String _obscureText({required String text}) {
    return text.replaceAll(RegExp(r'[a-zA-Z0-9]'), '*');
  }
}

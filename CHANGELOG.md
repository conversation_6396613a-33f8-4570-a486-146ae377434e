
<a name="v3.1.3-310041"></a>
## [v3.1.3-310041](https://gitlab.unimedfortaleza.com.br:2222/novas-tecnologias/cooperados-flutter/compare/v3.1.1-310023...v3.1.3-310041)

> 2023-03-22

### Feat

* tweak in PDF sharing method
* update blocbuild to blocconsumer
* change logout method
* clean widgets
* add assync function
* stream forgot password improvements
* stream forgot password concluded
* change user and password profile apps
* update token
* initial stream forgot password
* stream forgot password
* stream forgot password
* agree terms model
* test type json
* addressModel test
* json test ZipCodeModel
* VersionRemoteModel errors json
* json unit test
* unit test version-remote
* unit test service-historic
* unit test zipcode.model.dart 100%
* user unit test 87%
*  unit test indicadores.model_test.dart
* Unit test 100%
* diretoriaModel test
* Address model unit test done
* type Address model test
* folders
* open page with pdfView & pdf filename
* package firebase crashlytics

### Fix

* remove package and change download
* duble click popup bug
* ajust logout report
* remove state duplicate
* duplicate popup home page
* device orientation
* auth returning null credentials
* duplicate popup home page
* duplicate popup and add alert no internet access
* news
* auth service
* Address tests and add some tests in zipCodeModel
* revert commit
* address model test and feat agree terms test
* spacement and textAlign
* ui without terms index
* alert
* ui
* Alert and done
* bugs agree dialog
* remove button throw exception test from login
* checkbox using cubit
* dynamic texts
* padding, text and punctuation corrections
* adicionado url do portal v2 nas flavor config

### Refactor

* remove permission unused GET_ALL_PACKAGES
* blocBuilder code

### Wip

* credencial solution


<a name="v3.1.1-310023"></a>
## [v3.1.1-310023](https://gitlab.unimedfortaleza.com.br:2222/novas-tecnologias/cooperados-flutter/compare/v3.0.1-300012...v3.1.1-310023)

> 2022-06-17

### Feat

* fix token prod
* Versão 300013

### Fix

* msg 500
* cast object
* money library
* iOS Stuffs
* arm64 ios config podfile
* remove null-safety branches
* alert cadastro - utf
* ajuste de context
* iOS Stuffs
* credentials ajuste
* remove warnings
* corrigido pie chart e donut chart
* add margin and change to legend vertical in behavior
* increase height datacell
*  header title flexibles in details

### Fix

* gradle same version another apps
* donut chart corrigido
* brightness and algumas function onesignal

### Refactor

* iOS Stuffs
* iOS Stuffs
* downgrade version 2.5.2
* iOS Configs
* update remote_log
* merge flutter-packages to master
* Update Changelog

### WIP

* atualizado todos os packages e message cubit
* onesignal service
* corrigido theme texttheme para novo formato
* update packages

### Wip

* comment pubspec.yaml
* ajuste de alerta - retorno
* ajustado models
* corrigido model e retorno de funcoes
* corrigido variaveis nulas em views e cubit

### Wip

* ajustado variaveis iniciadas nulas


<a name="v3.0.1-300012"></a>
## [v3.0.1-300012](https://gitlab.unimedfortaleza.com.br:2222/novas-tecnologias/cooperados-flutter/compare/v3.0.0-300008...v3.0.1-300012)

> 2021-07-18

### Feat

* Versão 300012
* Versão 300011
* update local_auth to lastest version
* Versão 300010
* Versão 300009

### Fix

* sdk android 24
* change android theme in manisfest
* save credentials after responde with crm
* logout method

### Refactor

* token prod test
* Adjusts urls and constants
* Clean unused packages
* Update Changelog


<a name="v3.0.0-300008"></a>
## [v3.0.0-300008](https://gitlab.unimedfortaleza.com.br:2222/novas-tecnologias/cooperados-flutter/compare/v2.1.7-200140...v3.0.0-300008)

> 2021-07-09

### Feat

* Versão 300008
* Versão 300008
* Versão 300007
* Versão 300006
* deviceid - meus dados para teste
* Versão 300005
* Versão 300004
* change launch to pdf viewer widget
* add param to login service
* Versão 300003
* using behaviors to show flexible legends in daily-graphic
* Versão 300002
* add CardRefresh in indicators
* add force token for production environment and add orientations in readme

### Feature

* flutter 2.2.2

### Fix

* using column scrollable in 'transparencia'
* avoiding  elastic list 'resultados' screen
* prevents effect elastic list
* add space to regex to format filename pdf
* Enabled Push Notification iOS
* ajustments in text for force update in readme
* adjustment loading in profile config
* align error widgets and loading in screen 'diretoria'
* navigator to first screen to app when error
* add alert with version in log when scroll
* get crm after login
* login with forced token
* Firebase Prod
* Flutter 2.2.2
* avoid biometric in background
* change endpoint for logout service
* traitments no data for historic production
* color adjustments
* UnimedException to capture exceptions in getExtractCopart
* adjustments label msg error
* layout bug fixed using hero animation
* update with develop and padding adjustment
* code adjustments
* bug beneficiary
* adjustment message error adress cep
* add param physics to ListView
* layout cards and cache date
* last date cached if not passing data report
* adjustments padding icon in error state
* adjust label in card  production
* unification card with refresh and paddings configurations
* prevent member concil url image empty
* adjustment color close button
* add image cached in concil  and directory screen
* adjust  minimumFetchInterval to 10 min
* code adjustments
* adjustment remote-config
* thumb using cached image network in 'conselho'
* add default value
* adjustment in readme
* create card standard
* standart Error using error banner in services screen
* error barner standard

### Fix

* one signal service config

### Fx

* espaçamentos

### Style

* add more infos in readme


<a name="v2.1.7-200140"></a>
## [v2.1.7-200140](https://gitlab.unimedfortaleza.com.br:2222/novas-tecnologias/cooperados-flutter/compare/flutter-1.22.5...v2.1.7-200140)

> 2021-06-08


<a name="flutter-1.22.5"></a>
## [flutter-1.22.5](https://gitlab.unimedfortaleza.com.br:2222/novas-tecnologias/cooperados-flutter/compare/v2.1.5-200134...flutter-1.22.5)

> 2021-06-08

### Feat

* add refresh indicator in profile screen
* show modal options
* add hero animation to card: 'servicos deste mes'
* manage orientation changes in PdfViewScreen
* add MaskedTextController
* adaptation for change several emails in profile

### Fix

* change label error
* prevent no data in quantity service
* add condition for Error state in each tab
* organizing functions in bloc and fix title date
* adjustments  messages exceptions in service configr profile api
* change height Sized Box
* label tipoServicos
* change stack to after DonePdf state in BlocBuilder
* layout  adjustments
* update view using last 'dataFim'
* clean title in banner error
* adjustments in  daily_attendance_cubit
* remove button 'Configurações' from home screen
* change icon
* Using component CustomCircleSymbolRenderer
* adjustments in  names for linecharts
* layout adjustments
* using lisview builder and ClampingScrollPhysics
* initialize variables in bloc event
* remove safearea from up scaffold
* safeArea over body configScreen
* remove safearea login screen
* code adjustments
* adjustment label text
* adsjustments layout services screen
* add safearea to scaffolds
* solving conflits after merging
* reordering cards in service screen
* align to center  banner error
* usgin component for header
* calling dataFormatted
* organizing formatters
* allow edit field without clean
* inserido restante dos logs em pdfview
* added log in card-report.dart
* remove unused comments
* adjustment in openBiometry
* adjustmnts layout
* request focus
* layout adjustments
* change label 'Fechar'


<a name="v2.1.5-200134"></a>
## [v2.1.5-200134](https://gitlab.unimedfortaleza.com.br:2222/novas-tecnologias/cooperados-flutter/compare/v2.1.3-200128...v2.1.5-200134)

> 2021-05-20

### Feat

* change layout piechart
* add custom tooltip in lineCharts
* using pichart for data beneficiary
* add refreshIndicator
* when click at unimeds logo on home screen show apps version

### Fix

* update 'bloc' call to 'read' call
* update bloc functions in config-screen
* change base endpoint in flavor for 'consultometro'
* add barrier dimissible false in dialog when error
* prevent click while generating file in bloc
* prevent multiples clicks in share button
* layout adjustments
* clean state when refresh or change date
* code adjustments
* adjustment error  in cards from home
* adjustment error in 'Comparativo de produçao'
* adjust size labels
* layout adjustments
* expansion panel was weird behavior when scrolled
* adjustments layout
* icon editable for fields editables
* adjustments imports
* adjustments in event and layout
* choose date component always show last month as available

### Refactor

* improve details layout on extracts
* report url was hard coded on view


<a name="v2.1.3-200128"></a>
## [v2.1.3-200128](https://gitlab.unimedfortaleza.com.br:2222/novas-tecnologias/cooperados-flutter/compare/v2.1.1-200120...v2.1.3-200128)

> 2021-05-03


<a name="v2.1.1-200120"></a>
## v2.1.1-200120

> 2021-04-15


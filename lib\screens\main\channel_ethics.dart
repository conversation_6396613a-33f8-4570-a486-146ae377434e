import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/chanel-ethics/channel_ethics_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/chanel-ethics/channel_ethics_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:url_launcher/url_launcher.dart';

class WebViewChannelEthics extends StatefulWidget {
  const WebViewChannelEthics(
      {super.key, required this.analytics, required this.observer});

  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  @override
  State<WebViewChannelEthics> createState() => _WebViewChannelEthicsState();
}

class _WebViewChannelEthicsState extends State<WebViewChannelEthics> {
  String url = '';
  final logger = UnimedLogger(className: 'ChannelEthicsApi');
  @override
  initState() {
    context.read<ChannelEthicsCubit>().getChannelEthics();
    BlocProvider.of<ChannelEthicsCubit>(context).getChannelEthics();
    super.initState();
    url = BlocProvider.of<AuthCubit>(context)
            .modelGeneralConfigModel
            .links
            ?.channelEthics ??
        '';

    widget.analytics.logScreenView(
      screenName: 'Canal de Etica',
      screenClass: 'ChannelEthics',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text(
          "Canal de Ética",
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
      ),
      body: BlocBuilder<ChannelEthicsCubit, ChannelEthicsState>(
        builder: (context, state) {
          if (state is ChannelEthicsStateDone) {
            return SingleChildScrollView(
              child: Html(
                data: state.channelResposive.retorno?.page?.content,
                onLinkTap: (url, attributes, element) {
                  if (url != null) _onTapLink(url);
                },
              ),
            );
          } else if (state is ChannelEthicsError) {
            return Center(
              child: ErrorBanner(message: state.message),
            );
          } else {
            return const SpinKitCircle(
              color: CooperadoColors.tealGreen,
            );
          }
        },
      ),
    );
  }

  _onTapLink(String url) async {
    await _launchURL(url);
  }

  Future _launchURL(String url) async {
    logger.i('launchURL - abrindo url $url');
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      throw 'Unable to open url : $url';
    }
  }
}

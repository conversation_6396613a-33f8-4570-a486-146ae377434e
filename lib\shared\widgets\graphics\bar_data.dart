// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:cooperado_minha_unimed/shared/widgets/graphics/individual_bar.dart';

class BarData {
  final double meAmount;
  final double prodAmount;

  BarData({
    required this.meAmount,
    required this.prodAmount,
  });

  List<IndividualBar> barData = [];

  void initializeBarData() {
    barData = [
      if (meAmount > 0) IndividualBar(x: 0, y: meAmount),
      IndividualBar(x: 1, y: prodAmount),
    ];
  }
}

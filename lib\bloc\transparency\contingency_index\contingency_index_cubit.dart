

import 'package:cooperado_minha_unimed/shared/api/transparencia.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/messages.exceptions.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/indices.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'contingency_index_state.dart';

class ContingencyIndexCubit extends Cubit<ContingencyIndexState> {
  ContingencyIndexCubit() : super(InitialContingencyIndexState());

  getContingencyIndex() async {
    try {
      emit(LoadingContingencyIndexState());

      final indices = await Locator.instance!<TransparenciaApi>()
          .getIndices('CONTInGENCIA');
        
      if (indices.data!
          .where((VODataModel element) => element.monthValue != 0)
          .toList()
          .isNotEmpty) {
        emit(LoadedContingencyIndexState(indices));
      } else {
        emit(const ErrorContingencyIndexState(MessageException.genericNoData));
      }
    } catch (e) {
      emit(ErrorContingencyIndexState(e.toString()));
    }
  }
}

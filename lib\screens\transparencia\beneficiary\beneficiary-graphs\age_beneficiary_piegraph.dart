import 'package:cooperado_minha_unimed/screens/transparencia/beneficiary/beneficiary-graphs/age_piegraph_widget.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/beneficiary/beneficiary-graphs/beneficiary_piegraph.dart';
import 'package:flutter/material.dart';

class AgeBeneficiaryPieGraph extends StatelessWidget {
  final List data;
  const AgeBeneficiaryPieGraph({super.key, required this.data});
  //List<BeneficiaryData> listBeneficiaryData =[];

  List<BeneficiaryData> getListSeries() {
    final List<BeneficiaryData> seriesList = data
        .map<BeneficiaryData>((e) =>
            BeneficiaryData(quantity: e.quantidade, description: e.descricao))
        .toList();

    return seriesList;
  }

  @override
  Widget build(BuildContext context) {
    final data = getListSeries();
    return AgePieGraphWidget(listBenefic: data);
  }
}

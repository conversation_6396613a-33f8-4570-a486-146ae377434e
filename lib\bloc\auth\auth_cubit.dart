import 'dart:io';

import 'package:collection/collection.dart';
import 'package:cooperado_minha_unimed/models/general_config_model.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/api/general_config.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/messages.exceptions.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';

part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  AuthCubit() : super(InitialAuthState());

  late UserCredentials _userCredentials;
  UserCredentials get credentials {
    return _userCredentials;
  }

  late User _userLogged;
  User get userLogged => _userLogged;

  late GeneralConfigModel _modelGeneralConfigModel;
  GeneralConfigModel get modelGeneralConfigModel => _modelGeneralConfigModel;

  Future<void> autenticate({required UserCredentials credentials}) async {
    try {
      emit(LoadingAuthState());

      String deviceId = '';
      final deviceInfo =
          await Locator.instance!.get<RemoteLog>().deviceInfo?.toJson();
      if (Platform.isAndroid) {
        deviceId = deviceInfo?['androidId'] ?? '';
      } else if (Platform.isIOS) {
        deviceId = deviceInfo?['identifierForVendor'] ?? '';
      }

      _userCredentials = credentials;

      _userLogged = await Locator.instance!<AuthApi>()
          .login(credentials: _userCredentials, deviceId: deviceId);

      final generalConfigModel =
          await Locator.instance!<GeneralConfigsApi>().getGeneralMessage();

      _modelGeneralConfigModel = generalConfigModel;

      emit(LoadedAuthState(
          user: _userLogged, generalConfigModel: generalConfigModel));
    } catch (e) {
      emit(ErrorAuthState(e.toString()));
    }
  }

  signout() async {
    emit(LoadingLogoutUserState());
    try {
      await Locator.instance!<AuthApi>().logout(_userCredentials);
      _userCredentials = UserCredentials(crm: '', password: '');
      emit(DoneLogoutUserState());
    } catch (ex) {
      emit(ErrorLogoutUserState('$ex'));
    }
  }

  void updatePassword(String newPassword) {
    _userCredentials.password = newPassword;
  }

  String getMessageConfig(String id) {
    return (_modelGeneralConfigModel.messages!
            .firstWhereOrNull((element) => element.id == id)
            ?.message) ??
        MessageException.general;
  }
}

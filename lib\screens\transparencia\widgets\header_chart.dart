import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';

class Transparency<PERSON>eaderChart extends StatelessWidget {
  final String? title;
  const TransparencyHeaderChart({super.key, this.title});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title!,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          color: CooperadoColors.blackText,
          fontSize: 16,
        ),
      ),
    );
  }
}

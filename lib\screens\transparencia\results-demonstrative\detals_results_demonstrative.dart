import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/utils/uicons.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/demostrativo_resultado.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../shared/widgets/pdf_view/pdf_view_platform.dart';

class ResultsDemonstrativeDetail extends StatefulWidget {
  final DemonstrativoResultadosVO? demonstativo;
  const ResultsDemonstrativeDetail({super.key, this.demonstativo});
  @override
  ResultsDemonstrativeDetailState createState() =>
      ResultsDemonstrativeDetailState();
}

class ResultsDemonstrativeDetailState
    extends State<ResultsDemonstrativeDetail> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Transparência"),
        backgroundColor: CooperadoColors.tealGreenDark,
      ),
      body: SafeArea(
        child: Container(
          padding: const EdgeInsets.all(10.0),
          child: ListView(
            physics: const ClampingScrollPhysics(
                parent: AlwaysScrollableScrollPhysics()),
            children: _listResults(),
          ),
        ),
      ),
    );
  }

  List<Widget> _listResults() {
    List<Widget> list = [];

    list.add(const Text(
      "Resultados",
      style: TextStyle(
          fontWeight: FontWeight.bold,
          color: CooperadoColors.tealGreen,
          fontSize: 18),
    ));
    list.add(const Padding(
      padding: EdgeInsets.all(8.0),
      child: Column(
        children: [
          Divider(height: 1, color: CooperadoColors.grayDark),
          Padding(
            padding: EdgeInsets.symmetric(vertical: 8.0),
            child: Text(
              "Documentos",
              style: TextStyle(color: CooperadoColors.tealGreen),
            ),
          ),
          Divider(height: 2, color: CooperadoColors.grayDark2),
        ],
      ),
    ));

    for (Noticia noticia in widget.demonstativo!.noticias!) {
      list.add(Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
          child: GestureDetector(
            onTap: () => Navigator.push(
              context,
              FadeRoute(
                page: PDFViewPlatform(
                  noticia.arquivo,
                  share: true,
                  filename: noticia.assunto ?? "",
                  title: noticia.assunto!.toUpperCase(),
                ),
              ),
            ),
            child: Column(
              children: <Widget>[
                Row(
                  children: <Widget>[
                    const Icon(
                      UIcons.iconPdf,
                      color: CooperadoColors.tealGreenSecondary,
                    ),
                    const SizedBox(
                      width: 5.0,
                    ),
                    Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(noticia.assunto!.toUpperCase(),
                              style: const TextStyle(
                                  color: CooperadoColors.tealGreen,
                                  fontWeight: FontWeight.bold)),
                          Padding(
                              padding: const EdgeInsets.only(top: 5, bottom: 5),
                              child: Text(
                                DateFormat("dd/MM/yyyy").format(noticia.data!),
                                style: const TextStyle(
                                    color: CooperadoColors.grayDark),
                              )),
                        ]),
                  ],
                ),
                const Divider(
                  height: 1,
                  color: CooperadoColors.tealGreen,
                )
              ],
            ),
          )));
    }

    return list;
  }
}

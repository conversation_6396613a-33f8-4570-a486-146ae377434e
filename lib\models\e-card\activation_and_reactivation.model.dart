class EcardActiveAndReativeCardModel {
  final String message;

  EcardActiveAndReativeCardModel({required this.message});

  factory EcardActiveAndReativeCardModel.fromJson(Map<String, dynamic> json) {
    return EcardActiveAndReativeCardModel(
      message: json['data']['eCardActiveCard']['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': {
        'eCardActiveCard': {
          'message': message,
        },
      },
    };
  }
}

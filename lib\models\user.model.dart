// ignore_for_file: non_constant_identifier_names

import 'dart:convert';

import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:crypto/crypto.dart';
import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user.model.g.dart';

@JsonSerializable(anyMap: true)
class UserCredentials {
  String crm;
  String? password;

  UserCredentials({required this.crm, this.password}) : assert(crm.isNotEmpty);

  factory UserCredentials.fromJson(Map json) => _$UserCredentialsFromJson(json);
  Map<String, dynamic> toJson() => _$UserCredentialsToJson(this);
}

class User {
  String? email;
  List<Especialidades>? especialidades;
  String? crm;
  int? cpf;
  String? dataNascimento;
  String? nome;
  String? codPrestador;
  String? FCMUserId;
  String? token;

  String get crmOnlyNumbers => crm!.split(' ')[0];

  User(
      {this.email,
      this.especialidades,
      this.crm,
      this.cpf,
      this.dataNascimento,
      this.nome,
      this.codPrestador,
      this.FCMUserId,
      this.token});

  static get empty => User(
      email: '',
      codPrestador: '',
      cpf: 00000000000,
      crm: '',
      dataNascimento: '',
      especialidades: [],
      nome: '',
      FCMUserId: '');

  User.fromJson(Map<String, dynamic> json) {
    email = json['email'];
    if (json['especialidades'] != null) {
      especialidades = [];
      json['especialidades'].forEach((v) {
        especialidades!.add(Especialidades.fromJson(v));
      });
    }
    crm = json['crm'];
    cpf = json['cpf'];
    dataNascimento = json['dataNascimento'];
    nome = json['nome'];
    codPrestador = json['codPrestador'];
    FCMUserId = json['FCMUserId'];
    token = json['token'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['email'] = email;
    if (especialidades != null) {
      data['especialidades'] = especialidades!.map((v) => v.toJson()).toList();
    }
    data['crm'] = crm;
    data['cpf'] = cpf;
    data['dataNascimento'] = dataNascimento;
    data['nome'] = nome;
    data['codPrestador'] = codPrestador;
    data['FCMUserId'] = FCMUserId;
    data['token'] = token;
    return data;
  }

  static Future<String> createToken(
      {required UserCredentials credentials}) async {
    if (isTokenInit()) {
      const token = String.fromEnvironment('token_forced', defaultValue: "");
      return token;
    } else {
      final today = DateFormat('dd/MM/yyyy').format(DateTime.now());
      const String clientId = String.fromEnvironment('CLIENT_ID');
      final password = clientId == 'UNIMED_FORTALEZA'
          ? credentials.password
          : sha256.convert(utf8.encode(credentials.password!)).toString();
      return md5
          .convert(utf8.encode('${credentials.crm}|$password|$today'))
          .toString();
    }
  }

  static bool isTokenInit() {
    if (FlavorConfig.isProduction()) {
      const token = String.fromEnvironment('token_forced', defaultValue: "");
      return token.isNotEmpty;
    } else {
      return false;
    }
  }
}

class Especialidades {
  int? codigo;
  String? descricao;
  String? qualificacoes;
  String? qualificacao;
  String? especialidadePrincipal;

  Especialidades(
      {this.codigo,
      this.descricao,
      this.qualificacoes,
      this.qualificacao,
      this.especialidadePrincipal});

  Especialidades.fromJson(Map<String, dynamic> json) {
    codigo = json['codigo'];
    descricao = json['descricao'];
    qualificacoes = json['qualificacoes'];
    qualificacao = json['qualificacao'];
    especialidadePrincipal = json['especialidadePrincipal'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codigo'] = codigo;
    data['descricao'] = descricao;
    data['qualificacoes'] = qualificacoes;
    data['qualificacao'] = qualificacao;
    data['especialidadePrincipal'] = especialidadePrincipal;
    return data;
  }
}

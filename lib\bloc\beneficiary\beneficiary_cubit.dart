import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/models/beneficiary_card.model.dart';
import 'package:cooperado_minha_unimed/shared/api/beneficiary.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';

part 'beneficiary_state.dart';

class BeneficiaryCubit extends Cubit<BeneficiaryState> {
  BeneficiaryCubit() : super(BeneficiaryInitial());

  late BeneficiaryCardModel _selectedCard;
  BeneficiaryCardModel get selectedCard => _selectedCard;

  List<BeneficiaryCardModel> _cards = [];
  List<BeneficiaryCardModel> get cards => _cards;

  getCardsData({
    required String cpf,
  }) async {
    emit(LoadingBeneficiaryState());
    try {
      _cards = await Locator.instance!<BeneficiaryApi>().getCadsData(cpf: cpf);

      if (_cards.isEmpty) {
        emit(const ErrorBeneficiaryState("Nenhum dado encontrado."));
      } else {
        _selectedCard = _cards.first;
        emit(LoadedBeneficiaryState(cards: _cards, selectCard: _selectedCard));
      }
    } catch (ex) {
      emit(ErrorBeneficiaryState('$ex'));
    }
  }

  selectCard({
    required BeneficiaryCardModel card,
  }) async {
    _selectedCard = card;
  }
}

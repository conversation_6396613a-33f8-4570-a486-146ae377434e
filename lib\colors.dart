import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

const MaterialColor cooperadoTealGreen = MaterialColor(
  0xFF1A6D71,
  <int, Color>{
    50: Color(0xFFe0f5f6),
    100: Color(0xFFb3e4e6),
    200: Color(0xFF80d3d9),
    300: Color(0xFF4dc2cc),
    400: Color(0xFF26b3bf),
    500: Color(0xFF1a6d71), // A cor original
    600: Color(0xFF00828a),
    700: Color(0xFF006c7a),
    800: Color(0xFF005665),
    900: Color(0xFF003a4d),
  },
);

const MaterialColor cooperadoTealGreenDark = MaterialColor(
  0xFF08484C,
  <int, Color>{
    50: Color(0xFFe1e9eb),
    100: Color(0xFFb4cdd2),
    200: Color(0xFF87b2b8),
    300: Color(0xFF59969e),
    400: Color(0xFF3b8890),
    500: Color(0xFF1f7a82),
    600: Color(0xFF1b6f78),
    700: Color(0xFF17646e),
    800: Color(0xFF135964),
    900: Color(0xFF0d4752),
  },
);

const MaterialColor cooperadoPurple = MaterialColor(
  0xFF411564,
  <int, Color>{
    50: Color(0xFFece8f0),
    100: Color(0xFFd9d0e0),
    200: Color(0xFFc6b9d1),
    300: Color(0xFFb3a1c1),
    400: Color(0xFFa08ab2),
    500: Color(0xFF8d73a2),
    600: Color(0xFF7a5b93),
    700: Color(0xFF674483),
    800: Color(0xFF542c74),
    900: Color(0xFF411564),
  },
);

const MaterialColor unimedGreen = MaterialColor(
  0xFF00995D,
  <int, Color>{
    50: Color(0xFFe0f3ec),
    100: Color(0xFFb3e0ce),
    200: Color(0xFF80ccae),
    300: Color(0xFF4db88e),
    400: Color(0xFF26a875),
    500: Color(0xFF00995d),
    600: Color(0xFF009155),
    700: Color(0xFF00864b),
    800: Color(0xFF007c41),
    900: Color(0xFF006b30),
  },
);

const MaterialColor unimedOrange = MaterialColor(
  0xFFF47920,
  <int, Color>{
    50: Color(0xFFfeefe4),
    100: Color(0xFFfcd7bc),
    200: Color(0xFFfabc90),
    300: Color(0xFFf7a163),
    400: Color(0xFFf68d41),
    500: Color(0xFFf47920),
    600: Color(0xFFf3711c),
    700: Color(0xFFf16618),
    800: Color(0xFFef5c13),
    900: Color(0xFFec490b),
  },
);

class CooperadoColors {
  static const Color backgroundColor = Color(0xFFF5F5F5);
  static const Color backgroundWhiteColor = Color(0xFFF3F3F3);
  static const Color blackText = Color(0xFF242425);
  static const Color indicatorsText = Color(0xFF627821);
  static const Color gray = Color.fromRGBO(244, 244, 244, 1);
  static const Color grayLight = Color.fromRGBO(238, 238, 238, 1);
  static const Color grayLight2 = Color.fromRGBO(91, 92, 101, 1);
  static const Color grayLight3 = Color.fromRGBO(229, 229, 229, 1);
  static const Color grayLight4 = Color.fromRGBO(250, 250, 250, 1);
  static const Color grayLight5 = Color.fromRGBO(180, 181, 190, 1);
  static const Color grayLight6 = Color.fromRGBO(80, 80, 80, 1);
  static const Color grayLight7 = Color.fromRGBO(144, 144, 144, 1);
  static const Color grayLight8 = Color.fromRGBO(248, 248, 248, 1);
  static const Color grayDark = Color.fromRGBO(112, 112, 112, 1);
  static const Color grayDark2 = Color.fromRGBO(77, 77, 77, 1);
  static const Color grayDark3 = Color.fromRGBO(31, 31, 31, 1);
  static const Color opcionalGray4 = Color.fromRGBO(213, 213, 213, 1);
  static const Color opcionalGray3 = Color.fromRGBO(144, 144, 144, 1);
  static const Color green = Color.fromRGBO(0, 153, 93, 1);
  static const Color green2 = Color.fromRGBO(0, 145, 85, 1);
  static const Color green3 = Color.fromRGBO(10, 95, 85, 0.08);
  static const Color greenLight = Color.fromRGBO(190, 215, 0, 1);
  static const Color greenLight2 = Color.fromRGBO(163, 212, 58, 0.3);
  static const Color greenLight3 = Color.fromRGBO(196, 206, 207, 1);
  static const Color greenLight4 = Color.fromRGBO(184, 207, 88, 1);
  static const Color greenDark5 = Color.fromRGBO(8, 71, 76, 0.24);
  static const Color greenDark = Color.fromRGBO(10, 95, 85, 1);
  static const Color greenDark2 = Color.fromRGBO(8, 71, 76, 1);
  static const Color orange = Color.fromRGBO(244, 121, 32, 1);
  static const Color orange2 = Color.fromRGBO(255, 150, 75, 1);

  static const Color greenWhite = Color.fromRGBO(15, 151, 162, 0.02);

  static const Color tealGreen = Color(0xFF1A6D71);
  static const Color tealGreenDark = Color(0xFF08484C);
  static const Color purple = Color.fromRGBO(65, 21, 100, 1);
  static const Color tealGreenSecondary = Color(0xFF1A6D71);
  static const Color purpleSecondary = Color.fromRGBO(163, 35, 142, 1);
  static const Color yellowLight = Color.fromRGBO(218, 228, 142, 1);
  static const Color greenAsccentChart = Color.fromRGBO(190, 215, 0, 1);
  static const Color pinkChart = Color.fromRGBO(237, 22, 81, 1);
  static const Color greenChart = Color.fromRGBO(0, 133, 81, 1);
  static const Color arrowExpasionTile = Color.fromRGBO(255, 255, 255, 1);
  static const Color redCancel = Color(0xFFCA203C);
  static const Color lightOrange = Color(0xFFFFE7D6);
  static const Color lightOrangeText = Color(0xFFB2530F);
  static const Color darkOrangeText = Color(0xFFB2530F);

  //Evaluation Colors
  static const Color darkRed = Color.fromRGBO(151, 41, 49, 1);
  static const Color darkPink = Color.fromRGBO(202, 32, 60, 1);
  static const Color darkOrange = Color.fromRGBO(196, 97, 26, 1);
  static const Color yellow = Color.fromRGBO(255, 203, 11, 1);
  static const Color greenLight5 = Color.fromRGBO(1, 134, 82, 1);

  static const Color totorialBackground = Color.fromRGBO(0, 0, 0, .75);

  //Chart Colors
  static const Color paleGreenish = Color(0xFFDCF09F);
  static const Color limaColor = Color(0xFFB0D14B);
  static const Color limaColorDark = Color(0xFF8FAD32);
  static const Color peachColor = Color(0xFFF9B27F);
  static const Color rustyOrangeColor = Color(0xFFDD640E);
  static const Color lightSeaGreen = Color(0xFF93D7CF);
  static const Color aliceBlue = Color(0xFFE9F9FF);
  static const Color aliceBlueLight = Color.fromARGB(255, 243, 252, 255);

  static const chartColors = [
    Color.fromRGBO(34, 168, 168, 1.0),
    Color.fromRGBO(213, 213, 213, 1.0),
    Color.fromRGBO(115, 175, 198, 1.0),
    Color.fromRGBO(56, 66, 81, 1.0),
    Color.fromRGBO(251, 163, 167, 1.0),
    Color.fromRGBO(123, 145, 162, 1.0),
    Color.fromRGBO(0, 64, 26, 1.0),
    Color(0xFF8FAD32),
    Color(0xFFDD640E),
    Color(0xFFDCF09F),
    Color.fromRGBO(65, 21, 100, 1.0),
    Color.fromRGBO(163, 35, 142, 1.0),
  ];
}

class ThemeWidgets {
  static ThemeData expansionTile(BuildContext context,
      {Color colorArrow = CooperadoColors.arrowExpasionTile}) {
    return Theme.of(context).copyWith(
        unselectedWidgetColor: colorArrow,
        colorScheme: ColorScheme.fromSwatch().copyWith(primary: colorArrow));
  }
}

class ThemeCooperado {
  static ThemeData purple() {
    return ThemeData(
      useMaterial3: false,
      primarySwatch: cooperadoTealGreen,
      textSelectionTheme: const TextSelectionThemeData(
        cursorColor: CooperadoColors.green,
      ),
      appBarTheme: const AppBarTheme(
        systemOverlayStyle: SystemUiOverlayStyle.light,
        backgroundColor: CooperadoColors.tealGreenDark,
        titleTextStyle: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.w500,
        ),
        iconTheme: IconThemeData(color: Colors.white),
      ),
      buttonTheme: const ButtonThemeData(
        minWidth: 10,
        buttonColor: cooperadoTealGreen,
        textTheme: ButtonTextTheme.primary,
      ),
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return CooperadoColors.tealGreen;
          }
          return Colors.transparent;
        }),
        checkColor: WidgetStateProperty.all<Color>(Colors.white),
      ),
      dialogTheme: const DialogTheme(
        backgroundColor: Colors.white,
        shape: BeveledRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(5)),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: cooperadoTealGreen, // Button color
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(5.0)), // Text color
        ),
      ),
      fontFamily: 'UnimedSans',
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          color: CooperadoColors.grayDark2,
        ),
      ),
    );
  }

  static ThemeData purpleWithSelector() {
    return purple().copyWith(
      textSelectionTheme: const TextSelectionThemeData(
        cursorColor: unimedGreen,
        selectionColor: unimedGreen,
        selectionHandleColor: unimedGreen,
      ),
    );
  }
}

part of 'news_cubit.dart';

abstract class NewsState extends Equatable {
  const NewsState();

  @override
  List<Object?> get props => [];
}

class NewsInitial extends NewsState {}

class LoadingGetListNewsState extends NewsState {
  @override
  List<Object> get props => [];
}

class LoadingGetListNewsStatePagination extends NewsState {
  final List<Noticia> list;
  @override
  List<Object> get props => [list];
  const LoadingGetListNewsStatePagination({required this.list});
}

class ErrorGetListNewsState extends NewsState {
  final String message;
  @override
  List<Object> get props => [message];
  const ErrorGetListNewsState(this.message);
}

class DoneGetListNewsState extends NewsState {
  final List<Noticia> list;
  @override
  List<Object> get props => [list];

  const DoneGetListNewsState({required this.list});
}

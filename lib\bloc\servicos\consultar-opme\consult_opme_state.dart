import 'package:cooperado_minha_unimed/shared/vo/servicos/opme_list.vo.dart';
import 'package:equatable/equatable.dart';

abstract class ConsultOpmeState extends Equatable {
  const ConsultOpmeState();
}

class ConsultOpmeInitial extends ConsultOpmeState {
  @override
  List<Object> get props => [];
}

class LoadingGetConsultOpmeState extends ConsultOpmeState {
  @override
  List<Object> get props => [];
}

class ErrorGetConsultOpmeState extends ConsultOpmeState {
  final String message;
  @override
  List<Object> get props => [message];
  const ErrorGetConsultOpmeState(this.message);
}

class DoneGetConsultOpmeState extends ConsultOpmeState {
  final OpmeList opmeList;
  @override
  List<Object> get props => [opmeList];

  const DoneGetConsultOpmeState(this.opmeList);
}
